package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 结算电量聚合数据DTO
 * 用于首页概览的结算电量汇总显示
 */
@Data
@NoArgsConstructor
@ApiModel("结算电量聚合数据DTO")
public class SettlementAggregationDTO {

    @ApiModelProperty(value = "结算电量(MWh)", example = "12345.67")
    private Double settlementPower;

    @ApiModelProperty(value = "结算电量单位", example = "MWh")
    private String settlementPowerUnit = "MWh";

    @ApiModelProperty(value = "结算均价(元/MWh)", example = "420.56")
    private Double settlementAvgPrice;

    @ApiModelProperty(value = "结算均价单位", example = "元/MWh")
    private String settlementAvgPriceUnit = "元/MWh";

    @ApiModelProperty(value = "标杆电价(元/MWh)", example = "350.78")
    private Double coalBenchmarkPrice;

    @ApiModelProperty(value = "标杆电价单位", example = "元/MWh")
    private String coalBenchmarkPriceUnit = "元/MWh";

    @ApiModelProperty(value = "请求中包含的电站总数", example = "5")
    private Integer totalStations;

    @ApiModelProperty(value = "总结算电费(元)", example = "1234567.89")
    private Double totalSettlementFee;

    @ApiModelProperty(value = "总结算电费单位", example = "元")
    private String totalSettlementFeeUnit = "元";

    @ApiModelProperty(value = "数据记录数", example = "120")
    private Integer recordCount;

    @ApiModelProperty(value = "查询开始日期", example = "2024-01-01")
    private String startDate;

    @ApiModelProperty(value = "查询结束日期", example = "2024-12-31")
    private String endDate;

    @ApiModelProperty(value = "实际上网电量(MWh)", example = "12000.00")
    private Double totalActualElectricity;

    @ApiModelProperty(value = "合同电量(MWh)", example = "12500.00")
    private Double totalContractElectricity;

    @ApiModelProperty(value = "偏差电量(MWh)", example = "500.00")
    private Double totalDeviationElectricity;

    /**
     * 全参数构造函数
     */
    public SettlementAggregationDTO(Double settlementPower, Double settlementAvgPrice, 
                                   Double coalBenchmarkPrice, Integer totalStations) {
        this.settlementPower = settlementPower;
        this.settlementAvgPrice = settlementAvgPrice;
        this.coalBenchmarkPrice = coalBenchmarkPrice;
        this.totalStations = totalStations;
    }

    /**
     * 扩展构造函数
     */
    public SettlementAggregationDTO(Double settlementPower, Double settlementAvgPrice, 
                                   Double totalSettlementFee, Integer totalStations,
                                   Integer recordCount, String startDate, String endDate) {
        this.settlementPower = settlementPower;
        this.settlementAvgPrice = settlementAvgPrice;
        this.totalSettlementFee = totalSettlementFee;
        this.totalStations = totalStations;
        this.recordCount = recordCount;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    /**
     * 获取结算电量（GWh单位）
     */
    public Double getSettlementPowerInGWh() {
        return settlementPower != null ? settlementPower / 1000.0 : 0.0;
    }

    /**
     * 获取总结算电费（万元单位）
     */
    public Double getTotalSettlementFeeInWanYuan() {
        return totalSettlementFee != null ? totalSettlementFee / 10000.0 : 0.0;
    }

    /**
     * 计算容量因子（如果有装机容量信息）
     */
    public Double calculateCapacityFactor(Double installedCapacity, Integer hours) {
        if (installedCapacity == null || installedCapacity <= 0 || 
            hours == null || hours <= 0 || 
            settlementPower == null || settlementPower <= 0) {
            return 0.0;
        }
        
        double theoreticalGeneration = installedCapacity * hours;
        return (settlementPower / theoreticalGeneration) * 100; // 百分比
    }

    /**
     * 检查数据完整性
     */
    public boolean isDataComplete() {
        return settlementPower != null && settlementPower > 0 &&
               settlementAvgPrice != null && settlementAvgPrice > 0 &&
               totalStations != null && totalStations > 0;
    }

    /**
     * 获取数据质量评分
     */
    public String getDataQualityScore() {
        int score = 0;
        
        if (settlementPower != null && settlementPower > 0) score += 20;
        if (settlementAvgPrice != null && settlementAvgPrice > 0) score += 20;
        if (totalSettlementFee != null && totalSettlementFee > 0) score += 20;
        if (totalStations != null && totalStations > 0) score += 20;
        if (recordCount != null && recordCount > 0) score += 20;
        
        if (score >= 90) return "优秀";
        else if (score >= 70) return "良好";
        else if (score >= 50) return "一般";
        else return "较差";
    }

    /**
     * 转换为显示用的Map
     */
    public java.util.Map<String, Object> toDisplayMap() {
        java.util.Map<String, Object> map = new java.util.HashMap<>();
        
        map.put("settlementPower", settlementPower);
        map.put("settlementPowerUnit", settlementPowerUnit);
        map.put("settlementAvgPrice", settlementAvgPrice);
        map.put("settlementAvgPriceUnit", settlementAvgPriceUnit);
        map.put("totalSettlementFee", totalSettlementFee);
        map.put("totalSettlementFeeUnit", totalSettlementFeeUnit);
        map.put("totalStations", totalStations);
        map.put("recordCount", recordCount);
        map.put("dataQuality", getDataQualityScore());
        
        return map;
    }

    @Override
    public String toString() {
        return String.format("SettlementAggregationDTO{settlementPower=%.2f %s, avgPrice=%.2f %s, " +
                           "totalFee=%.2f %s, stations=%d, records=%d, quality=%s}",
                           settlementPower, settlementPowerUnit,
                           settlementAvgPrice, settlementAvgPriceUnit,
                           totalSettlementFee, totalSettlementFeeUnit,
                           totalStations, recordCount, getDataQualityScore());
    }
}
