package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("screen_trade_settlement")
public class ScreenTradeSettlement {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @TableField("station_id")
    @ApiModelProperty(value = "电站ID", example = "1001")
    private Long stationId;

    @TableField("year")
    @ApiModelProperty(value = "年份", example = "2023")
    private String year;

    @TableField("month")
    @ApiModelProperty(value = "月份", example = "01")
    private String month;

    @TableField("settle_power")
    @ApiModelProperty(value = "结算电量(单位:万千瓦时)", example = "100.00")
    private BigDecimal settlePower;

    @TableField("settle_electricity_fee")
    @ApiModelProperty(value = "结算电费(单位:万元)", example = "50.00")
    private BigDecimal settleElectricityFee;

    @TableField("assessment_fee")
    @ApiModelProperty(value = "双细则考核电费(单位:万元)", example = "5.00")
    private BigDecimal assessmentFee;

    @TableField("settlement_average_price")
    @ApiModelProperty(value = "结算均价(单位:元/千瓦时)", example = "0.5000")
    private BigDecimal settlementAveragePrice;

    @TableField("bench_mark_electricity_price")
    @ApiModelProperty(value = "标杆电价(单位:元/千瓦时)", example = "0.4000")
    private BigDecimal benchMarkElectricityPrice;

    @TableField("limited_power")
    @ApiModelProperty(value = "限电量(单位:万千瓦时)", example = "20.00")
    private BigDecimal limitedPower;

    @TableField("current_month_power")
    @ApiModelProperty(value = "当月发电量(单位:万千瓦时)", example = "120.00")
    private BigDecimal currentMonthPower;

    @TableField("current_month_plan_power")
    @ApiModelProperty(value = "当月计划发电量(单位:万千瓦时)", example = "150.00")
    private BigDecimal currentMonthPlanPower;
}