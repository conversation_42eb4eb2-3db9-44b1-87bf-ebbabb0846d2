package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 储能日清数据重构后的DTO（V2版本）
 * 将扁平化结构重构为分类结构，便于前端处理
 */
@Data
public class EnergyStorageDailyCleanV2DTO {

    @ApiModelProperty(value = "主键ID", example = "null")
    private Long id;

    @ApiModelProperty(value = "日期", example = "2025-04-01 00:00:00")
    private Date date;

    @ApiModelProperty(value = "电站ID", example = "null")
    private Long stationId;

    @ApiModelProperty(value = "用户侧数据（购电侧）")
    private UserSideDataDTO userSideData;

    @ApiModelProperty(value = "发电侧数据（售电侧）")
    private PowerGenerationSideDataDTO powerGenerationSideData;

    @ApiModelProperty(value = "创建时间", example = "null")
    private Date createTime;

    @ApiModelProperty(value = "更新时间", example = "null")
    private Date updateTime;

    @ApiModelProperty(value = "创建人", example = "null")
    private String createBy;

    @ApiModelProperty(value = "更新人", example = "null")
    private String updateBy;

    @ApiModelProperty(value = "省份ID", example = "null")
    private Integer provinceId;

    @ApiModelProperty(value = "交易日期", example = "null")
    private String tradeDate;
}
