package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 政策文件实体
 */
@Data
@TableName("policy_file")
@ApiModel(value = "PolicyFile", description = "政策文件实体")
public class PolicyFile {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @TableField("file_url")
    @ApiModelProperty(value = "政策文件URL", example = "http://example.com/policy.pdf")
    private String fileUrl;

    @TableField("file_name")
    @ApiModelProperty(value = "政策文件名", example = "政策文件名.pdf")
    private String fileName;
}
