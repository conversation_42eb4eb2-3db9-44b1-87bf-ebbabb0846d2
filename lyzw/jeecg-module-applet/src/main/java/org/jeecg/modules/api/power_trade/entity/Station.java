package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("bi.station")
public class Station {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @TableField("name")
    @ApiModelProperty(value = "电站名称", example = "XX光伏电站")
    private String name;

    @TableField("dispatch_name")
    @ApiModelProperty(value = "调度名称", example = "XX调度中心")
    private String dispatchName;

    @TableField("province_id")
    @ApiModelProperty(value = "省份ID", example = "11")
    private Integer provinceId;

    @TableField("station_no")
    @ApiModelProperty(value = "沈阳嘉越提供的场站编号", example = "JY20230001")
    private String stationNo;

    // 数据库表有capacity字段(decimal(10,2))，但实体类中是Double类型
    @TableField("capacity")
    @ApiModelProperty(value = "装机容量(单位:万千瓦)", example = "100.00")
    private Double capacity;

    @TableField("type")
    @ApiModelProperty(value = "电站类型(0:光伏,1:风电)", example = "0")
    private Integer type;

    // 以下字段在数据库表中不存在，是实体类新增的
    @TableField("trade_status")
    @ApiModelProperty(value = "交易状态", example = "1")
    private Integer tradeStatus;

    @TableField("power_status")
    @ApiModelProperty(value = "电源状态", example = "1")
    private Integer powerStatus;

    @TableField("day_ahead_declaration_status")
    @ApiModelProperty(value = "日前申报状态", example = "1")
    private Integer dayAheadDeclarationStatus;

    // 以下字段在数据库表中不存在，是实体类新增的
    @TableField("longitude")
    @ApiModelProperty(value = "经度", example = "120.123456")
    private Double longitude;

    @TableField("latitude")
    @ApiModelProperty(value = "纬度", example = "30.123456")
    private Double latitude;

    // 数据库表有capacity字段，但实体类中又有一个power字段(Double类型)
    @TableField("power")
    @ApiModelProperty(value = "功率(单位:兆瓦)", example = "50.0")
    private Double power;

    @TableField("sort_no")
    @ApiModelProperty(value = "排序序号", example = "1")
    private Long sortNo;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    private Date createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间", example = "2023-01-01 12:00:00")
    private Date updateTime;

    @TableField("create_by")
    @ApiModelProperty(value = "创建人", example = "admin")
    private String createBy;

    @TableField("update_by")
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updateBy;
}