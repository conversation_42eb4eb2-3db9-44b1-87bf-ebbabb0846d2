package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("trade_calendar_record")
public class TradeCalendarRecord {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @TableField("type_id")
    @ApiModelProperty(value = "关联交易类型ID", example = "1")
    private Integer typeId;

    @TableField("trade_date")
    @ApiModelProperty(value = "交易日期", example = "2023-01-01")
    private Date tradeDate;

    @TableField("start_time")
    @ApiModelProperty(value = "开始时间(格式:HH:mm)", example = "09:00")
    private String startTime;

    @TableField("end_time")
    @ApiModelProperty(value = "结束时间(格式:HH:mm)", example = "17:00")
    private String endTime;

    @TableField("remark")
    @ApiModelProperty(value = "备注", example = "交易日历备注信息")
    private String remark;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    private Date createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间", example = "2023-01-01 12:00:00")
    private Date updateTime;

    @TableField("create_by")
    @ApiModelProperty(value = "创建人", example = "admin")
    private String createBy;

    @TableField("update_by")
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updateBy;

    @TableField(exist = false)
    @ApiModelProperty(value = "交易类型名称", example = "日前交易")
    private String tradeType;

    @TableField(exist = false)
    @ApiModelProperty(value = "省份ID", example = "2")
    private Integer provinceId;

    @TableField(exist = false)
    @ApiModelProperty(value = "交易附件列表")
    private List<TradeDiaryFile> files;
}