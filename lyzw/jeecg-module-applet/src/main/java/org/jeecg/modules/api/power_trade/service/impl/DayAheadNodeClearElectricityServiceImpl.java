package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.power_trade.entity.DayAheadNodeClearElectricity;
import org.jeecg.modules.api.power_trade.mapper.DayAheadNodeClearElectricityMapper;
import org.jeecg.modules.api.power_trade.service.DayAheadNodeClearElectricityService;
import org.springframework.stereotype.Service;

@Service
public class DayAheadNodeClearElectricityServiceImpl extends ServiceImpl<DayAheadNodeClearElectricityMapper, DayAheadNodeClearElectricity> implements DayAheadNodeClearElectricityService {

}