<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.DayAheadClearPowerMapper">

    <!-- 按日查询出清电量明细 -->
    <select id="selectByDay" resultType="org.jeecg.modules.api.power_trade.entity.DayAheadClearPower">
        SELECT * FROM day_ahead_clear_power 
        WHERE station_id = #{stationId} 
        AND DATE(date) = #{date} 
        ORDER BY time
    </select>

    <!-- 按月汇总查询出清电量 -->
    <select id="selectMonthlySum" resultType="java.util.Map">
        SELECT DATE(date) as date, SUM(IFNULL(value, 0)) as totalValue
        FROM day_ahead_clear_power
        WHERE station_id = #{stationId} 
        AND DATE_FORMAT(date, '%Y-%m') = #{yearMonth}
        GROUP BY DATE(date)
        ORDER BY date
    </select>

    <!-- 按年汇总查询出清电量 -->
    <select id="selectYearlySum" resultType="java.util.Map">
        SELECT DATE_FORMAT(date, '%Y-%m') as month, SUM(IFNULL(value, 0)) as totalValue
        FROM day_ahead_clear_power
        WHERE station_id = #{stationId} 
        AND YEAR(date) = #{year}
        GROUP BY YEAR(date), MONTH(date)
        ORDER BY month
    </select>

</mapper>