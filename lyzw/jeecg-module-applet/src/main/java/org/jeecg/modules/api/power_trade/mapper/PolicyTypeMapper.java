package org.jeecg.modules.api.power_trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.power_trade.entity.PolicyType;

import java.util.List;

/**
 * 政策类型Mapper接口
 */
@Mapper
public interface PolicyTypeMapper extends BaseMapper<PolicyType> {

    /**
     * 查询所有启用的政策类型
     * 
     * @return 政策类型列表
     */
    List<PolicyType> selectEnabledTypes();

    /**
     * 根据编码查询类型
     * 
     * @param typeCode 类型编码
     * @return 政策类型
     */
    PolicyType selectByCode(@Param("typeCode") String typeCode);

    /**
     * 获取所有政策类型（按排序字段排序）
     * 
     * @return 政策类型列表
     */
    List<PolicyType> selectAllOrderBySortOrder();

    /**
     * 更新政策类型状态
     * 
     * @param id 类型ID
     * @param status 状态
     * @return 更新行数
     */
    int updateStatus(@Param("id") Integer id, @Param("status") Integer status);

    /**
     * 更新政策类型排序
     * 
     * @param id 类型ID
     * @param sortOrder 排序值
     * @return 更新行数
     */
    int updateSortOrder(@Param("id") Integer id, @Param("sortOrder") Integer sortOrder);

    /**
     * 检查类型编码是否存在
     * 
     * @param typeCode 类型编码
     * @return 是否存在
     */
    boolean checkTypeCodeExists(@Param("typeCode") String typeCode);

    /**
     * 检查类型名称是否存在
     * 
     * @param typeName 类型名称
     * @return 是否存在
     */
    boolean checkTypeNameExists(@Param("typeName") String typeName);

    /**
     * 获取最大排序值
     * 
     * @return 最大排序值
     */
    Integer getMaxSortOrder();

    /**
     * 批量更新政策类型状态
     * 
     * @param ids 类型ID列表
     * @param status 状态
     * @return 更新行数
     */
    int batchUpdateStatus(@Param("ids") List<Integer> ids, @Param("status") Integer status);
}
