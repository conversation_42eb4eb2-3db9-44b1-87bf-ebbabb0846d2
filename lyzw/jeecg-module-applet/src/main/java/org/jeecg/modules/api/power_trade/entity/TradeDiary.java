package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("trade_diary")
public class TradeDiary {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @TableField("target_date")
    @ApiModelProperty(value = "标的日", example = "2023-01-01")
    private Date targetDate;

    @TableField("day_ahead_record")
    @ApiModelProperty(value = "日前申报记录", example = "日前申报详细记录内容...")
    private String dayAheadRecord;

    @TableField("rolling_record")
    @ApiModelProperty(value = "滚动撮合记录", example = "滚动撮合详细记录内容...")
    private String rollingRecord;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    private Date createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间", example = "2023-01-01 12:00:00")
    private Date updateTime;

    @TableField("create_by")
    @ApiModelProperty(value = "创建人", example = "admin")
    private String createBy;

    @TableField("update_by")
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updateBy;

    // 以下字段在数据库表中不存在，是实体类新增的
    @TableField(exist = false)
    @ApiModelProperty(value = "电站名称", example = "XX光伏电站")
    private String stationName;

    @TableField(exist = false)
    @ApiModelProperty(value = "省份ID", example = "11")
    private Integer provinceId;

    @TableField(exist = false)
    @ApiModelProperty(value = "省份名称", example = "辽宁省")
    private String provinceName;

    @TableField(exist = false)
    @ApiModelProperty(value = "交易日志文件列表")
    private List<TradeDiaryFile> files;
}