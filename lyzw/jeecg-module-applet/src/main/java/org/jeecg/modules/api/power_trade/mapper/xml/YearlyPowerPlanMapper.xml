<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.YearlyPowerPlanMapper">

    <!-- 获取电站年度发电计划统计 -->
    <select id="getStationYearlyPowerStats" resultType="java.util.Map">
        SELECT COALESCE(SUM(plan_value), 0)   as totalPlanValue,
               COALESCE(SUM(actual_value), 0) as totalActualValue,
               CASE
                   WHEN SUM(plan_value) > 0 THEN ROUND((SUM(actual_value) / SUM(plan_value)) * 100, 2)
                   ELSE 0
                   END                        as completionRate,
               COUNT(DISTINCT month)          as monthCount
        FROM yearly_power_plan
        WHERE station_id = #{stationId}
                  AND year = #{year}
    </select>

    <!-- 获取电站详情（包含基础信息和年度发电统计） -->
    <select id="getStationDetailWithPowerPlan" resultType="org.jeecg.modules.api.power_trade.dto.StationDetailDTO">
        SELECT
            s.id as stationId,
            s.name as stationName,
            s.type as stationType,
            s.capacity as stationCapacity,
            COALESCE(SUM(ypp.plan_value), 0) as yearlyPlanValue,
            COALESCE(SUM(ypp.actual_value), 0) as yearlyActualValue,
            CASE
                WHEN SUM(ypp.plan_value) > 0 THEN ROUND((SUM(ypp.actual_value) / SUM(ypp.plan_value)) * 100, 2)
                ELSE 0
                END as completionRate,
            COUNT(DISTINCT ypp.month) as monthCount,
            #{year} as queryYear
        FROM bi.station s
                 LEFT JOIN yearly_power_plan ypp ON s.id = ypp.station_id AND ypp.year = #{year}
        WHERE s.id = #{stationId}
        GROUP BY s.id, s.name, s.type, s.capacity
    </select>

    <!-- 获取电站月度发电计划明细 -->
    <select id="getStationMonthlyPowerPlan" resultType="org.jeecg.modules.api.power_trade.entity.YearlyPowerPlan">
        SELECT
            id,
            station_id as stationId,
            year,
            month,
            plan_value as planValue,
            actual_value as actualValue,
            create_time as createTime,
            update_time as updateTime
        FROM yearly_power_plan
        WHERE station_id = #{stationId}
          AND year = #{year}
        ORDER BY CAST(month AS UNSIGNED)
    </select>

</mapper>