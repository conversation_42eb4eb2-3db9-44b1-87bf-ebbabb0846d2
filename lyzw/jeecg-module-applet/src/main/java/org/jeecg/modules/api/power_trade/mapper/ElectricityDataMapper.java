package org.jeecg.modules.api.power_trade.mapper;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.power_trade.dto.ElectricityDataDTO;

import java.util.List;
import java.util.Map;

public interface ElectricityDataMapper {

    /**
     * 根据省份ID动态查询电量数据
     * @param provinceId 省份ID (1-安徽, 2-江苏)
     * @param stationId 电站ID
     * @param date 日期
     * @param dimension 维度 (day/month/year)
     * @return 电量数据列表
     */
    List<ElectricityDataDTO> selectElectricityDataByProvince(
            @Param("provinceId") Integer provinceId,
            @Param("stationId") Long stationId,
            @Param("date") String date,
            @Param("dimension") String dimension);

    /**
     * 按维度分组统计电量数据
     * @param provinceId 省份ID
     * @param stationId 电站ID
     * @param date 日期
     * @param dimension 维度
     * @return 统计结果
     */
    List<Map<String, Object>> selectElectricityDataGrouped(
            @Param("provinceId") Integer provinceId,
            @Param("stationId") Long stationId,
            @Param("date") String date,
            @Param("dimension") String dimension);
}