package org.jeecg.modules.api.power_trade.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 电站月度交易信息VO
 */
@Data
@ApiModel(value = "StationMonthlyTradeVO", description = "电站月度交易信息")
public class StationMonthlyTradeVO {

    @ApiModelProperty(value = "电站ID")
    private Long stationId;

    @ApiModelProperty(value = "月份 (yyyy-MM)")
    private String month;

    @ApiModelProperty(value = "区域代码")
    private Integer provinceId;

    @ApiModelProperty(value = "电站类型 (1-风电, 2-光伏, 3-储能)")
    private Integer stationType;

    // ========== 通用字段 ==========
    @ApiModelProperty(value = "结算电量(MWh)")
    private BigDecimal settlementPower;

    @ApiModelProperty(value = "结算均价(元/MWh)")
    private BigDecimal settlementAvgPrice;

    @ApiModelProperty(value = "结算电费(元)")
    private BigDecimal settlementFee;

    // ========== 安徽光伏风电字段 ==========
    @ApiModelProperty(value = "实际上网电量(MWh) - 安徽光伏风电")
    private BigDecimal actualGridPower;

    @ApiModelProperty(value = "合同电量(MWh) - 安徽光伏风电")
    private BigDecimal contractPower;

    @ApiModelProperty(value = "偏差电量(MWh) - 安徽光伏风电")
    private BigDecimal deviationPower;

    // ========== 安徽储能字段 ==========
    @ApiModelProperty(value = "期间类型 (purchase-购电侧, sale-售电侧) - 安徽储能")
    private String periodType;

    @ApiModelProperty(value = "实际用电量(MWh) - 安徽储能")
    private BigDecimal actualPowerConsumption;

    @ApiModelProperty(value = "偏差电费(元) - 安徽储能")
    private BigDecimal deviationFee;

    // ========== 江苏储能字段 ==========
    @ApiModelProperty(value = "实际上网电量(MWh) - 江苏储能")
    private BigDecimal actualGridPowerJs;

    @ApiModelProperty(value = "合同电量(MWh) - 江苏储能")
    private BigDecimal contractPowerJs;

    @ApiModelProperty(value = "偏差电费(元) - 江苏储能")
    private BigDecimal deviationFeeJs;

    @ApiModelProperty(value = "是否为开发中状态")
    private Boolean isDevelopment;

    @ApiModelProperty(value = "开发状态消息")
    private String developmentMessage;

}
