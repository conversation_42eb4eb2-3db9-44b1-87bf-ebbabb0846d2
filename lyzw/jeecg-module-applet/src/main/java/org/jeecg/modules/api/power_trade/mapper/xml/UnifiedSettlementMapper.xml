<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.UnifiedSettlementMapper">

    <!-- 获取区域结算电量汇总 - 用于首页概览 -->
    <select id="getRegionSettlementSummary" resultType="java.util.Map">
        SELECT 
            COALESCE(SUM(pss.settlement_electricity), 0) as totalSettlementPower,
            COALESCE(SUM(pss.settlement_electric_fee), 0) as totalSettlementFee,
            CASE 
                WHEN SUM(pss.settlement_electricity) > 0 
                THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
                ELSE 0 
            END as avgSettlementPrice,
            COUNT(DISTINCT fsr.station_id) as stationCount,
            COUNT(*) as recordCount,
            MIN(fsr.settle_date) as startDate,
            MAX(fsr.settle_date) as endDate,
            COALESCE(SUM(pss.actual_internet_electricity), 0) as totalActualElectricity,
            COALESCE(SUM(pss.contract_electricity), 0) as totalContractElectricity,
            COALESCE(SUM(pss.deviation_electricity), 0) as totalDeviationElectricity
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id IN 
        <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
            #{stationId}
        </foreach>
        <if test="startDate != null and endDate != null">
            AND fsr.settle_date BETWEEN #{startDate} AND #{endDate}
        </if>
    </select>

    <!-- 获取电站结算电量明细 - 用于交易概况 -->
    <select id="getStationSettlementDetails" resultType="java.util.Map">
        SELECT 
            fsr.station_id,
            s.name as stationName,
            s.type as stationType,
            s.capacity as stationCapacity,
            COALESCE(SUM(pss.settlement_electricity), 0) as totalSettlementPower,
            COALESCE(SUM(pss.settlement_electric_fee), 0) as totalSettlementFee,
            CASE 
                WHEN SUM(pss.settlement_electricity) > 0 
                THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
                ELSE 0 
            END as avgSettlementPrice,
            COALESCE(SUM(pss.actual_internet_electricity), 0) as totalActualElectricity,
            COALESCE(SUM(pss.contract_electricity), 0) as totalContractElectricity,
            COALESCE(SUM(pss.deviation_electricity), 0) as totalDeviationElectricity,
            COUNT(*) as recordCount,
            MIN(fsr.settle_date) as firstSettleDate,
            MAX(fsr.settle_date) as lastSettleDate
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        INNER JOIN station s ON fsr.station_id = s.id
        WHERE fsr.station_id IN 
        <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
            #{stationId}
        </foreach>
        <if test="startDate != null and endDate != null">
            AND fsr.settle_date BETWEEN #{startDate} AND #{endDate}
        </if>
        GROUP BY fsr.station_id, s.name, s.type, s.capacity
        ORDER BY fsr.station_id
    </select>

    <!-- 获取电站结算电量时间序列 -->
    <select id="getStationSettlementTimeSeries" resultType="java.util.Map">
        SELECT 
            <choose>
                <when test="groupBy == 'day'">
                    DATE(fsr.settle_date) as timeKey,
                    DATE(fsr.settle_date) as settleDate
                </when>
                <when test="groupBy == 'month'">
                    DATE_FORMAT(fsr.settle_date, '%Y-%m') as timeKey,
                    DATE_FORMAT(fsr.settle_date, '%Y-%m-01') as settleDate
                </when>
                <when test="groupBy == 'year'">
                    YEAR(fsr.settle_date) as timeKey,
                    CONCAT(YEAR(fsr.settle_date), '-01-01') as settleDate
                </when>
                <otherwise>
                    DATE(fsr.settle_date) as timeKey,
                    DATE(fsr.settle_date) as settleDate
                </otherwise>
            </choose>,
            COALESCE(SUM(pss.settlement_electricity), 0) as settlementPower,
            COALESCE(SUM(pss.settlement_electric_fee), 0) as settlementFee,
            CASE 
                WHEN SUM(pss.settlement_electricity) > 0 
                THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
                ELSE 0 
            END as avgPrice,
            COUNT(*) as recordCount
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id = #{stationId}
        <if test="startDate != null and endDate != null">
            AND fsr.settle_date BETWEEN #{startDate} AND #{endDate}
        </if>
        GROUP BY timeKey
        ORDER BY timeKey
    </select>

    <!-- 获取省份结算电量汇总 -->
    <select id="getProvinceSettlementSummary" resultType="java.util.Map">
        SELECT 
            #{provinceId} as provinceId,
            COALESCE(SUM(pss.settlement_electricity), 0) as totalSettlementPower,
            COALESCE(SUM(pss.settlement_electric_fee), 0) as totalSettlementFee,
            CASE 
                WHEN SUM(pss.settlement_electricity) > 0 
                THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
                ELSE 0 
            END as avgSettlementPrice,
            COUNT(DISTINCT fsr.station_id) as stationCount,
            COUNT(*) as recordCount
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        INNER JOIN station s ON fsr.station_id = s.id
        WHERE s.province_id = #{provinceId}
        <if test="startDate != null and endDate != null">
            AND fsr.settle_date BETWEEN #{startDate} AND #{endDate}
        </if>
    </select>

    <!-- 获取电站月度结算数据 -->
    <select id="getStationMonthlySettlement" resultType="java.util.Map">
        SELECT 
            #{stationId} as stationId,
            #{year} as year,
            #{month} as month,
            COALESCE(SUM(pss.settlement_electricity), 0) as monthlySettlementPower,
            COALESCE(SUM(pss.settlement_electric_fee), 0) as monthlySettlementFee,
            CASE 
                WHEN SUM(pss.settlement_electricity) > 0 
                THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
                ELSE 0 
            END as monthlyAvgPrice,
            COUNT(*) as recordCount
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id = #{stationId}
          AND YEAR(fsr.settle_date) = #{year}
          AND MONTH(fsr.settle_date) = #{month}
    </select>

    <!-- 获取电站年度结算数据 -->
    <select id="getStationYearlySettlement" resultType="java.util.Map">
        SELECT 
            #{stationId} as stationId,
            #{year} as year,
            COALESCE(SUM(pss.settlement_electricity), 0) as yearlySettlementPower,
            COALESCE(SUM(pss.settlement_electric_fee), 0) as yearlySettlementFee,
            CASE 
                WHEN SUM(pss.settlement_electricity) > 0 
                THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
                ELSE 0 
            END as yearlyAvgPrice,
            COUNT(*) as recordCount,
            COUNT(DISTINCT MONTH(fsr.settle_date)) as monthCount
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id = #{stationId}
          AND YEAR(fsr.settle_date) = #{year}
    </select>

    <!-- 验证数据一致性 -->
    <select id="validateDataConsistency" resultType="java.util.Map">
        SELECT 
            'power_side_settle' as dataSource,
            COALESCE(SUM(pss.settlement_electricity), 0) as totalSettlementPower,
            COALESCE(SUM(pss.settlement_electric_fee), 0) as totalSettlementFee,
            COUNT(DISTINCT fsr.station_id) as stationCount,
            COUNT(*) as recordCount
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id IN 
        <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
            #{stationId}
        </foreach>
        <if test="startDate != null and endDate != null">
            AND fsr.settle_date BETWEEN #{startDate} AND #{endDate}
        </if>
    </select>

    <!-- 获取结算数据统计信息 -->
    <select id="getSettlementStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalRecords,
            COUNT(DISTINCT fsr.station_id) as uniqueStations,
            COUNT(DISTINCT DATE(fsr.settle_date)) as uniqueDates,
            MIN(fsr.settle_date) as earliestDate,
            MAX(fsr.settle_date) as latestDate,
            MIN(pss.settlement_electricity) as minSettlementPower,
            MAX(pss.settlement_electricity) as maxSettlementPower,
            AVG(pss.settlement_electricity) as avgSettlementPower,
            STDDEV(pss.settlement_electricity) as stddevSettlementPower,
            SUM(CASE WHEN pss.settlement_electricity IS NULL THEN 1 ELSE 0 END) as nullPowerCount,
            SUM(CASE WHEN pss.settlement_electric_fee IS NULL THEN 1 ELSE 0 END) as nullFeeCount
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id IN 
        <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
            #{stationId}
        </foreach>
        <if test="startDate != null and endDate != null">
            AND fsr.settle_date BETWEEN #{startDate} AND #{endDate}
        </if>
    </select>

    <!-- 获取电站类型结算汇总 -->
    <select id="getSettlementByStationType" resultType="java.util.Map">
        SELECT 
            s.type as stationType,
            CASE s.type
                WHEN 1 THEN '风电'
                WHEN 2 THEN '光伏'
                WHEN 3 THEN '储能'
                ELSE '其他'
            END as stationTypeName,
            COUNT(DISTINCT fsr.station_id) as stationCount,
            COALESCE(SUM(pss.settlement_electricity), 0) as totalSettlementPower,
            COALESCE(SUM(pss.settlement_electric_fee), 0) as totalSettlementFee,
            CASE 
                WHEN SUM(pss.settlement_electricity) > 0 
                THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
                ELSE 0 
            END as avgSettlementPrice,
            COUNT(*) as recordCount
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        INNER JOIN station s ON fsr.station_id = s.id
        WHERE fsr.station_id IN 
        <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
            #{stationId}
        </foreach>
        <if test="startDate != null and endDate != null">
            AND fsr.settle_date BETWEEN #{startDate} AND #{endDate}
        </if>
        GROUP BY s.type
        ORDER BY s.type
    </select>

    <!-- 获取最新结算数据 -->
    <select id="getLatestSettlementData" resultType="java.util.Map">
        SELECT 
            fsr.station_id,
            s.name as stationName,
            s.type as stationType,
            pss.settlement_electricity as latestSettlementPower,
            pss.settlement_electric_fee as latestSettlementFee,
            CASE 
                WHEN pss.settlement_electricity > 0 
                THEN pss.settlement_electric_fee / pss.settlement_electricity
                ELSE 0 
            END as latestSettlementPrice,
            fsr.settle_date as latestSettleDate,
            pss.actual_internet_electricity as latestActualElectricity,
            pss.contract_electricity as latestContractElectricity,
            pss.deviation_electricity as latestDeviationElectricity
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        INNER JOIN station s ON fsr.station_id = s.id
        INNER JOIN (
            SELECT 
                fsr2.station_id,
                MAX(fsr2.settle_date) as max_date
            FROM file_station_relation fsr2
            WHERE fsr2.station_id IN 
            <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
                #{stationId}
            </foreach>
            GROUP BY fsr2.station_id
        ) latest ON fsr.station_id = latest.station_id AND fsr.settle_date = latest.max_date
        ORDER BY fsr.station_id
    </select>

</mapper>
