<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.ScreenTradeSettlementMapper">

    <!-- 获取首页概览数据 - 按省份聚合所有电站的最新结算数据 -->
    <select id="selectDashboardSummaryData" resultType="java.util.Map">
        SELECT 
            COALESCE(SUM(current_month_power), 0) as accumulatedPower,
            COALESCE(SUM(current_month_plan_power), 0) as plannedPower,
            CASE WHEN COUNT(CASE WHEN settlement_average_price IS NOT NULL THEN 1 END) > 0 
                 THEN AVG(settlement_average_price) 
                 ELSE 0 END as settlementAvgPrice,
            COALESCE(SUM(settle_power), 0) as settlementPower,
            COALESCE(SUM(limited_power), 0) as limitedPower,
            CASE WHEN COUNT(CASE WHEN bench_mark_electricity_price IS NOT NULL THEN 1 END) > 0 
                 THEN AVG(bench_mark_electricity_price) 
                 ELSE 0 END as benchmarkPrice
        FROM screen_trade_settlement 
        WHERE 1=1 
        <if test="stationIds != null and stationIds.size() > 0">
            AND station_id IN 
            <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
                #{stationId}
            </foreach>
        </if>
        AND year = (
            SELECT MAX(year) 
            FROM screen_trade_settlement 
            <if test="stationIds != null and stationIds.size() > 0">
                WHERE station_id IN 
                <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
                    #{stationId}
                </foreach>
            </if>
        )
        AND month = (
            SELECT MAX(month) 
            FROM screen_trade_settlement 
            WHERE year = (
                SELECT MAX(year) 
                FROM screen_trade_settlement 
                <if test="stationIds != null and stationIds.size() > 0">
                    WHERE station_id IN 
                    <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
                        #{stationId}
                    </foreach>
                </if>
            )
            <if test="stationIds != null and stationIds.size() > 0">
                AND station_id IN 
                <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
                    #{stationId}
                </foreach>
            </if>
        )
    </select>

    <!-- 获取首页概览数据 - 指定年月 -->
    <select id="selectDashboardSummaryDataByMonth" resultType="java.util.Map">
        SELECT 
            COALESCE(SUM(current_month_power), 0) as accumulatedPower,
            COALESCE(SUM(current_month_plan_power), 0) as plannedPower,
            CASE WHEN COUNT(CASE WHEN settlement_average_price IS NOT NULL THEN 1 END) > 0 
                 THEN AVG(settlement_average_price) 
                 ELSE 0 END as settlementAvgPrice,
            COALESCE(SUM(settle_power), 0) as settlementPower,
            COALESCE(SUM(limited_power), 0) as limitedPower,
            CASE WHEN COUNT(CASE WHEN bench_mark_electricity_price IS NOT NULL THEN 1 END) > 0 
                 THEN AVG(bench_mark_electricity_price) 
                 ELSE 0 END as benchmarkPrice
        FROM screen_trade_settlement 
        WHERE year = #{year} AND month = #{month}
        <if test="stationIds != null and stationIds.size() > 0">
            AND station_id IN 
            <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
                #{stationId}
            </foreach>
        </if>
    </select>

    <!-- 调试方法：检查表中的数据情况 -->
    <select id="debugTableData" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalCount,
            COUNT(DISTINCT station_id) as stationCount,
            COUNT(DISTINCT CONCAT(year, '-', month)) as timeCount,
            MIN(CONCAT(year, '-', month)) as minTime,
            MAX(CONCAT(year, '-', month)) as maxTime
        FROM screen_trade_settlement 
        <if test="stationIds != null and stationIds.size() > 0">
            WHERE station_id IN 
            <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
                #{stationId}
            </foreach>
        </if>
    </select>

    <!-- 调试方法：查看具体的数据记录 -->
    <select id="debugDetailData" resultType="java.util.Map">
        SELECT
            station_id,
            year,
            month,
            current_month_power,
            current_month_plan_power,
            settlement_average_price,
            settle_power,
            limited_power
        FROM screen_trade_settlement
        <if test="stationIds != null and stationIds.size() > 0">
            WHERE station_id IN
            <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
                #{stationId}
            </foreach>
        </if>
        ORDER BY year DESC, month DESC, station_id
        LIMIT 10
    </select>

    <!-- 根据电站ID和月份查询结算数据 -->
    <select id="selectByStationAndMonth" resultType="org.jeecg.modules.api.power_trade.entity.ScreenTradeSettlement">
        SELECT * FROM screen_trade_settlement
        WHERE station_id = #{stationId}
        AND year = #{year}
        AND month = #{month}
        LIMIT 1
    </select>

    <!-- 查询电站累计发电量（从年初到指定月份） -->
    <select id="selectCumulativePower" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(current_month_power), 0)
        FROM screen_trade_settlement
        WHERE station_id = #{stationId}
        AND CONCAT(year, '-', LPAD(month, 2, '0')) &lt;= #{endMonth}
    </select>

    <!-- 查询电站指定月份的结算数据（用于电站总览） -->
    <select id="selectByStationAndMonthForOverview" resultType="org.jeecg.modules.api.power_trade.entity.ScreenTradeSettlement">
        SELECT * FROM screen_trade_settlement
        WHERE station_id = #{stationId}
        AND CONCAT(year, '-', LPAD(month, 2, '0')) = #{month}
        LIMIT 1
    </select>

    <!-- 查询电站年度累计发电量 -->
    <select id="selectYearCumulativePower" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(current_month_power), 0)
        FROM screen_trade_settlement
        WHERE station_id = #{stationId}
        AND year = #{year}
    </select>

    <!-- 查询指定电站、年份、月份的结算电量 -->
    <select id="selectSettlePower" resultType="java.math.BigDecimal">
        SELECT COALESCE(settle_power, 0)
        FROM screen_trade_settlement
        WHERE station_id = #{stationId}
        AND year = #{year}
        AND month = #{month}
        LIMIT 1
    </select>

    <!-- 查询指定电站、年份、月份的结算均价 -->
    <select id="selectSettlementAveragePrice" resultType="java.math.BigDecimal">
        SELECT COALESCE(settlement_average_price, 0)
        FROM screen_trade_settlement
        WHERE station_id = #{stationId}
        AND year = #{year}
        AND month = #{month}
        LIMIT 1
    </select>

    <!-- 查询指定电站、年份、月份的标杆电价（目标电价） -->
    <select id="selectBenchMarkElectricityPrice" resultType="java.math.BigDecimal">
        SELECT COALESCE(bench_mark_electricity_price, 0)
        FROM screen_trade_settlement
        WHERE station_id = #{stationId}
        AND year = #{year}
        AND month = #{month}
        LIMIT 1
    </select>

    <!-- 批量获取电站聚合结算数据（用于电站总览） -->
    <select id="selectStationAggregatedData" resultType="java.util.Map">
        SELECT
            station_id,
            SUM(settle_power) as totalSettlePower,
            SUM(settle_electricity_fee) as totalSettleFee,
            AVG(settlement_average_price) as avgSettlementPrice,
            AVG(bench_mark_electricity_price) as avgBenchmarkPrice,
            SUM(current_month_power) as totalCurrentMonthPower
        FROM screen_trade_settlement
        WHERE year = #{year}
        <if test="month != null and month != ''">
            AND month = #{month}
        </if>
        <if test="stationIds != null and stationIds.size() > 0">
            AND station_id IN
            <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
                #{stationId}
            </foreach>
        </if>
        GROUP BY station_id
        ORDER BY station_id
    </select>

    <!-- 获取单个电站聚合结算数据 -->
    <select id="selectSingleStationAggregatedData" resultType="java.util.Map">
        SELECT
            station_id,
            SUM(settle_power) as totalSettlePower,
            SUM(settle_electricity_fee) as totalSettleFee,
            AVG(settlement_average_price) as avgSettlementPrice,
            AVG(bench_mark_electricity_price) as avgBenchmarkPrice,
            SUM(current_month_power) as totalCurrentMonthPower,
            COUNT(*) as recordCount
        FROM screen_trade_settlement
        WHERE station_id = #{stationId}
        AND year = #{year}
        <if test="month != null and month != ''">
            AND month = #{month}
        </if>
        GROUP BY station_id
    </select>

    <!-- 获取电站列表聚合结算数据 -->
    <select id="selectStationListAggregatedData" resultType="java.util.Map">
        SELECT
            COALESCE(SUM(settle_power), 0) as settlementPower,
            CASE WHEN SUM(settle_power) > 0 THEN SUM(settle_electricity_fee) / SUM(settle_power) ELSE 0 END as settlementAvgPrice,
            CASE WHEN COUNT(CASE WHEN bench_mark_electricity_price IS NOT NULL THEN 1 END) > 0
                 THEN AVG(bench_mark_electricity_price) ELSE 0 END as coalBenchmarkPrice,
            COUNT(DISTINCT station_id) as totalStations,
            COUNT(*) as validSettlements
        FROM screen_trade_settlement
        WHERE year = #{year} AND month = #{month}
        <if test="stationIds != null and stationIds.size() > 0">
            AND station_id IN
            <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
                #{stationId}
            </foreach>
        </if>
    </select>

</mapper>
