package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 电站结算数据DTO
 */
@ApiModel("电站结算数据DTO")
@Data
@NoArgsConstructor
public class StationSettlementDTO {

    @ApiModelProperty(value = "电站ID")
    private Long stationId;

    @ApiModelProperty(value = "电站名称")
    private String stationName;

    @ApiModelProperty(value = "电站类型")
    private Integer stationType;

    @ApiModelProperty(value = "累计结算电量(GWh)")
    private BigDecimal totalSettlementElectricity;

    @ApiModelProperty(value = "累计交易均价(元/kWh)")
    private BigDecimal avgTradePrice;

    @ApiModelProperty(value = "累计结算电费(万元)")
    private BigDecimal totalSettlementElectricFee;

    @ApiModelProperty(value = "电站容量(MW)")
    private BigDecimal stationCapacity;

    @ApiModelProperty(value = "累计结算电量(MWh) - 新字段")
    private BigDecimal totalSettlementPower;

    @ApiModelProperty(value = "累计结算电费(元) - 新字段")
    private BigDecimal totalSettlementFee;

    @ApiModelProperty(value = "结算均价(元/MWh) - 新字段")
    private BigDecimal avgSettlementPrice;

    @ApiModelProperty(value = "实际上网电量(MWh)")
    private BigDecimal totalActualElectricity;

    @ApiModelProperty(value = "合同电量(MWh)")
    private BigDecimal totalContractElectricity;

    @ApiModelProperty(value = "偏差电量(MWh)")
    private BigDecimal totalDeviationElectricity;

    @ApiModelProperty(value = "结算记录数")
    private Integer recordCount;

    @ApiModelProperty(value = "首次结算日期")
    private String firstSettleDate;

    @ApiModelProperty(value = "最新结算日期")
    private String lastSettleDate;

    /**
     * 兼容性构造函数 - 支持原有字段
     */
    public StationSettlementDTO(Long stationId, String stationName, Integer stationType,
                               BigDecimal totalSettlementElectricity, BigDecimal avgTradePrice,
                               BigDecimal totalSettlementElectricFee) {
        this.stationId = stationId;
        this.stationName = stationName;
        this.stationType = stationType;
        this.totalSettlementElectricity = totalSettlementElectricity;
        this.avgTradePrice = avgTradePrice;
        this.totalSettlementElectricFee = totalSettlementElectricFee;

        // 同时设置新字段以保持一致性
        this.totalSettlementPower = totalSettlementElectricity != null ?
            totalSettlementElectricity.multiply(BigDecimal.valueOf(1000)) : null; // GWh转MWh
        this.avgSettlementPrice = avgTradePrice != null ?
            avgTradePrice.multiply(BigDecimal.valueOf(1000)) : null; // 元/kWh转元/MWh
        this.totalSettlementFee = totalSettlementElectricFee != null ?
            totalSettlementElectricFee.multiply(BigDecimal.valueOf(10000)) : null; // 万元转元
    }

    /**
     * 新的构造函数 - 支持统一结算数据
     */
    public StationSettlementDTO(Long stationId, String stationName, Integer stationType,
                               BigDecimal stationCapacity, BigDecimal totalSettlementPower,
                               BigDecimal totalSettlementFee, BigDecimal avgSettlementPrice,
                               Integer recordCount) {
        this.stationId = stationId;
        this.stationName = stationName;
        this.stationType = stationType;
        this.stationCapacity = stationCapacity;
        this.totalSettlementPower = totalSettlementPower;
        this.totalSettlementFee = totalSettlementFee;
        this.avgSettlementPrice = avgSettlementPrice;
        this.recordCount = recordCount;

        // 同时设置旧字段以保持兼容性
        this.totalSettlementElectricity = totalSettlementPower != null ?
            totalSettlementPower.divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP) : null; // MWh转GWh
        this.avgTradePrice = avgSettlementPrice != null ?
            avgSettlementPrice.divide(BigDecimal.valueOf(1000), 6, BigDecimal.ROUND_HALF_UP) : null; // 元/MWh转元/kWh
        this.totalSettlementElectricFee = totalSettlementFee != null ?
            totalSettlementFee.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP) : null; // 元转万元
    }

    /**
     * 获取电站类型名称
     */
    public String getStationTypeName() {
        if (stationType == null) {
            return "未知";
        }
        switch (stationType) {
            case 1: return "风电";
            case 2: return "光伏";
            case 3: return "储能";
            default: return "其他";
        }
    }

    /**
     * 数据同步方法 - 确保新旧字段一致
     */
    public void syncFields() {
        // 如果新字段有值，同步到旧字段
        if (totalSettlementPower != null) {
            this.totalSettlementElectricity = totalSettlementPower.divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP);
        }
        if (avgSettlementPrice != null) {
            this.avgTradePrice = avgSettlementPrice.divide(BigDecimal.valueOf(1000), 6, BigDecimal.ROUND_HALF_UP);
        }
        if (totalSettlementFee != null) {
            this.totalSettlementElectricFee = totalSettlementFee.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP);
        }

        // 如果旧字段有值但新字段没有，同步到新字段
        if (totalSettlementElectricity != null && totalSettlementPower == null) {
            this.totalSettlementPower = totalSettlementElectricity.multiply(BigDecimal.valueOf(1000));
        }
        if (avgTradePrice != null && avgSettlementPrice == null) {
            this.avgSettlementPrice = avgTradePrice.multiply(BigDecimal.valueOf(1000));
        }
        if (totalSettlementElectricFee != null && totalSettlementFee == null) {
            this.totalSettlementFee = totalSettlementElectricFee.multiply(BigDecimal.valueOf(10000));
        }
    }
}
