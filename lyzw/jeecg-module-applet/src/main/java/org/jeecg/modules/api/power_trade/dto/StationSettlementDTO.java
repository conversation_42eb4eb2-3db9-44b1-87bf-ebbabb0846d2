package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 电站结算数据DTO
 */
@ApiModel("电站结算数据DTO")
@Data
@NoArgsConstructor
public class StationSettlementDTO {

    @ApiModelProperty(value = "电站ID")
    private Long stationId;

    @ApiModelProperty(value = "电站名称")
    private String stationName;

    @ApiModelProperty(value = "电站类型")
    private Integer stationType;

    @ApiModelProperty(value = "累计结算电量(GWh)")
    private BigDecimal totalSettlementElectricity;

    @ApiModelProperty(value = "累计交易均价(元/kWh)")
    private BigDecimal avgTradePrice;

    @ApiModelProperty(value = "累计结算电费(万元)")
    private BigDecimal totalSettlementElectricFee;
}
