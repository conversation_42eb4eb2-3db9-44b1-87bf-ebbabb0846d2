package org.jeecg.modules.api.power_trade.dto;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class StationDetailDTO {
    // 电站基础信息
    private Long stationId;
    private String stationName;
    private String dispatchName;
    private Long provinceId;
    private String stationNo;
    private BigDecimal capacity;
    private Integer type; // 0-光伏, 1-风电

    // 年度发电统计
    private BigDecimal yearTotalPlanValue;    // 年计划发电量
    private BigDecimal yearTotalActualValue;  // 年实际发电量
    private BigDecimal completionRate;        // 完成率

    // 查询参数
    private String queryYear;
}