package org.jeecg.modules.api.power_trade.util;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;

/**
 * 日期格式处理工具类
 * 
 * <AUTHOR> Team
 * @since 2025-01-01
 */
public class DateFormatUtil {

    /**
     * 日期格式常量
     */
    public static final String PATTERN_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String PATTERN_YYYY_MM = "yyyy-MM";
    public static final String PATTERN_YYYY = "yyyy";
    
    /**
     * 正则表达式常量
     */
    private static final String REGEX_YYYY_MM_DD = "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])$";
    private static final String REGEX_YYYY_MM = "^\\d{4}-(0[1-9]|1[0-2])$";
    private static final String REGEX_YYYY = "^\\d{4}$";

    /**
     * 统一日期格式验证和转换
     * 
     * @param date 日期字符串
     * @return 日期解析结果
     * @throws ParameterValidator.ValidationException 验证失败时抛出
     */
    public static DateParseResult parseAndValidateDate(String date) {
        if (StringUtils.isBlank(date)) {
            throw new ParameterValidator.ValidationException("日期参数不能为空");
        }
        
        String trimmedDate = date.trim();
        
        // 检查 yyyy-MM-dd 格式
        if (trimmedDate.matches(REGEX_YYYY_MM_DD)) {
            try {
                LocalDate.parse(trimmedDate, DateTimeFormatter.ofPattern(PATTERN_YYYY_MM_DD));
                String targetMonth = trimmedDate.substring(0, 7);
                return new DateParseResult(trimmedDate, targetMonth, DateType.FULL_DATE);
            } catch (DateTimeParseException e) {
                throw new ParameterValidator.ValidationException("日期格式错误，无效的日期: " + trimmedDate);
            }
        }
        
        // 检查 yyyy-MM 格式
        if (trimmedDate.matches(REGEX_YYYY_MM)) {
            try {
                YearMonth.parse(trimmedDate, DateTimeFormatter.ofPattern(PATTERN_YYYY_MM));
                return new DateParseResult(trimmedDate, trimmedDate, DateType.YEAR_MONTH);
            } catch (DateTimeParseException e) {
                throw new ParameterValidator.ValidationException("年月格式错误，无效的年月: " + trimmedDate);
            }
        }
        
        // 检查 yyyy 格式
        if (trimmedDate.matches(REGEX_YYYY)) {
            try {
                Integer.parseInt(trimmedDate);
                return new DateParseResult(trimmedDate, null, DateType.YEAR_ONLY);
            } catch (NumberFormatException e) {
                throw new ParameterValidator.ValidationException("年份格式错误，无效的年份: " + trimmedDate);
            }
        }
        
        throw new ParameterValidator.ValidationException(
            "日期格式不正确，支持格式：yyyy-MM-dd（具体日期）、yyyy-MM（年月）或 yyyy（年份）");
    }

    /**
     * 从日期字符串提取年月信息
     * 
     * @param date 日期字符串
     * @return 年月字符串（yyyy-MM格式）
     * @throws ParameterValidator.ValidationException 验证失败时抛出
     */
    public static String extractTargetMonth(String date) {
        DateParseResult result = parseAndValidateDate(date);
        if (result.getTargetMonth() == null) {
            throw new ParameterValidator.ValidationException("无法从年份格式中提取月份信息");
        }
        return result.getTargetMonth();
    }

    /**
     * 从日期字符串提取年份信息
     * 
     * @param date 日期字符串
     * @return 年份字符串（yyyy格式）
     * @throws ParameterValidator.ValidationException 验证失败时抛出
     */
    public static String extractYear(String date) {
        DateParseResult result = parseAndValidateDate(date);
        return result.getOriginalDate().substring(0, 4);
    }

    /**
     * 多格式日期验证
     * 
     * @param date             日期字符串
     * @param supportedFormats 支持的格式（正则表达式）
     * @return 是否匹配任一格式
     */
    public static boolean validateDateFormat(String date, String... supportedFormats) {
        if (StringUtils.isBlank(date) || supportedFormats == null || supportedFormats.length == 0) {
            return false;
        }
        
        return Arrays.stream(supportedFormats)
                .anyMatch(format -> date.trim().matches(format));
    }

    /**
     * 检查是否为有效的日期格式
     * 
     * @param date 日期字符串
     * @return 是否有效
     */
    public static boolean isValidDateFormat(String date) {
        if (StringUtils.isBlank(date)) {
            return false;
        }
        
        try {
            parseAndValidateDate(date);
            return true;
        } catch (ParameterValidator.ValidationException e) {
            return false;
        }
    }

    /**
     * 获取当前年月
     * 
     * @return 当前年月（yyyy-MM格式）
     */
    public static String getCurrentYearMonth() {
        return YearMonth.now().format(DateTimeFormatter.ofPattern(PATTERN_YYYY_MM));
    }

    /**
     * 获取当前年份
     * 
     * @return 当前年份（yyyy格式）
     */
    public static String getCurrentYear() {
        return String.valueOf(LocalDate.now().getYear());
    }

    /**
     * 格式化日期为指定格式
     * 
     * @param date   日期字符串
     * @param format 目标格式
     * @return 格式化后的日期字符串
     * @throws ParameterValidator.ValidationException 验证失败时抛出
     */
    public static String formatDate(String date, String format) {
        DateParseResult result = parseAndValidateDate(date);
        
        try {
            switch (result.getDateType()) {
                case FULL_DATE:
                    LocalDate localDate = LocalDate.parse(result.getOriginalDate(), 
                        DateTimeFormatter.ofPattern(PATTERN_YYYY_MM_DD));
                    return localDate.format(DateTimeFormatter.ofPattern(format));
                    
                case YEAR_MONTH:
                    YearMonth yearMonth = YearMonth.parse(result.getOriginalDate(), 
                        DateTimeFormatter.ofPattern(PATTERN_YYYY_MM));
                    return yearMonth.format(DateTimeFormatter.ofPattern(format));
                    
                case YEAR_ONLY:
                    if (PATTERN_YYYY.equals(format)) {
                        return result.getOriginalDate();
                    } else {
                        throw new ParameterValidator.ValidationException(
                            "年份格式无法转换为: " + format);
                    }
                    
                default:
                    throw new ParameterValidator.ValidationException("不支持的日期类型");
            }
        } catch (DateTimeParseException e) {
            throw new ParameterValidator.ValidationException("日期格式转换失败: " + e.getMessage());
        }
    }

    /**
     * 验证日期范围
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @throws ParameterValidator.ValidationException 验证失败时抛出
     */
    public static void validateDateRange(String startDate, String endDate) {
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return; // 如果有一个为空，则不验证范围
        }
        
        DateParseResult startResult = parseAndValidateDate(startDate);
        DateParseResult endResult = parseAndValidateDate(endDate);
        
        // 只有相同类型的日期才能比较
        if (startResult.getDateType() != endResult.getDateType()) {
            throw new ParameterValidator.ValidationException("开始日期和结束日期格式必须一致");
        }
        
        try {
            switch (startResult.getDateType()) {
                case FULL_DATE:
                    LocalDate start = LocalDate.parse(startResult.getOriginalDate(), 
                        DateTimeFormatter.ofPattern(PATTERN_YYYY_MM_DD));
                    LocalDate end = LocalDate.parse(endResult.getOriginalDate(), 
                        DateTimeFormatter.ofPattern(PATTERN_YYYY_MM_DD));
                    if (start.isAfter(end)) {
                        throw new ParameterValidator.ValidationException("开始日期不能晚于结束日期");
                    }
                    break;
                    
                case YEAR_MONTH:
                    YearMonth startYM = YearMonth.parse(startResult.getOriginalDate(), 
                        DateTimeFormatter.ofPattern(PATTERN_YYYY_MM));
                    YearMonth endYM = YearMonth.parse(endResult.getOriginalDate(), 
                        DateTimeFormatter.ofPattern(PATTERN_YYYY_MM));
                    if (startYM.isAfter(endYM)) {
                        throw new ParameterValidator.ValidationException("开始年月不能晚于结束年月");
                    }
                    break;
                    
                case YEAR_ONLY:
                    int startYear = Integer.parseInt(startResult.getOriginalDate());
                    int endYear = Integer.parseInt(endResult.getOriginalDate());
                    if (startYear > endYear) {
                        throw new ParameterValidator.ValidationException("开始年份不能晚于结束年份");
                    }
                    break;
            }
        } catch (DateTimeParseException e) {
            throw new ParameterValidator.ValidationException("日期范围验证失败: " + e.getMessage());
        }
    }

    /**
     * 日期解析结果
     */
    public static class DateParseResult {
        private final String originalDate;
        private final String targetMonth;
        private final DateType dateType;

        public DateParseResult(String originalDate, String targetMonth, DateType dateType) {
            this.originalDate = originalDate;
            this.targetMonth = targetMonth;
            this.dateType = dateType;
        }

        public String getOriginalDate() {
            return originalDate;
        }

        public String getTargetMonth() {
            return targetMonth;
        }

        public DateType getDateType() {
            return dateType;
        }

        @Override
        public String toString() {
            return String.format("DateParseResult{originalDate='%s', targetMonth='%s', dateType=%s}", 
                originalDate, targetMonth, dateType);
        }
    }

    /**
     * 日期类型枚举
     */
    public enum DateType {
        FULL_DATE,    // yyyy-MM-dd
        YEAR_MONTH,   // yyyy-MM
        YEAR_ONLY     // yyyy
    }
}
