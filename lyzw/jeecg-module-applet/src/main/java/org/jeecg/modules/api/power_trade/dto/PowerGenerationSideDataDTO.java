package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 发电侧数据DTO（售电侧）
 * 包含所有以powerGeneration前缀的字段，去除前缀后的字段名
 */
@Data
public class PowerGenerationSideDataDTO {

    @ApiModelProperty(value = "日前偏差电量(单位:万千瓦时)", example = "null")
    private BigDecimal dayAheadDeviationPower;

    @ApiModelProperty(value = "日前偏差平均电价(单位:元/千瓦时)", example = "null")
    private BigDecimal dayAheadDeviationAveragePrice;

    @ApiModelProperty(value = "日前偏差电费(单位:万元)", example = "null")
    private BigDecimal dayAheadDeviationFee;

    @ApiModelProperty(value = "实时偏差电量(单位:万千瓦时)", example = "null")
    private BigDecimal realtimeDeviationPower;

    @ApiModelProperty(value = "实时偏差平均电价(单位:元/千瓦时)", example = "null")
    private BigDecimal realtimeDeviationAveragePrice;

    @ApiModelProperty(value = "实时偏差电费(单位:万元)", example = "null")
    private BigDecimal realtimeDeviationFee;

    @ApiModelProperty(value = "总电量(单位:万千瓦时)", example = "null")
    private BigDecimal totalPower;

    @ApiModelProperty(value = "总费用(单位:万元)", example = "null")
    private BigDecimal totalFee;
}
