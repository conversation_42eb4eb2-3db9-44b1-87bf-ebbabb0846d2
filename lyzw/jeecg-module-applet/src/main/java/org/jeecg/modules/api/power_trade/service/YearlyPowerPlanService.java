package org.jeecg.modules.api.power_trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.swagger.models.auth.In;
import org.jeecg.modules.api.power_trade.entity.YearlyPowerPlan;
import org.jeecg.modules.api.power_trade.dto.StationDetailDTO;

import java.util.List;
import java.util.Map;

/**
 * 年度发电计划服务接口
 */
public interface YearlyPowerPlanService extends IService<YearlyPowerPlan> {


    Map<String, Object> getStationYearlyPowerStats(Integer stationId, String year);

    /**
     * 获取电站详情（包含基础信息和年度发电统计）
     * @param stationId 电站ID
     * @param year 年份
     * @return 电站详情
     */
    StationDetailDTO getStationDetailWithPowerPlan(Long stationId, String year);

    /**
     * 获取电站月度发电计划明细
     * @param stationId 电站ID
     * @param year 年份
     * @return 月度明细列表
     */
    List<YearlyPowerPlan> getStationMonthlyPowerPlan(Long stationId, String year);
}