<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.EnergyStorageDailyCleanMapper">

    <!-- 按日查询明细数据 -->
    <select id="selectByDay" resultType="org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean">
        SELECT * FROM energy_storage_daily_clean
        WHERE station_id = #{stationId}
        AND DATE(date) = #{date}
        ORDER BY date
    </select>

    <!-- 按月汇总查询 -->
    <select id="selectByMonth" resultType="org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean">
        SELECT
            DATE_FORMAT(date, '%Y-%m-01') as date,
            SUM(user_day_ahead_deviation_power) as userDayAheadDeviationPower,
            AVG(user_day_ahead_deviation_average_price) as userDayAheadDeviationAveragePrice,
            SUM(user_day_ahead_deviation_fee) as userDayAheadDeviationFee,
            SUM(user_realtime_deviation_power) as userRealTimeDeviationPower,
            AVG(user_realtime_deviation_average_price) as userRealTimeDeviationAveragePrice,
            SUM(user_realtime_deviation_fee) as userRealTimeDeviationFee,
            SUM(user_total_power) as userTotalDeviationPower,
            SUM(user_total_fee) as userTotalDeviationFee,
            SUM(power_generation_day_ahead_deviation_power) as gridDayAheadDeviationPower,
            AVG(power_generation_day_ahead_deviation_average_price) as gridDayAheadDeviationAveragePrice,
            SUM(power_generation_day_ahead_deviation_fee) as gridDayAheadDeviationFee,
            SUM(power_generation_realtime_deviation_power) as gridRealTimeDeviationPower,
            AVG(power_generation_realtime_deviation_average_price) as gridRealTimeDeviationAveragePrice,
            SUM(power_generation_realtime_deviation_fee) as gridRealTimeDeviationFee,
            SUM(power_generation_total_power) as gridTotalDeviationPower,
            SUM(power_generation_total_fee) as gridTotalDeviationFee
        FROM energy_storage_daily_clean
        WHERE station_id = #{stationId}
        AND DATE_FORMAT(date, '%Y-%m') = #{yearMonth}
        GROUP BY DATE_FORMAT(date, '%Y-%m-01')
        ORDER BY DATE_FORMAT(date, '%Y-%m-01')
    </select>

    <!-- 按年汇总查询 -->
    <select id="selectByYear" resultType="org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean">
        SELECT
            DATE_FORMAT(date, '%Y-01-01') as date,
            SUM(user_day_ahead_deviation_power) as userDayAheadDeviationPower,
            AVG(user_day_ahead_deviation_average_price) as userDayAheadDeviationAveragePrice,
            SUM(user_day_ahead_deviation_fee) as userDayAheadDeviationFee,
            SUM(user_realtime_deviation_power) as userRealTimeDeviationPower,
            AVG(user_realtime_deviation_average_price) as userRealTimeDeviationAveragePrice,
            SUM(user_realtime_deviation_fee) as userRealTimeDeviationFee,
            SUM(user_total_power) as userTotalDeviationPower,
            SUM(user_total_fee) as userTotalDeviationFee,
            SUM(power_generation_day_ahead_deviation_power) as gridDayAheadDeviationPower,
            AVG(power_generation_day_ahead_deviation_average_price) as gridDayAheadDeviationAveragePrice,
            SUM(power_generation_day_ahead_deviation_fee) as gridDayAheadDeviationFee,
            SUM(power_generation_realtime_deviation_power) as gridRealTimeDeviationPower,
            AVG(power_generation_realtime_deviation_average_price) as gridRealTimeDeviationAveragePrice,
            SUM(power_generation_realtime_deviation_fee) as gridRealTimeDeviationFee,
            SUM(power_generation_total_power) as gridTotalDeviationPower,
            SUM(power_generation_total_fee) as gridTotalDeviationFee
        FROM energy_storage_daily_clean
        WHERE station_id = #{stationId}
        AND YEAR(date) = #{year}
        GROUP BY DATE_FORMAT(date, '%Y-01-01')
        ORDER BY DATE_FORMAT(date, '%Y-01-01')
    </select>

    <!-- 按日期范围查询储能日交易信息 -->
    <select id="selectByDateRange" resultType="org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean">
        SELECT
            id,
            date,
            station_id as stationId,
            user_day_ahead_deviation_power as userDayAheadDeviationPower,
            user_day_ahead_deviation_average_price as userDayAheadDeviationAveragePrice,
            user_day_ahead_deviation_fee as userDayAheadDeviationFee,
            user_realtime_deviation_power as userRealTimeDeviationPower,
            user_realtime_deviation_average_price as userRealTimeDeviationAveragePrice,
            user_realtime_deviation_fee as userRealTimeDeviationFee,
            user_total_power as userTotalPower,
            user_total_fee as userTotalFee,
            power_generation_day_ahead_deviation_power as powerGenerationDayAheadDeviationPower,
            power_generation_day_ahead_deviation_average_price as powerGenerationDayAheadDeviationAveragePrice,
            power_generation_day_ahead_deviation_fee as powerGenerationDayAheadDeviationFee,
            power_generation_realtime_deviation_power as powerGenerationRealTimeDeviationPower,
            power_generation_realtime_deviation_average_price as powerGenerationRealTimeDeviationAveragePrice,
            power_generation_realtime_deviation_fee as powerGenerationRealTimeDeviationFee,
            power_generation_total_power as powerGenerationTotalPower,
            power_generation_total_fee as powerGenerationTotalFee,
            create_time as createTime,
            update_time as updateTime,
            create_by as createBy,
            update_by as updateBy
        FROM energy_storage_daily_clean
        WHERE date BETWEEN #{startDate} AND #{endDate}
        ORDER BY date ASC, station_id ASC
    </select>

</mapper>