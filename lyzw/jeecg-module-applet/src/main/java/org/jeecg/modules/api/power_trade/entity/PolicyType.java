package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 政策类型实体
 */
@Data
@TableName("policy_type")
@ApiModel(value = "PolicyType", description = "政策类型实体")
public class PolicyType {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @TableField("type_name")
    @ApiModelProperty(value = "政策类型名称", example = "市场政策")
    private String typeName;

    @TableField("type_code")
    @ApiModelProperty(value = "政策类型编码", example = "MARKET_POLICY")
    private String typeCode;

    @TableField("sort_order")
    @ApiModelProperty(value = "排序", example = "1")
    private Integer sortOrder;

    @TableField("status")
    @ApiModelProperty(value = "状态(1:启用,0:禁用)", example = "1")
    private Integer status;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private Date createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间", example = "2024-01-01 12:00:00")
    private Date updateTime;
}
