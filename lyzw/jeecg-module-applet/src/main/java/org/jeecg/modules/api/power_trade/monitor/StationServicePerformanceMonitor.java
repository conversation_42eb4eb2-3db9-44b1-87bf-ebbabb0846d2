package org.jeecg.modules.api.power_trade.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;

/**
 * 电站服务性能监控组件
 * 监控查询耗时、成功率、缓存命中率等关键指标
 * 
 * <AUTHOR> Team
 * @since 2025-01-01
 */
@Slf4j
@Component
public class StationServicePerformanceMonitor {

    /**
     * 查询耗时统计
     */
    private final Map<String, QueryMetrics> queryMetricsMap = new ConcurrentHashMap<>();

    /**
     * 全局统计计数器
     */
    private final AtomicLong totalQueries = new AtomicLong(0);
    private final AtomicLong successfulQueries = new AtomicLong(0);
    private final AtomicLong failedQueries = new AtomicLong(0);

    /**
     * 监控查询执行
     * 
     * @param queryName     查询名称
     * @param querySupplier 查询执行器
     * @param <T>           返回类型
     * @return 查询结果
     */
    public <T> T monitorQuery(String queryName, Supplier<T> querySupplier) {
        long startTime = System.currentTimeMillis();
        totalQueries.incrementAndGet();
        
        try {
            T result = querySupplier.get();
            
            long duration = System.currentTimeMillis() - startTime;
            recordSuccessfulQuery(queryName, duration);
            successfulQueries.incrementAndGet();
            
            if (duration > 1000) { // 超过1秒的查询记录警告
                log.warn("慢查询检测 - 查询: {}, 耗时: {}ms", queryName, duration);
            } else {
                log.debug("查询完成 - 查询: {}, 耗时: {}ms", queryName, duration);
            }
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            recordFailedQuery(queryName, duration);
            failedQueries.incrementAndGet();
            
            log.error("查询失败 - 查询: {}, 耗时: {}ms, 错误: {}", queryName, duration, e.getMessage());
            throw e;
        }
    }

    /**
     * 监控查询执行（无返回值）
     * 
     * @param queryName 查询名称
     * @param runnable  查询执行器
     */
    public void monitorQuery(String queryName, Runnable runnable) {
        monitorQuery(queryName, () -> {
            runnable.run();
            return null;
        });
    }

    /**
     * 记录成功查询
     */
    private void recordSuccessfulQuery(String queryName, long duration) {
        QueryMetrics metrics = queryMetricsMap.computeIfAbsent(queryName, k -> new QueryMetrics());
        metrics.recordSuccess(duration);
    }

    /**
     * 记录失败查询
     */
    private void recordFailedQuery(String queryName, long duration) {
        QueryMetrics metrics = queryMetricsMap.computeIfAbsent(queryName, k -> new QueryMetrics());
        metrics.recordFailure(duration);
    }

    /**
     * 获取查询统计信息
     * 
     * @param queryName 查询名称
     * @return 统计信息
     */
    public Map<String, Object> getQueryStats(String queryName) {
        QueryMetrics metrics = queryMetricsMap.get(queryName);
        if (metrics == null) {
            return new ConcurrentHashMap<>();
        }
        
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("queryName", queryName);
        stats.put("totalCount", metrics.getTotalCount());
        stats.put("successCount", metrics.getSuccessCount());
        stats.put("failureCount", metrics.getFailureCount());
        stats.put("successRate", metrics.getSuccessRate());
        stats.put("averageDuration", metrics.getAverageDuration());
        stats.put("minDuration", metrics.getMinDuration());
        stats.put("maxDuration", metrics.getMaxDuration());
        stats.put("lastExecutionTime", metrics.getLastExecutionTime());
        
        return stats;
    }

    /**
     * 获取所有查询统计信息
     * 
     * @return 所有查询的统计信息
     */
    public Map<String, Object> getAllQueryStats() {
        Map<String, Object> allStats = new ConcurrentHashMap<>();
        
        // 全局统计
        Map<String, Object> globalStats = new ConcurrentHashMap<>();
        globalStats.put("totalQueries", totalQueries.get());
        globalStats.put("successfulQueries", successfulQueries.get());
        globalStats.put("failedQueries", failedQueries.get());
        globalStats.put("globalSuccessRate", calculateGlobalSuccessRate());
        allStats.put("global", globalStats);
        
        // 各查询统计
        Map<String, Object> queryStats = new ConcurrentHashMap<>();
        queryMetricsMap.forEach((queryName, metrics) -> {
            queryStats.put(queryName, getQueryStats(queryName));
        });
        allStats.put("queries", queryStats);
        
        return allStats;
    }

    /**
     * 获取性能报告
     * 
     * @return 性能报告
     */
    public Map<String, Object> getPerformanceReport() {
        Map<String, Object> report = new ConcurrentHashMap<>();
        
        // 基础统计
        report.put("totalQueries", totalQueries.get());
        report.put("successfulQueries", successfulQueries.get());
        report.put("failedQueries", failedQueries.get());
        report.put("globalSuccessRate", calculateGlobalSuccessRate());
        
        // 慢查询统计
        Map<String, Object> slowQueries = new ConcurrentHashMap<>();
        queryMetricsMap.forEach((queryName, metrics) -> {
            if (metrics.getAverageDuration() > 1000) {
                Map<String, Object> slowQueryInfo = new ConcurrentHashMap<>();
                slowQueryInfo.put("averageDuration", metrics.getAverageDuration());
                slowQueryInfo.put("maxDuration", metrics.getMaxDuration());
                slowQueryInfo.put("count", metrics.getTotalCount());
                slowQueries.put(queryName, slowQueryInfo);
            }
        });
        report.put("slowQueries", slowQueries);
        
        // 错误率高的查询
        Map<String, Object> errorProneQueries = new ConcurrentHashMap<>();
        queryMetricsMap.forEach((queryName, metrics) -> {
            if (metrics.getSuccessRate() < 0.95 && metrics.getTotalCount() > 10) {
                Map<String, Object> errorInfo = new ConcurrentHashMap<>();
                errorInfo.put("successRate", metrics.getSuccessRate());
                errorInfo.put("failureCount", metrics.getFailureCount());
                errorInfo.put("totalCount", metrics.getTotalCount());
                errorProneQueries.put(queryName, errorInfo);
            }
        });
        report.put("errorProneQueries", errorProneQueries);
        
        return report;
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        queryMetricsMap.clear();
        totalQueries.set(0);
        successfulQueries.set(0);
        failedQueries.set(0);
        log.info("性能监控统计信息已重置");
    }

    /**
     * 计算全局成功率
     */
    private double calculateGlobalSuccessRate() {
        long total = totalQueries.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) successfulQueries.get() / total;
    }

    /**
     * 查询指标内部类
     */
    private static class QueryMetrics {
        private final AtomicLong totalCount = new AtomicLong(0);
        private final AtomicLong successCount = new AtomicLong(0);
        private final AtomicLong failureCount = new AtomicLong(0);
        private final AtomicLong totalDuration = new AtomicLong(0);
        private volatile long minDuration = Long.MAX_VALUE;
        private volatile long maxDuration = 0;
        private volatile long lastExecutionTime = 0;

        public void recordSuccess(long duration) {
            totalCount.incrementAndGet();
            successCount.incrementAndGet();
            totalDuration.addAndGet(duration);
            updateDurationStats(duration);
            lastExecutionTime = System.currentTimeMillis();
        }

        public void recordFailure(long duration) {
            totalCount.incrementAndGet();
            failureCount.incrementAndGet();
            totalDuration.addAndGet(duration);
            updateDurationStats(duration);
            lastExecutionTime = System.currentTimeMillis();
        }

        private void updateDurationStats(long duration) {
            if (duration < minDuration) {
                minDuration = duration;
            }
            if (duration > maxDuration) {
                maxDuration = duration;
            }
        }

        public long getTotalCount() {
            return totalCount.get();
        }

        public long getSuccessCount() {
            return successCount.get();
        }

        public long getFailureCount() {
            return failureCount.get();
        }

        public double getSuccessRate() {
            long total = totalCount.get();
            return total == 0 ? 0.0 : (double) successCount.get() / total;
        }

        public double getAverageDuration() {
            long total = totalCount.get();
            return total == 0 ? 0.0 : (double) totalDuration.get() / total;
        }

        public long getMinDuration() {
            return minDuration == Long.MAX_VALUE ? 0 : minDuration;
        }

        public long getMaxDuration() {
            return maxDuration;
        }

        public long getLastExecutionTime() {
            return lastExecutionTime;
        }
    }

    /**
     * 性能监控常量
     */
    public static class QueryNames {
        public static final String STATION_LIST_QUERY = "station_list_query";
        public static final String STATION_TRADING_OVERVIEW = "station_trading_overview";
        public static final String BATCH_SETTLEMENT_QUERY = "batch_settlement_query";
        public static final String STATION_DETAIL_QUERY = "station_detail_query";
        public static final String NATIONAL_AGGREGATION = "national_aggregation";
        public static final String PROVINCE_DATA_QUERY = "province_data_query";
        public static final String MONTHLY_DATA_BUILD = "monthly_data_build";
        public static final String YEARLY_DATA_BUILD = "yearly_data_build";
    }

    /**
     * 性能阈值常量
     */
    public static class PerformanceThresholds {
        public static final long SLOW_QUERY_THRESHOLD_MS = 1000;
        public static final double MIN_SUCCESS_RATE = 0.95;
        public static final long MAX_ACCEPTABLE_DURATION_MS = 5000;
    }
}
