package org.jeecg.modules.api.power_trade.constant;

/**
 * 电站相关常量定义
 * <AUTHOR>
 * @date 2025-07-14
 */
public class StationConstants {

    // ========== 电站类型常量 ==========
    
    /**
     * 电站类型：光伏
     */
    public static final Integer STATION_TYPE_SOLAR = 1;
    
    /**
     * 电站类型：风电
     */
    public static final Integer STATION_TYPE_WIND = 2;
    
    /**
     * 电站类型：储能
     */
    public static final Integer STATION_TYPE_STORAGE = 3;

    // ========== 电站类型名称 ==========
    
    /**
     * 光伏电站名称
     */
    public static final String STATION_TYPE_NAME_SOLAR = "光伏";
    
    /**
     * 风电电站名称
     */
    public static final String STATION_TYPE_NAME_WIND = "风电";
    
    /**
     * 储能电站名称
     */
    public static final String STATION_TYPE_NAME_STORAGE = "储能";

    // ========== 单位转换常量 ==========
    
    /**
     * 容量单位转换阈值：MW转GW的阈值
     */
    public static final Double CAPACITY_GW_THRESHOLD = 1000.0;
    
    /**
     * 电量单位转换：MWh转GWh的除数
     */
    public static final Double POWER_GWH_DIVISOR = 1000.0;
    
    /**
     * 容量单位：MW
     */
    public static final String UNIT_MW = "MW";
    
    /**
     * 容量单位：GW
     */
    public static final String UNIT_GW = "GW";
    
    /**
     * 电量单位：MWh
     */
    public static final String UNIT_MWH = "MWh";
    
    /**
     * 电量单位：GWh
     */
    public static final String UNIT_GWH = "GWh";
    
    /**
     * 价格单位：元/MWh
     */
    public static final String UNIT_PRICE = "元/MWh";

    // ========== 电站状态常量 ==========
    
    /**
     * 交易状态：已开启
     */
    public static final Integer TRADE_STATUS_ENABLED = 1;
    
    /**
     * 交易状态：未开启
     */
    public static final Integer TRADE_STATUS_DISABLED = 0;
    
    /**
     * 电力状态：正常
     */
    public static final Integer POWER_STATUS_NORMAL = 1;
    
    /**
     * 电力状态：异常
     */
    public static final Integer POWER_STATUS_ABNORMAL = 0;

    // ========== 区域代码常量 ==========
    
    /**
     * 安徽省代码
     */
    public static final String PROVINCE_ANHUI = "ah";
    
    /**
     * 江苏省代码
     */
    public static final String PROVINCE_JIANGSU = "js";
    
    /**
     * 全国代码
     */
    public static final String PROVINCE_NATIONAL = "national";



    // ========== 工具方法 ==========

    /**
     * 电量单位转换(MWh -> GWh)
     * @param powerMWh 电量值(MWh)
     * @return 电量值(GWh)
     */
    public static Double convertMWhToGWh(Double powerMWh) {
        if (powerMWh == null) {
            return 0.0;
        }
        return powerMWh / POWER_GWH_DIVISOR;
    }
    
    /**
     * 格式化电量显示
     * @param powerMWh 电量值(MWh)
     * @return 格式化后的电量字符串(GWh)
     */
    public static String formatPower(Double powerMWh) {
        if (powerMWh == null || powerMWh == 0) {
            return "0" + UNIT_GWH;
        }
        
        Double powerGWh = convertMWhToGWh(powerMWh);
        return String.format("%.2f%s", powerGWh, UNIT_GWH);
    }
    
    /**
     * 格式化价格显示
     * @param price 价格值
     * @return 格式化后的价格字符串
     */
    public static String formatPrice(Double price) {
        if (price == null || price == 0) {
            return "0" + UNIT_PRICE;
        }
        return String.format("%.2f%s", price, UNIT_PRICE);
    }
}
