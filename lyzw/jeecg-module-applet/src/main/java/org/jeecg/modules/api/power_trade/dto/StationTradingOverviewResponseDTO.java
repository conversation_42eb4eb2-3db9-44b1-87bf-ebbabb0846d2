package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 电站交易总览响应DTO
 * 用于/electricity/api/power_trade/stations/list接口的返回
 */
@Data
@ApiModel(value = "电站交易总览响应DTO", description = "电站交易总览的完整响应数据")
public class StationTradingOverviewResponseDTO {

    @ApiModelProperty(value = "电站交易数据列表", required = true)
    private List<StationTradingOverviewItemDTO> records;

    @ApiModelProperty(value = "总记录数", required = true, example = "150")
    private Long total;

    @ApiModelProperty(value = "每页条数", required = true, example = "10")
    private Long size;

    @ApiModelProperty(value = "当前页码", required = true, example = "1")
    private Long current;

    @ApiModelProperty(value = "总页数", required = true, example = "15")
    private Long pages;

    @ApiModelProperty(value = "查询维度 (1-月度, 2-年度)", required = true, example = "1")
    private Integer dimension;

    @ApiModelProperty(value = "查询年份", required = true, example = "2024")
    private String year;

    @ApiModelProperty(value = "查询月份 (月度查询时返回)", example = "07")
    private String month;

    @ApiModelProperty(value = "省份ID (0-全国, 1-安徽, 2-江苏)", required = true, example = "1")
    private Integer provinceId;

    @ApiModelProperty(value = "电站名称搜索关键词", example = "风电")
    private String searchName;

    /**
     * 电站交易总览单项数据DTO
     */
    @Data
    @ApiModel(value = "电站交易总览单项数据DTO", description = "单个电站的交易总览数据")
    public static class StationTradingOverviewItemDTO {

        @ApiModelProperty(value = "电站ID", required = true, example = "123")
        private Long stationId;

        @ApiModelProperty(value = "电站名称", required = true, example = "永武风电")
        private String stationName;

        @ApiModelProperty(value = "电站类型 (1-风电, 2-光伏, 3-储能)", required = true, example = "1")
        private Integer stationType;

        @ApiModelProperty(value = "电站类型名称", example = "风电")
        private String stationTypeName;

        @ApiModelProperty(value = "电站容量(MW)", example = "143.75")
        private BigDecimal capacity;

        @ApiModelProperty(value = "省份ID", required = true, example = "1")
        private Integer provinceId;

        @ApiModelProperty(value = "省份名称", example = "安徽省")
        private String provinceName;

        @ApiModelProperty(value = "累计结算电量(MWh)", required = true, example = "12500.50")
        private BigDecimal totalSettlementElectricity;

        @ApiModelProperty(value = "结算电量单位", example = "MWh")
        private String settlementElectricityUnit = "MWh";

        @ApiModelProperty(value = "交易均价(元/MWh)", required = true, example = "385.25")
        private BigDecimal avgTradePrice;

        @ApiModelProperty(value = "交易均价单位", example = "元/MWh")
        private String avgTradePriceUnit = "元/MWh";

        @ApiModelProperty(value = "累计结算电费(万元)", example = "4812.69")
        private BigDecimal totalSettlementElectricFee;

        @ApiModelProperty(value = "结算电费单位", example = "万元")
        private String settlementElectricFeeUnit = "万元";

        @ApiModelProperty(value = "当月实际电量(MWh)", example = "11800.30")
        private BigDecimal currentMonthPower;

        @ApiModelProperty(value = "当月计划电量(MWh)", example = "12000.00")
        private BigDecimal currentMonthPlanPower;

        @ApiModelProperty(value = "年度数据列表 (年度查询时返回)", notes = "包含12个月的详细数据")
        private List<MonthlyTradingDataDTO> monthlyDataList;

        @ApiModelProperty(value = "记录数量", example = "15")
        private Integer recordCount;

        @ApiModelProperty(value = "查询年份", example = "2024")
        private String year;

        @ApiModelProperty(value = "查询月份", example = "07")
        private String month;
    }

    /**
     * 月度交易数据DTO
     */
    @Data
    @ApiModel(value = "月度交易数据DTO", description = "单月的交易数据详情")
    public static class MonthlyTradingDataDTO {

        @ApiModelProperty(value = "年份", required = true, example = "2024")
        private String year;

        @ApiModelProperty(value = "月份", required = true, example = "01")
        private String month;

        @ApiModelProperty(value = "月度结算电量(MWh)", required = true, example = "1200.50")
        private BigDecimal totalElectricity;

        @ApiModelProperty(value = "月度当月电量(MWh)", example = "1150.30")
        private BigDecimal currentMonthPower;

        @ApiModelProperty(value = "月度交易均价(元/MWh)", required = true, example = "380.25")
        private BigDecimal averagePrice;

        @ApiModelProperty(value = "月度记录数量", example = "5")
        private Integer recordCount;

        @ApiModelProperty(value = "月度结算电费(万元)", example = "456.30")
        private BigDecimal totalSettlementElectricFee;
    }
}
