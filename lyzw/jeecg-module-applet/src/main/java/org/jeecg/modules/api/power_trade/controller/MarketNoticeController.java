package org.jeecg.modules.api.power_trade.controller;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power_trade.entity.MarketNotice;
import org.jeecg.modules.api.power_trade.entity.NoticeType;
import org.jeecg.modules.api.power_trade.service.MarketNoticeService;
import org.jeecg.modules.api.power_trade.service.MultiDataSourceAggregationService;
import org.jeecg.modules.api.power_trade.service.PolicyFileService;
import org.jeecg.modules.api.power_trade.util.ParamValidationUtil;
import org.jeecg.modules.api.power_trade.vo.MarketNoticeVO;
import org.jeecg.modules.api.power_trade.vo.PolicyAnnouncementVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 市场公告接口
 */
@RestController
@RequestMapping("/api/power_trade/market_notice")
@Api(tags = "公告接口")
@Slf4j
public class MarketNoticeController {

    @Autowired
    private MarketNoticeService marketNoticeService;

    @Autowired
    private MultiDataSourceAggregationService multiDataSourceAggregationService;

    @Autowired
    private PolicyFileService policyFileService;

    /**
     * 分页获取市场公告
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页获取市场公告", notes = "分页获取市场公告列表")
    public Result<IPage<MarketNoticeVO>> getAnnouncementList(
            @ApiParam(value = "页码") @RequestParam(defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页条数") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "省份ID") @RequestParam(defaultValue = "0") Integer provinceId,
            @ApiParam(value = "公告类型ID") @RequestParam(required = false) Integer typeId,
            @ApiParam(value = "搜索关键词") @RequestParam(required = false) String keyword) {

        // 参数验证
        Result<Void> validationResult = ParamValidationUtil.validateVoid(() -> {
            ParamValidationUtil.Validator.create()
                    .validatePagination(pageNo, pageSize)
                    .validateProvinceId(provinceId);
        });
        if (!validationResult.isSuccess()) {
            return Result.error(validationResult.getMessage());
        }

        // 检查是否为全国数据源汇总
        if (provinceId == 0) {
            // 全国数据源汇总模式 - 优雅实现
            IPage<MarketNoticeVO> aggregatedResult = queryAllProvincesMarketNotices(pageNo, pageSize, typeId, keyword);
            return Result.OK(aggregatedResult);
        }

        // 单省份模式
        Page<MarketNotice> page = new Page<>(pageNo, pageSize);
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return Result.error("不支持的省份ID");
        }
        DynamicDataSourceContextHolder.push(dsKey);
        try {
            IPage<MarketNoticeVO> pageList = marketNoticeService.getAnnouncementList(page, typeId, provinceId, keyword);
            return Result.OK(pageList);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 获取公告详情
     */
    @GetMapping("/detail/{id}")
    @ApiOperation(value = "获取公告详情", notes = "根据ID获取公告详细信息")
    public Result<MarketNoticeVO> getAnnouncementDetail(
            @ApiParam(value = "省份ID") @RequestParam(defaultValue = "0", required = false) Integer provinceId,
            @ApiParam(value = "公告ID", required = true) @PathVariable Long id) {

        // 参数验证
        Result<Void> validationResult = ParamValidationUtil.validateVoid(() -> {
            ParamValidationUtil.Validator.create()
                    .validateProvinceId(provinceId)
                    .validateRequired(id, "公告ID");
        });
        if (!validationResult.isSuccess()) {
            return Result.error(validationResult.getMessage());
        }

        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return Result.error("不支持该省份ID");
        }
        DynamicDataSourceContextHolder.push(dsKey);
        LambdaQueryWrapper<MarketNotice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketNotice::getId, id);
        String provinceName = ProvinceDataSourceUtil.getProvinceName(provinceId);
        List<String> tags = new ArrayList<>();
        tags.add(provinceName);
        MarketNoticeVO detail = marketNoticeService.getAnnouncementDetail(id);
        if (detail == null) {
            return Result.error("未找到对应的公告信息");
        }
        detail.setTags(tags);
        return Result.OK(detail);
    }

    /**
     * 根据类型获取公告列表
     */
    @GetMapping("/type/{typeId}")
    @ApiOperation(value = "根据类型获取公告列表", notes = "获取指定类型的最新公告")
    public Result<List<MarketNoticeVO>> getAnnouncementsByType(
            @ApiParam(value = "省份ID") @RequestParam(defaultValue = "0") Integer provinceId,
            @ApiParam(value = "公告类型ID", required = true) @PathVariable Integer typeId,
            @ApiParam(value = "返回数量", required = true) @RequestParam(defaultValue = "5") Integer limit) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return Result.error("不支持该省份ID");
        }
        DynamicDataSourceContextHolder.push(dsKey);
        List<MarketNoticeVO> list = marketNoticeService.getAnnouncementsByType(typeId, limit);
        return Result.OK(list);
    }


    /**
     * 获取分享信息
     */
    @GetMapping("/shareInfo/{id}")
    @ApiOperation(value = "获取分享信息", notes = "获取公告的分享信息")
    public Result<Map<String, Object>> getShareInfo(
            @PathVariable Long id,
            @RequestParam Integer provinceId) {

        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        DynamicDataSourceContextHolder.push(dsKey);

        try {
            MarketNoticeVO notice = marketNoticeService.getAnnouncementDetail(id);
            if (notice == null) {
                return Result.error("公告不存在");
            }

            Map<String, Object> shareInfo = new HashMap<>();
            shareInfo.put("title", notice.getNoticeTitle());
            shareInfo.put("description", notice.getSummary());
            shareInfo.put("url", "/notice/detail/" + id + "?provinceId=" + provinceId);
            shareInfo.put("cover", notice.getCoverUrl());
            shareInfo.put("tags", notice.getTags());

            return Result.OK(shareInfo);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 获取所有公告类型
     */
    @GetMapping("/types")
    @ApiOperation(value = "获取所有公告类型", notes = "获取所有公告类型列表")
    public Result<List<NoticeType>> getAllNoticeTypes(
            @ApiParam(value = "省份ID") @RequestParam(defaultValue = "0") Integer provinceId
    ) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return Result.error("不支持该省份ID");
        }
        DynamicDataSourceContextHolder.push(dsKey);
        List<NoticeType> types = marketNoticeService.getAllNoticeTypesList();
        return Result.OK(types);
    }

    public IPage<MarketNoticeVO> queryAllProvincesMarketNotices(int pageNo, int pageSize, Integer typeId, String keyword) {
        log.info("开始汇总查询全国公告列表 - 页码: {}, 每页条数: {}, 类型ID: {}, 关键词: {}", pageNo, pageSize, typeId, keyword);

        Map<Integer, String> provinceDataSourceMap = ProvinceDataSourceUtil.getAllProvinceDataSource();

        List<MarketNoticeVO> allNotices = new ArrayList<>();

        List<Integer> supportedProvinces = provinceDataSourceMap.keySet().stream()
                .filter(provinceId -> provinceId != 0) // 排除全国汇总
                .sorted()
                .collect(Collectors.toList());

        for (Integer provinceId : supportedProvinces) {
            String dsKey = provinceDataSourceMap.get(provinceId);
            if (dsKey == null) continue;

            try {
                DynamicDataSourceContextHolder.push(dsKey);

                Page<MarketNotice> tempPage = new Page<>(1, Integer.MAX_VALUE); // 拉取所有数据
                IPage<MarketNoticeVO> provinceResult = marketNoticeService.getAnnouncementList(tempPage, typeId, provinceId, keyword);
                if (provinceResult != null && provinceResult.getRecords() != null) {
                    allNotices.addAll(new ArrayList<>(provinceResult.getRecords()));
                }

            } catch (Exception e) {
                log.warn("查询省份{}的公告列表失败: {}", provinceId, e.getMessage(), e);
            } finally {
                DynamicDataSourceContextHolder.clear();
            }
        }

        // 全部数据按时间降序排
        allNotices.sort(Comparator.comparing(MarketNoticeVO::getNoticeDate, Comparator.nullsLast(Comparator.reverseOrder())));

        // 内存分页
        int total = allNotices.size();
        int fromIndex = Math.min((pageNo - 1) * pageSize, total);
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<MarketNoticeVO> pageRecords = allNotices.subList(fromIndex, toIndex);

        IPage<MarketNoticeVO> resultPage = new Page<>(pageNo, pageSize, total);
        resultPage.setRecords(pageRecords);

        log.info("全国公告汇总完成 - 总条数: {}, 当前页: {}, 当前条数: {}", total, pageNo, pageRecords.size());

        return resultPage;
    }

    @GetMapping("/policy/list")
    @ApiOperation(value = "分页获取政策公告", notes = "分页获取政策公告列表")
    public Result<IPage<PolicyAnnouncementVO>> getPolicyAnnouncementList(
            @ApiParam(value = "页码") @RequestParam(defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页条数") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "省份ID") @RequestParam(defaultValue = "0") Integer provinceId,
            @ApiParam(value = "搜索关键词") @RequestParam(required = false) String keyword){
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return Result.error("不支持该省份ID");
        }
        DynamicDataSourceContextHolder.push(dsKey);
        try{
            IPage<PolicyAnnouncementVO> pageList =
            return Result.OK(pageList);
        }
        finally {
            DynamicDataSourceContextHolder.clear();
        }
    }
}