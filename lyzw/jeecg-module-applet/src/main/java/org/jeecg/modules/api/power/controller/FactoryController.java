package org.jeecg.modules.api.power.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.api.power.entity.StationForecastFactory;
import org.jeecg.modules.api.power.mapper.StationForecastFactoryMapper;
import org.jeecg.modules.api.power.service.impl.PowerService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/bi/factory")
public class FactoryController {

    @Resource
    private StationForecastFactoryMapper stationForecastFactoryMapper;

    @Resource
    private PowerService powerService;

    @GetMapping("/queryFactoryByStationId")
    public Result<List<StationForecastFactory>> queryFactoryByStationId(@RequestParam Long stationId) {
        List<StationForecastFactory> stationFactoryList = stationForecastFactoryMapper.selectList(Wrappers.<StationForecastFactory>lambdaQuery()
                .eq(StationForecastFactory::getStationId, stationId));
        for (StationForecastFactory stationForecastFactory : stationFactoryList) {
            stationForecastFactory.setFactoryName(powerService.queryFactoryNameById(stationForecastFactory.getFactoryId()));
        }
        return Result.OK(stationFactoryList);
    }
}
