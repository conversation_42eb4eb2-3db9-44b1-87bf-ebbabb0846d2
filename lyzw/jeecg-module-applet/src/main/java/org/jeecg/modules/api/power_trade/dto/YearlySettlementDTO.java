package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@ApiModel("年度结算数据DTO")
@Data
@AllArgsConstructor
public class YearlySettlementDTO {
    @ApiModelProperty(value = "总结算电量(MWh)", notes = "全年累计结算电量")
    private Double totalSettlePower;

    @ApiModelProperty(value = "平均结算价格(元/MWh)", notes = "全年结算价格平均值")
    private Double averageSettlementPrice;

    @ApiModelProperty(value = "月份数量", notes = "有结算数据的月份数量")
    private Integer monthCount;
}