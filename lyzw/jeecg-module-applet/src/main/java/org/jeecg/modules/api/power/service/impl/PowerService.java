package org.jeecg.modules.api.power.service.impl;

import cn.hutool.core.util.StrUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.api.power.dto.DataDto;
import org.jeecg.modules.api.power.entity.ForecastPowerFactory;
import org.jeecg.modules.api.power.entity.StationForecastFactory;
import org.jeecg.modules.api.power.mapper.ForecastPowerFactoryMapper;
import org.jeecg.modules.api.power.mapper.StationForecastFactoryMapper;
import org.jeecg.modules.api.power.param.CommonPowerDto;
import org.jeecg.modules.api.power.param.PowerGenerationTrendDto;
import org.jeecg.modules.api.power.param.PowerGenerationTrendQueryParam;
import org.jeecg.modules.api.power.param.PowerQueryParam;
import org.jeecg.modules.api.power.service.PowerStrategy;
import org.jeecg.modules.api.power.service.PowerStrategyFactory;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.service.StationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PowerService {

    @Resource
    private PowerStrategyFactory strategyFactory;

    @Resource
    private StationForecastFactoryMapper stationForecastFactoryMapper;

    @Resource
    private ForecastPowerFactoryMapper forecastPowerFactoryMapper;

    @Autowired
    private StationService stationService;

    private final Map<Integer, String> FACTORY_ID_TO_NAME = new HashMap<>();

    private final List<StationForecastFactory> FORECAST_FACTORY_LIST = new ArrayList<>();

    /**
     * 初始化厂家id->厂家名称的map
     */
    @PostConstruct
    private void init() {
        try {
            log.info("开始初始化功率预测服务...");
            
            // 获取厂家id->厂家名称的map
            try {
                List<ForecastPowerFactory> factoryList = forecastPowerFactoryMapper.selectList(null);
                if (CollectionUtils.isNotEmpty(factoryList)) {
                    Map<Integer, String> collect = factoryList.stream()
                            .collect(Collectors.toMap(ForecastPowerFactory::getId, ForecastPowerFactory::getFactoryName));
                    FACTORY_ID_TO_NAME.putAll(collect);
                    log.info("加载功率预测厂家数据: {} 条", factoryList.size());
                } else {
                    log.warn("未找到功率预测厂家数据，将使用默认厂家");
                    // 如果没有数据，添加一个默认厂家
                    FACTORY_ID_TO_NAME.put(0, "默认厂家");
                }
            } catch (Exception e) {
                log.error("加载功率预测厂家数据失败: {}", e.getMessage());
                // 添加一个默认厂家以确保系统可以继续运行
                FACTORY_ID_TO_NAME.put(0, "默认厂家");
            }
            
            // 获取电站厂家关联数据
            try {
                List<StationForecastFactory> forecastFactoryList = stationForecastFactoryMapper.selectList(null);
                if (CollectionUtils.isNotEmpty(forecastFactoryList)) {
                    FORECAST_FACTORY_LIST.addAll(forecastFactoryList);
                    log.info("加载电站预测厂家关联数据: {} 条", forecastFactoryList.size());
                } else {
                    log.warn("未找到电站预测厂家关联数据");
                }
            } catch (Exception e) {
                log.error("加载电站预测厂家关联数据失败: {}", e.getMessage());
                log.warn("系统将在表创建后自动加载数据，或者请手动导入数据");
            }
            
            log.info("功率预测服务初始化完成");
        } catch (Exception e) {
            log.error("功率预测服务初始化失败: {}", e.getMessage(), e);
            log.warn("系统将使用默认配置继续运行");
            // 添加默认厂家以确保系统可以继续运行
            if (FACTORY_ID_TO_NAME.isEmpty()) {
                FACTORY_ID_TO_NAME.put(0, "默认厂家");
            }
        }
    }

    /**
     * 根据时间范围查询功率预测
     *
     * @param param 查询参数
     * @return 电站id->(厂家id->预测结果)
     */
    public Map<Long, Map<Integer, List<CommonPowerDto>>> queryPowerPredictionByRange(PowerQueryParam param) {
        // 获取电站id->厂家id列表的map
        Map<Long, List<Integer>> stationIdToFactoryIds = queryStationIdToFactoryIds(param);
        // 根据不同厂家查询功率预测
        Map<Long, Map<Integer, List<CommonPowerDto>>> result = new HashMap<>();
        stationIdToFactoryIds.forEach((stationId, factoryIds) ->
                factoryIds.forEach(factoryId -> {
                    PowerStrategy strategy = strategyFactory.getStrategy(factoryId);
                    param.setStationId(stationId);
                    List<CommonPowerDto> powerDtoList = strategy.queryPowerPredictionByRange(param);
                    if (CollectionUtils.isNotEmpty(powerDtoList)) {
                        // 功率数据不为空就将当前厂家的预测结果放入map中
                        Map<Integer, List<CommonPowerDto>> factoryIdToData = result.computeIfAbsent(stationId, k -> new HashMap<>());
                        factoryIdToData.put(factoryId, powerDtoList);
                    }
                })
        );
        return result;
    }

    /**
     * 根据时间范围查询实际功率
     *
     * @param param 查询参数
     * @return 电站id->(厂家id->实际功率)
     */
    public Map<Long, Map<Integer, List<CommonPowerDto>>> queryPowerRealByRange(PowerQueryParam param) {
        // 获取电站id->厂家id列表的map
        Map<Long, List<Integer>> stationIdToFactoryIds = queryStationIdToFactoryIds(param);
        // 根据不同厂家查询功率
        Map<Long, Map<Integer, List<CommonPowerDto>>> result = new HashMap<>();
        stationIdToFactoryIds.forEach((stationId, factoryIds) ->
                factoryIds.forEach(factoryId -> {
                    PowerStrategy strategy = strategyFactory.getStrategy(factoryId);
                    param.setStationId(stationId);
                    List<CommonPowerDto> powerDtoList = strategy.queryPowerRealByRange(param);
                    if (CollectionUtils.isNotEmpty(powerDtoList)) {
                        // 功率数据不为空就将当前厂家的预测结果放入map中
                        Map<Integer, List<CommonPowerDto>> factoryIdToData = result.computeIfAbsent(stationId, k -> new HashMap<>());
                        factoryIdToData.put(factoryId, powerDtoList);
                    }
                })
        );
        return result;
    }

    /**
     * 根据日期列表查询功率预测
     *
     * @param param 查询参数
     * @return 电站id->(厂家id->预测结果)
     */
    public Map<Long, Map<Integer, List<CommonPowerDto>>> queryPowerPredictionByDateList(PowerQueryParam param) {
        // 获取电站id->厂家id列表的map
        Map<Long, List<Integer>> stationIdToFactoryIds = queryStationIdToFactoryIds(param);
        // 根据不同厂家查询功率预测
        Map<Long, Map<Integer, List<CommonPowerDto>>> result = new HashMap<>();
        stationIdToFactoryIds.forEach((stationId, factoryIds) ->
                factoryIds.forEach(factoryId -> {
                    PowerStrategy strategy = strategyFactory.getStrategy(factoryId);
                    param.setStationId(stationId);
                    List<CommonPowerDto> powerDtoList = strategy.queryPowerPredictionByDateList(param);
                    if (CollectionUtils.isNotEmpty(powerDtoList)) {
                        // 功率数据不为空就将当前厂家的预测结果放入map中
                        Map<Integer, List<CommonPowerDto>> factoryIdToData = result.computeIfAbsent(stationId, k -> new HashMap<>());
                        factoryIdToData.put(factoryId, powerDtoList);
                    }
                })
        );
        return result;
    }

    /**
     * 根据日期列表查询实际功率
     *
     * @param param 查询参数
     * @return 电站id->(厂家id->实际功率)
     */
    public Map<Long, Map<Integer, List<CommonPowerDto>>> queryPowerRealByDateList(PowerQueryParam param) {
        // 获取电站id->厂家id列表的map
        Map<Long, List<Integer>> stationIdToFactoryIds = queryStationIdToFactoryIds(param);
        // 根据不同厂家查询功率
        Map<Long, Map<Integer, List<CommonPowerDto>>> result = new HashMap<>();
        stationIdToFactoryIds.forEach((stationId, factoryIds) ->
                factoryIds.forEach(factoryId -> {
                    PowerStrategy strategy = strategyFactory.getStrategy(factoryId);
                    param.setStationId(stationId);
                    List<CommonPowerDto> powerDtoList = strategy.queryPowerRealByRangeDateList(param);
                    if (CollectionUtils.isNotEmpty(powerDtoList)) {
                        // 功率数据不为空就将当前厂家的预测结果放入map中
                        Map<Integer, List<CommonPowerDto>> factoryIdToData = result.computeIfAbsent(stationId, k -> new HashMap<>());
                        factoryIdToData.put(factoryId, powerDtoList);
                    }
                })
        );
        return result;
    }

    public String queryMaxVersion(PowerQueryParam param) {
        if (param.getStationId() == null || param.getFactoryId() == null
                || StringUtils.isEmpty(param.getDate())) {
            throw new RuntimeException("参数错误！");
        }
        return strategyFactory.getStrategy(param.getFactoryId()).queryMaxVersion(param);
    }

    /**
     * 根据通用功率数据构建功率曲线
     *
     * @param startDate            开始日期
     * @param endDate              结束日期
     * @param factoryIdToPowerData 厂家id->通用功率数据
     * @param dataType             数据类型
     * @return 功率曲线数据
     */
    public List<DataDto> buildCurveData(String startDate, String endDate, Map<Integer, List<CommonPowerDto>> factoryIdToPowerData, String dataType) {
        Set<Integer> factoryIds = factoryIdToPowerData.keySet();
        if (CollectionUtils.isEmpty(factoryIds)) {
            throw new JeecgBootException("当前电站无功率预测厂家");
        }

        // 构造返回数据
        List<DataDto> results = new ArrayList<>(factoryIds.size());
        factoryIdToPowerData.forEach((factoryId, powerDtoList) -> {
            DataDto result = new DataDto();
            result.setDataType(dataType + StrUtil.DASHED + queryFactoryNameById(factoryId));
            for (CommonPowerDto commonPowerDto : powerDtoList) {
                String time = commonPowerDto.getDate() + StrUtil.SPACE + commonPowerDto.getTime();
                if (Objects.equals(startDate, endDate)) {
                    time = commonPowerDto.getTime();
                }
                result.getTimes().add(time);
                result.getValues().add(commonPowerDto.getValue());
            }
            result.setValues(result.getValues());
            results.add(result);
        });
        return results;
    }

    /**
     * 根据通用功率数据构建实际功率曲线
     *
     * @param startDate            开始日期
     * @param endDate              结束日期
     * @param factoryIdToPowerData 厂家id->通用功率数据
     * @param dataType             数据类型
     * @return 实际功率曲线数据
     */
    public List<DataDto> buildRealCurveData(String startDate, String endDate, Map<Integer, List<CommonPowerDto>> factoryIdToPowerData, String dataType) {
        Set<Integer> factoryIds = factoryIdToPowerData.keySet();
        if (CollectionUtils.isEmpty(factoryIds)) {
            throw new JeecgBootException("当前电站无功率预测厂家");
        }

        // 构造返回数据
        List<DataDto> results = new ArrayList<>();
        DataDto nullPowerData = new DataDto();
        for (Map.Entry<Integer, List<CommonPowerDto>> entry : factoryIdToPowerData.entrySet()) {
            List<CommonPowerDto> powerList = entry.getValue();

            // 找到第一个有数据的功率预测厂家的实际上网数据返回
            boolean isNull = false;
            DataDto result = new DataDto();
            result.setDataType(dataType);
            for (CommonPowerDto commonPowerDto : powerList) {
                if (Objects.isNull(commonPowerDto.getValue())) {
                    isNull = true;
                }
                String time = commonPowerDto.getDate() + StrUtil.SPACE + commonPowerDto.getTime();
                if (Objects.equals(startDate, endDate)) {
                    time = commonPowerDto.getTime();
                }
                result.getTimes().add(time);
                result.getValues().add(commonPowerDto.getValue());
            }
            nullPowerData = result;
            if (!isNull) {
                results.add(result);
                result.setValues(result.getValues());
                return results;
            }
        }
        // 如果没找到有数据的实际上网数据，则返回空数据
        results.add(nullPowerData);
        return results;
    }

    /**
     * 根据厂家id查询厂家名称
     *
     * @param factoryId 厂家id列表
     * @return 厂家名称
     */
    public String queryFactoryNameById(Integer factoryId) {
        return FACTORY_ID_TO_NAME.get(factoryId);
    }

    /**
     * 获取电站id->厂家id的列表
     *
     * @param param 查询参数
     * @return 电站id->厂家id的列表
     */
    private Map<Long, List<Integer>> queryStationIdToFactoryIds(PowerQueryParam param) {
        if (CollectionUtils.isEmpty(param.getStationIds())) {
            return new HashMap<>();
        }
        
        Map<Long, List<Integer>> result;
        
        // 如果没有预测厂家数据，为每个电站分配默认厂家
        if (CollectionUtils.isEmpty(FORECAST_FACTORY_LIST)) {
            log.warn("未找到电站预测厂家关联数据，将为每个电站使用默认厂家(ID=0)");
            result = new HashMap<>();
            for (Long stationId : param.getStationIds()) {
                List<Integer> factoryIds = new ArrayList<>();
                factoryIds.add(0); // 默认厂家ID
                result.put(stationId, factoryIds);
            }
            return result;
        }
        
        // 正常情况：获取电站id->厂家id列表的map
        result = FORECAST_FACTORY_LIST.stream()
                .filter(item ->
                        param.getStationIds().contains(item.getStationId()) &&  // 匹配电站id列表
                                (param.getFactoryId() == null || Objects.equals(item.getFactoryId(), param.getFactoryId()))  // 厂家id条件
                )
                .collect(Collectors.groupingBy(StationForecastFactory::getStationId, 
                        Collectors.mapping(StationForecastFactory::getFactoryId, Collectors.toList())));
        
        // 检查是否有电站没有找到对应的厂家，如果有，为其分配默认厂家
        if (result.size() < param.getStationIds().size()) {
            for (Long stationId : param.getStationIds()) {
                if (!result.containsKey(stationId)) {
                    List<Integer> factoryIds = new ArrayList<>();
                    factoryIds.add(0); // 默认厂家ID
                    result.put(stationId, factoryIds);
                    log.warn("电站 {} 未找到关联的预测厂家，将使用默认厂家(ID=0)", stationId);
                }
            }
        }
        
        return result;
    }

    /**
     * 获取发电趋势数据
     *
     * @param param 查询参数
     * @return 发电趋势数据列表
     */
    public List<PowerGenerationTrendDto> getPowerGenerationTrend(PowerGenerationTrendQueryParam param) {
        List<PowerGenerationTrendDto> result = new ArrayList<>();

        try {
            // 参数验证
            if (param == null) {
                log.error("查询参数不能为空");
                return result;
            }

            if (param.getStationId() == null) {
                log.error("电站ID不能为空");
                return result;
            }

            // 根据时间维度构建查询参数
            PowerQueryParam powerQueryParam = buildPowerQueryParam(param);
            if (powerQueryParam == null) {
                log.error("构建查询参数失败");
                return result;
            }

            // 查询实际功率数据
            Map<Long, Map<Integer, List<CommonPowerDto>>> powerData = queryPowerRealByRange(powerQueryParam);

            if (powerData == null || powerData.isEmpty()) {
                log.warn("未查询到功率数据，电站ID: {}，将生成0值数据", param.getStationId());
                // 生成0值数据
                result = generateZeroPowerGenerationTrend(param);
                return result;
            }

            // 获取指定电站的功率数据
            Map<Integer, List<CommonPowerDto>> stationPowerData = powerData.get(param.getStationId());
            if (stationPowerData == null || stationPowerData.isEmpty()) {
                log.warn("未查询到电站功率数据，电站ID: {}，将生成0值数据", param.getStationId());
                // 生成0值数据
                result = generateZeroPowerGenerationTrend(param);
                return result;
            }

            // 转换为发电趋势数据
            result = convertToPowerGenerationTrend(stationPowerData, param);

            log.info("成功获取发电趋势数据，电站ID: {}, 时间维度: {}, 数据条数: {}",
                    param.getStationId(), param.getTimeDimension(), result.size());

        } catch (Exception e) {
            log.error("获取发电趋势数据失败，电站ID: {}, 错误: {}", param.getStationId(), e.getMessage(), e);
        }

        return result;
    }

    /**
     * 构建功率查询参数
     */
    private PowerQueryParam buildPowerQueryParam(PowerGenerationTrendQueryParam param) {
        PowerQueryParam powerQueryParam = new PowerQueryParam();

        // 设置单个电站查询
        powerQueryParam.setStationId(param.getStationId());
        List<Long> stationIds = new ArrayList<>();
        stationIds.add(param.getStationId());
        powerQueryParam.setStationIds(stationIds);

        String queryDate = param.getQueryDate();
        String timeDimension = param.getTimeDimension();

        if ("3".equals(timeDimension)) {
            // 日维度：查询指定日期
            powerQueryParam.setStartDate(queryDate);
            powerQueryParam.setEndDate(queryDate);
        } else if ("2".equals(timeDimension)) {
            // 月维度：查询整个月的数据
            String startDate = queryDate + "-01";
            String endDate = getMonthEndDate(queryDate);
            powerQueryParam.setStartDate(startDate);
            powerQueryParam.setEndDate(endDate);
        } else if ("1".equals(timeDimension)) {
            // 年维度：查询整年的数据
            String startDate = queryDate + "-01-01";
            String endDate = queryDate + "-12-31";
            powerQueryParam.setStartDate(startDate);
            powerQueryParam.setEndDate(endDate);
        } else {
            log.error("不支持的时间维度: {}，只支持 1(年)、2(月)、3(日)", timeDimension);
            return null;
        }

        log.info("构建功率查询参数 - 电站ID: {}, 时间维度: {}, 开始日期: {}, 结束日期: {}",
                param.getStationId(), timeDimension, powerQueryParam.getStartDate(), powerQueryParam.getEndDate());

        return powerQueryParam;
    }

    /**
     * 获取月份的最后一天
     */
    private String getMonthEndDate(String yearMonth) {
        try {
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // 计算该月的最后一天
            int lastDay;
            if (month == 2) {
                // 二月份，需要判断闰年
                lastDay = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)) ? 29 : 28;
            } else if (month == 4 || month == 6 || month == 9 || month == 11) {
                lastDay = 30;
            } else {
                lastDay = 31;
            }

            return String.format("%04d-%02d-%02d", year, month, lastDay);
        } catch (Exception e) {
            log.error("计算月末日期失败: {}", yearMonth, e);
            return yearMonth + "-31"; // 默认返回31号
        }
    }

    /**
     * 转换为发电趋势数据
     */
    private List<PowerGenerationTrendDto> convertToPowerGenerationTrend(
            Map<Integer, List<CommonPowerDto>> stationPowerData, PowerGenerationTrendQueryParam param) {

        List<PowerGenerationTrendDto> result = new ArrayList<>();
        String timeDimension = param.getTimeDimension();

        // 合并所有厂家的数据
        List<CommonPowerDto> allPowerData = new ArrayList<>();
        for (List<CommonPowerDto> powerDataList : stationPowerData.values()) {
            allPowerData.addAll(powerDataList);
        }

        if (allPowerData.isEmpty()) {
            return result;
        }

        // 根据时间维度进行数据聚合
        if ("3".equals(timeDimension)) {
            // 日维度：显示一天96个点（每隔15分钟）
            result = aggregateDailyData(allPowerData);
        } else if ("2".equals(timeDimension)) {
            // 月维度：显示当月每天的发电量
            result = aggregateMonthlyData(allPowerData, param.getQueryDate());
        } else if ("1".equals(timeDimension)) {
            // 年维度：显示当年每个月的发电量
            result = aggregateYearlyData(allPowerData, param.getQueryDate());
        }

        return result;
    }

    /**
     * 聚合日维度数据 - 显示一天96个点（每隔15分钟）
     * 实现补零功能：确保始终返回96个时间点的完整数据
     */
    private List<PowerGenerationTrendDto> aggregateDailyData(List<CommonPowerDto> powerDataList) {
        List<PowerGenerationTrendDto> result = new ArrayList<>();

        // 创建时间点到数据的映射
        Map<String, CommonPowerDto> timeDataMap = new HashMap<>();
        for (CommonPowerDto powerDto : powerDataList) {
            timeDataMap.put(powerDto.getTime(), powerDto);
        }

        // 生成完整的96个时间点（24小时 × 4个15分钟间隔）
        for (int i = 0; i < 96; i++) {
            int hour = i / 4;
            int minute = (i % 4) * 15;
            String timeLabel = String.format("%02d:%02d", hour, minute);

            PowerGenerationTrendDto trendDto = new PowerGenerationTrendDto();
            trendDto.setTimeLabel(timeLabel);

            // 检查该时间点是否有数据
            CommonPowerDto powerData = timeDataMap.get(timeLabel);
            if (powerData != null) {
                // 有数据：使用实际数据
                trendDto.setActualPower(powerData.getValue());
                trendDto.setPowerGeneration(powerData.getValue() != null ? powerData.getValue() / 4.0 : 0.0);
            } else {
                // 无数据：填充零值
                trendDto.setActualPower(0.0);
                trendDto.setPowerGeneration(0.0);
            }

            result.add(trendDto);
        }

        log.debug("日维度数据补零完成 - 总数据点: {}, 实际数据点: {}, 补零数据点: {}",
                result.size(), timeDataMap.size(), result.size() - timeDataMap.size());

        return result;
    }

    /**
     * 聚合月维度数据 - 显示当月每天的发电量
     * 实现补零功能：确保返回当月所有天数的完整数据
     */
    private List<PowerGenerationTrendDto> aggregateMonthlyData(List<CommonPowerDto> powerDataList, String queryDate) {
        List<PowerGenerationTrendDto> result = new ArrayList<>();

        // 按日期分组，计算每天的发电量总和
        Map<String, Double> dailyPowerMap = new HashMap<>();

        for (CommonPowerDto powerDto : powerDataList) {
            String date = powerDto.getDate();
            Double power = powerDto.getValue();
            if (power != null) {
                // 累加每天的功率值，最后除以4得到发电量
                dailyPowerMap.merge(date, power / 4.0, Double::sum);
            }
        }

        // 获取当月的天数
        int daysInMonth = getDaysInMonth(queryDate);
        String yearMonth = queryDate.substring(0, 7); // 提取年月部分，如 "2024-01"

        // 生成当月所有天数的完整数据
        for (int day = 1; day <= daysInMonth; day++) {
            String dayStr = String.format("%02d", day);
            String fullDate = yearMonth + "-" + dayStr; // 如 "2024-01-01"

            PowerGenerationTrendDto trendDto = new PowerGenerationTrendDto();
            trendDto.setTimeLabel(dayStr); // 显示日期，如 "01", "02"

            // 检查该天是否有数据
            Double dailyGeneration = dailyPowerMap.get(fullDate);
            if (dailyGeneration != null) {
                // 有数据：使用实际数据
                trendDto.setActualPower(dailyGeneration * 4.0); // 反推实际功率用于显示
                trendDto.setPowerGeneration(dailyGeneration); // 发电量
            } else {
                // 无数据：填充零值
                trendDto.setActualPower(0.0);
                trendDto.setPowerGeneration(0.0);
            }

            result.add(trendDto);
        }

        log.debug("月维度数据补零完成 - 查询月份: {}, 总天数: {}, 实际数据天数: {}, 补零天数: {}",
                queryDate, daysInMonth, dailyPowerMap.size(), daysInMonth - dailyPowerMap.size());

        return result;
    }

    /**
     * 聚合年维度数据 - 显示当年每个月的发电量
     * 实现补零功能：确保始终返回12个月的完整数据
     */
    private List<PowerGenerationTrendDto> aggregateYearlyData(List<CommonPowerDto> powerDataList, String queryDate) {
        List<PowerGenerationTrendDto> result = new ArrayList<>();

        // 按月份分组，计算每月的发电量总和
        Map<String, Double> monthlyPowerMap = new HashMap<>();

        for (CommonPowerDto powerDto : powerDataList) {
            String date = powerDto.getDate();
            String month = date.substring(5, 7); // 提取月份
            Double power = powerDto.getValue();
            if (power != null) {
                // 累加每月的功率值，最后除以4得到发电量
                monthlyPowerMap.merge(month, power / 4.0, Double::sum);
            }
        }

        // 生成完整的12个月数据
        for (int month = 1; month <= 12; month++) {
            String monthStr = String.format("%02d", month);

            PowerGenerationTrendDto trendDto = new PowerGenerationTrendDto();
            trendDto.setTimeLabel(monthStr); // 显示月份，如 "01", "02"

            // 检查该月是否有数据
            Double monthlyGeneration = monthlyPowerMap.get(monthStr);
            if (monthlyGeneration != null) {
                // 有数据：使用实际数据
                trendDto.setActualPower(monthlyGeneration * 4.0); // 反推实际功率用于显示
                trendDto.setPowerGeneration(monthlyGeneration); // 发电量
            } else {
                // 无数据：填充零值
                trendDto.setActualPower(0.0);
                trendDto.setPowerGeneration(0.0);
            }

            result.add(trendDto);
        }

        log.debug("年维度数据补零完成 - 查询年份: {}, 总月数: 12, 实际数据月数: {}, 补零月数: {}",
                queryDate, monthlyPowerMap.size(), 12 - monthlyPowerMap.size());

        return result;
    }

    /**
     * 获取指定月份的天数
     * @param queryDate 查询日期，格式为 YYYY-MM 或 YYYY-MM-DD
     * @return 该月的天数
     */
    private int getDaysInMonth(String queryDate) {
        try {
            // 提取年月部分
            String yearMonth = queryDate.length() > 7 ? queryDate.substring(0, 7) : queryDate;
            java.time.YearMonth ym = java.time.YearMonth.parse(yearMonth);
            return ym.lengthOfMonth();
        } catch (Exception e) {
            log.warn("解析月份失败，使用默认31天: {}", queryDate, e);
            return 31; // 默认返回31天
        }
    }

    /**
     * 生成0值的发电趋势数据
     * 根据时间维度生成对应数量的0值数据点
     */
    private List<PowerGenerationTrendDto> generateZeroPowerGenerationTrend(PowerGenerationTrendQueryParam param) {
        List<PowerGenerationTrendDto> zeroDataList = new ArrayList<>();

        String timeDimension = param.getTimeDimension();
        String queryDate = param.getQueryDate();

        try {
            if ("3".equals(timeDimension)) {
                // 日维度：生成96个15分钟数据点（24小时 * 4个点/小时）
                for (int i = 0; i < 96; i++) {
                    int hour = i / 4;
                    int minute = (i % 4) * 15;
                    String timeLabel = String.format("%02d:%02d", hour, minute);

                    PowerGenerationTrendDto dto = new PowerGenerationTrendDto();
                    dto.setTimeLabel(timeLabel);
                    dto.setActualPower(0.0);
                    dto.setPowerGeneration(0.0);

                    zeroDataList.add(dto);
                }
                log.info("生成日维度0值数据 - 数据点数: 96");

            } else if ("2".equals(timeDimension)) {
                // 月维度：生成该月每天的0值数据
                try {
                    YearMonth yearMonth = YearMonth.parse(queryDate);
                    int daysInMonth = yearMonth.lengthOfMonth();

                    for (int day = 1; day <= daysInMonth; day++) {
                        String timeLabel = String.format("%02d", day);

                        PowerGenerationTrendDto dto = new PowerGenerationTrendDto();
                        dto.setTimeLabel(timeLabel);
                        dto.setActualPower(0.0);
                        dto.setPowerGeneration(0.0);

                        zeroDataList.add(dto);
                    }
                    log.info("生成月维度0值数据 - 年月: {}, 数据点数: {}", queryDate, daysInMonth);

                } catch (Exception e) {
                    log.error("解析月份失败: {}", queryDate, e);
                }

            } else if ("1".equals(timeDimension)) {
                // 年维度：生成12个月的0值数据
                for (int month = 1; month <= 12; month++) {
                    String timeLabel = String.format("%02d", month);

                    PowerGenerationTrendDto dto = new PowerGenerationTrendDto();
                    dto.setTimeLabel(timeLabel);
                    dto.setActualPower(0.0);
                    dto.setPowerGeneration(0.0);

                    zeroDataList.add(dto);
                }
                log.info("生成年维度0值数据 - 数据点数: 12");
            }

        } catch (Exception e) {
            log.error("生成0值数据失败", e);
        }

        return zeroDataList;
    }
}
