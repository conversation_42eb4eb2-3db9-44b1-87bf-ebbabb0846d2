package org.jeecg.modules.api.power_trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.power_trade.entity.PolicyAnnouncement;
import org.jeecg.modules.api.power_trade.vo.PolicyAnnouncementVO;

import java.util.List;
import java.util.Map;

/**
 * 政策公告Mapper接口
 */
@Mapper
public interface PolicyAnnouncementMapper extends BaseMapper<PolicyAnnouncement> {

    /**
     * 分页查询政策公告（带类型关联）
     * 
     * @param page 分页参数
     * @param typeId 政策类型ID
     * @param keyword 搜索关键词
     * @param status 状态
     * @return 政策公告分页列表
     */
    IPage<PolicyAnnouncementVO> selectPolicyAnnouncementPage(
        IPage<PolicyAnnouncementVO> page,
        @Param("typeId") Integer typeId,
        @Param("keyword") String keyword,
        @Param("status") Integer status
    );

    /**
     * 根据ID查询政策公告详情（带类型关联）
     * 
     * @param id 政策公告ID
     * @return 政策公告详情
     */
    PolicyAnnouncementVO selectPolicyAnnouncementDetail(@Param("id") Long id);

    /**
     * 根据类型查询政策公告列表
     * 
     * @param typeId 政策类型ID
     * @param limit 限制数量
     * @return 政策公告列表
     */
    List<PolicyAnnouncementVO> selectByType(
        @Param("typeId") Integer typeId,
        @Param("limit") Integer limit
    );

    /**
     * 更新浏览次数
     * 
     * @param id 政策公告ID
     * @return 更新行数
     */
    int updateViewCount(@Param("id") Long id);

    /**
     * 获取政策公告统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> selectStatistics();

    /**
     * 根据关键词搜索政策公告
     * 
     * @param keyword 搜索关键词
     * @param limit 限制数量
     * @return 政策公告列表
     */
    List<PolicyAnnouncementVO> searchByKeyword(
        @Param("keyword") String keyword,
        @Param("limit") Integer limit
    );

    /**
     * 获取最新政策公告
     * 
     * @param limit 限制数量
     * @return 最新政策公告列表
     */
    List<PolicyAnnouncementVO> selectLatest(@Param("limit") Integer limit);

    /**
     * 获取热门政策公告（按浏览次数排序）
     * 
     * @param limit 限制数量
     * @return 热门政策公告列表
     */
    List<PolicyAnnouncementVO> selectPopular(@Param("limit") Integer limit);

    /**
     * 根据发布日期范围查询政策公告
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param typeId 政策类型ID（可选）
     * @return 政策公告列表
     */
    List<PolicyAnnouncementVO> selectByDateRange(
        @Param("startDate") String startDate,
        @Param("endDate") String endDate,
        @Param("typeId") Integer typeId
    );

    /**
     * 获取相关政策公告（基于类型和关键词）
     * 
     * @param id 当前政策公告ID（排除自己）
     * @param typeId 政策类型ID
     * @param keyword 关键词
     * @param limit 限制数量
     * @return 相关政策公告列表
     */
    List<PolicyAnnouncementVO> selectRelated(
        @Param("id") Long id,
        @Param("typeId") Integer typeId,
        @Param("keyword") String keyword,
        @Param("limit") Integer limit
    );

    /**
     * 批量更新政策公告状态
     * 
     * @param ids 政策公告ID列表
     * @param status 状态
     * @return 更新行数
     */
    int batchUpdateStatus(
        @Param("ids") List<Long> ids,
        @Param("status") Integer status
    );

    /**
     * 获取政策公告数量统计（按类型分组）
     * 
     * @return 类型统计列表
     */
    List<Map<String, Object>> selectCountByType();

    /**
     * 获取政策公告数量统计（按月份分组）
     * 
     * @param year 年份
     * @return 月份统计列表
     */
    List<Map<String, Object>> selectCountByMonth(@Param("year") String year);
}
