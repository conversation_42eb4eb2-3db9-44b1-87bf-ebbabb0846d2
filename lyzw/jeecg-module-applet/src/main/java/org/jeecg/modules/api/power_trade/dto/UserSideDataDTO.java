package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户侧数据DTO（购电侧）
 * 包含所有以user前缀的字段，去除前缀后的字段名
 */
@Data
public class UserSideDataDTO {

    @ApiModelProperty(value = "日前偏差电量(单位:万千瓦时)", example = "4749.3001")
    private BigDecimal dayAheadDeviationPower;

    @ApiModelProperty(value = "日前偏差平均电价(单位:元/千瓦时)", example = "10.247881666")
    private BigDecimal dayAheadDeviationAveragePrice;

    @ApiModelProperty(value = "日前偏差电费(单位:万元)", example = "47700.11")
    private BigDecimal dayAheadDeviationFee;

    @ApiModelProperty(value = "实时偏差电量(单位:万千瓦时)", example = "565.7239")
    private BigDecimal realtimeDeviationPower;

    @ApiModelProperty(value = "实时偏差平均电价(单位:元/千瓦时)", example = "290.529215")
    private BigDecimal realtimeDeviationAveragePrice;

    @ApiModelProperty(value = "实时偏差电费(单位:万元)", example = "188473.67")
    private BigDecimal realtimeDeviationFee;

    @ApiModelProperty(value = "总电量(单位:万千瓦时)", example = "null")
    private BigDecimal totalPower;

    @ApiModelProperty(value = "总费用(单位:万元)", example = "null")
    private BigDecimal totalFee;
}
