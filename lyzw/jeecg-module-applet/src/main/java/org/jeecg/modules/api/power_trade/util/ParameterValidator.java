package org.jeecg.modules.api.power_trade.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 统一参数验证工具类
 * 
 * <AUTHOR> Team
 * @since 2025-01-01
 */
public class ParameterValidator {

    /**
     * 验证电站ID
     * 
     * @param stationId 电站ID
     * @throws ValidationException 验证失败时抛出
     */
    public static void validateStationId(Long stationId) {
        if (stationId == null || stationId <= 0) {
            throw new ValidationException("电站ID不能为空且必须大于0");
        }
    }

    /**
     * 验证省份ID
     * 
     * @param provinceId 省份ID
     * @throws ValidationException 验证失败时抛出
     */
    public static void validateProvinceId(Integer provinceId) {
        if (provinceId == null) {
            throw new ValidationException("省份ID不能为空");
        }
        if (provinceId < 0) {
            throw new ValidationException("省份ID不能为负数");
        }
    }

    /**
     * 验证日期参数
     * 
     * @param date 日期字符串
     * @throws ValidationException 验证失败时抛出
     */
    public static void validateDateParameter(String date) {
        if (StringUtils.isBlank(date)) {
            throw new ValidationException("日期参数不能为空");
        }
        if (!isValidDateFormat(date)) {
            throw new ValidationException("日期格式不正确，支持格式：yyyy-MM-dd（具体日期）或 yyyy-MM（年月）");
        }
    }

    /**
     * 验证分页参数
     * 
     * @param pageNo   页码
     * @param pageSize 每页条数
     * @throws ValidationException 验证失败时抛出
     */
    public static void validatePaginationParams(Integer pageNo, Integer pageSize) {
        if (pageNo != null && pageNo < 1) {
            throw new ValidationException("页码必须大于0");
        }
        if (pageSize != null && (pageSize < 1 || pageSize > 1000)) {
            throw new ValidationException("每页条数必须在1-1000之间");
        }
    }

    /**
     * 验证必填参数
     * 
     * @param value     参数值
     * @param fieldName 字段名称
     * @throws ValidationException 验证失败时抛出
     */
    public static void validateRequired(Object value, String fieldName) {
        if (value == null) {
            throw new ValidationException(fieldName + "不能为空");
        }
        if (value instanceof String && StringUtils.isBlank((String) value)) {
            throw new ValidationException(fieldName + "不能为空");
        }
    }

    /**
     * 验证年份格式
     * 
     * @param year 年份字符串
     * @throws ValidationException 验证失败时抛出
     */
    public static void validateYear(String year) {
        validateRequired(year, "年份");
        if (!year.matches("^\\d{4}$")) {
            throw new ValidationException("年份格式错误，应为：yyyy");
        }
    }

    /**
     * 验证月份格式
     * 
     * @param month 月份字符串
     * @throws ValidationException 验证失败时抛出
     */
    public static void validateMonth(String month) {
        validateRequired(month, "月份");
        if (!month.matches("^(0[1-9]|1[0-2])$")) {
            throw new ValidationException("月份格式错误，应为：01-12");
        }
    }

    /**
     * 验证年月格式
     * 
     * @param yearMonth 年月字符串
     * @throws ValidationException 验证失败时抛出
     */
    public static void validateYearMonth(String yearMonth) {
        validateRequired(yearMonth, "年月");
        if (!yearMonth.matches("^\\d{4}-(0[1-9]|1[0-2])$")) {
            throw new ValidationException("年月格式错误，应为：yyyy-MM");
        }
    }

    /**
     * 验证电站类型
     * 
     * @param stationType 电站类型
     * @throws ValidationException 验证失败时抛出
     */
    public static void validateStationType(String stationType) {
        if (StringUtils.isBlank(stationType)) {
            return; // 电站类型可以为空
        }
        
        String normalizedType = stationType.trim().toLowerCase();
        if (!"photovoltaic".equals(normalizedType) && 
            !"wind".equals(normalizedType) && 
            !"storage".equals(normalizedType)) {
            throw new ValidationException(
                "电站类型参数错误，支持的类型：photovoltaic(光伏)、wind(风电)、storage(储能)");
        }
    }

    /**
     * 验证查询维度
     * 
     * @param dimension 查询维度
     * @throws ValidationException 验证失败时抛出
     */
    public static void validateQueryDimension(Integer dimension) {
        if (dimension == null) {
            throw new ValidationException("查询维度不能为空");
        }
        if (dimension != 1 && dimension != 2) {
            throw new ValidationException("查询维度参数错误，支持：1-月度查询，2-年度查询");
        }
    }

    /**
     * 验证数值范围
     * 
     * @param value     数值
     * @param min       最小值
     * @param max       最大值
     * @param fieldName 字段名称
     * @throws ValidationException 验证失败时抛出
     */
    public static void validateRange(Number value, Number min, Number max, String fieldName) {
        if (value == null) {
            return;
        }
        
        double val = value.doubleValue();
        double minVal = min.doubleValue();
        double maxVal = max.doubleValue();
        
        if (val < minVal || val > maxVal) {
            throw new ValidationException(String.format("%s必须在%s-%s之间", fieldName, min, max));
        }
    }

    /**
     * 检查日期格式是否有效
     * 
     * @param date 日期字符串
     * @return 是否有效
     */
    private static boolean isValidDateFormat(String date) {
        if (StringUtils.isBlank(date)) {
            return false;
        }
        
        // 支持 yyyy-MM-dd 格式
        if (date.matches("^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])$")) {
            return true;
        }
        
        // 支持 yyyy-MM 格式
        if (date.matches("^\\d{4}-(0[1-9]|1[0-2])$")) {
            return true;
        }
        
        return false;
    }

    /**
     * 参数验证异常
     */
    public static class ValidationException extends RuntimeException {
        public ValidationException(String message) {
            super(message);
        }
        
        public ValidationException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 链式验证器
     */
    public static class ChainValidator {
        
        public static ChainValidator create() {
            return new ChainValidator();
        }
        
        public ChainValidator validateStationId(Long stationId) {
            ParameterValidator.validateStationId(stationId);
            return this;
        }
        
        public ChainValidator validateProvinceId(Integer provinceId) {
            ParameterValidator.validateProvinceId(provinceId);
            return this;
        }
        
        public ChainValidator validateDateParameter(String date) {
            ParameterValidator.validateDateParameter(date);
            return this;
        }
        
        public ChainValidator validatePaginationParams(Integer pageNo, Integer pageSize) {
            ParameterValidator.validatePaginationParams(pageNo, pageSize);
            return this;
        }
        
        public ChainValidator validateRequired(Object value, String fieldName) {
            ParameterValidator.validateRequired(value, fieldName);
            return this;
        }
        
        public ChainValidator validateYear(String year) {
            ParameterValidator.validateYear(year);
            return this;
        }
        
        public ChainValidator validateMonth(String month) {
            ParameterValidator.validateMonth(month);
            return this;
        }
        
        public ChainValidator validateYearMonth(String yearMonth) {
            ParameterValidator.validateYearMonth(yearMonth);
            return this;
        }
        
        public ChainValidator validateStationType(String stationType) {
            ParameterValidator.validateStationType(stationType);
            return this;
        }
        
        public ChainValidator validateQueryDimension(Integer dimension) {
            ParameterValidator.validateQueryDimension(dimension);
            return this;
        }
        
        public ChainValidator validateRange(Number value, Number min, Number max, String fieldName) {
            ParameterValidator.validateRange(value, min, max, fieldName);
            return this;
        }
    }
}
