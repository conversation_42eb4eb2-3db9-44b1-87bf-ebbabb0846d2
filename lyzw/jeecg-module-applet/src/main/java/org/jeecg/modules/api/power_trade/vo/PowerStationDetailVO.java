package org.jeecg.modules.api.power_trade.vo;

import lombok.Data;
import org.jeecg.modules.api.power_trade.entity.Station;

import java.util.List;

/**
 * 电站详情数据VO
 */
@Data
public class PowerStationDetailVO {
    
    private Station station;  // 电站基本信息
    
    private String regionName;  // 区域名称
    
    private String typeName;  // 类型名称
    
    private String typeIcon;  // 类型图标URL
    
    private Double yearTotalPower;  // 年度交易总电量 (GWh)
    
    private Double yearAvgPrice;  // 年度交易均价 (元/MWh)
} 