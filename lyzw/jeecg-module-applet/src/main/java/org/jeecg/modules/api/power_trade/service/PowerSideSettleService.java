package org.jeecg.modules.api.power_trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.power_trade.entity.PowerSideSettle;
import org.jeecg.modules.api.power_trade.enums.SettlementFileTypeEnum;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 统推发电侧结算单服务接口
 */
public interface PowerSideSettleService extends IService<PowerSideSettle> {

    /**
     * 根据电站ID和时间范围获取累计结算电量和交易均价
     * @param stationId 电站ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param startYearMonth 开始年月(yyyy-MM)，用于月统推结算单
     * @param endYearMonth 结束年月(yyyy-MM)，用于月统推结算单
     * @return 包含累计结算电量和交易均价的Map
     */
    Map<String, BigDecimal> getStationSettlementSummary(Long stationId, String startDate, String endDate,
                                                        String startYearMonth, String endYearMonth);

    /**
     * 根据省份ID和时间范围获取所有电站的结算汇总数据
     * @param provinceId 省份ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param startYearMonth 开始年月(yyyy-MM)，用于月统推结算单
     * @param endYearMonth 结束年月(yyyy-MM)，用于月统推结算单
     * @return 电站结算汇总列表
     */
    List<Map<String, Object>> getProvinceStationSettlementSummary(Integer provinceId, String startDate, String endDate,
                                                                  String startYearMonth, String endYearMonth);

    /**
     * 获取电站年度交易电量信息（用于电站详情）
     * @param stationId 电站ID
     * @param year 年份（yyyy格式）
     * @return 年度交易电量信息，包含按月分解的数据
     */
    Map<String, Object> getStationYearlyTradingInfo(Long stationId, String year);

    Map<String, Object> getStationMonthlyTradingInfo(Long stationId, String year);

    /**
     * 获取电站月度交易信息（按年份查询所有月份）
     * @param stationId 电站ID
     * @param year 年份（yyyy格式）
     * @param stationType 电站类型（1-光伏，2-风电，3-储能）
     * @return 月度交易信息，根据电站类型返回不同字段
     */
    Map<String, Object> getStationMonthlyTradingInfoByYear(Long stationId, String year, Integer stationType);
}