package org.jeecg.modules.api.power_trade.util;

import org.jeecg.common.api.vo.Result;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * 控制器通用模板
 * 提供统一的异常处理和响应构建模板，简化Controller层代码
 * 
 * <AUTHOR> Team
 * @since 2025-01-01
 */
@Slf4j
public class ControllerTemplate {

    /**
     * 执行操作并返回结果（有返回值）
     * 
     * @param operation 业务操作
     * @param <T>       返回值类型
     * @return 统一响应结果
     */
    public static <T> Result<T> execute(Supplier<T> operation) {
        try {
            T result = operation.get();
            return ResponseBuilder.success(result);
        } catch (ParameterValidator.ValidationException e) {
            log.warn("参数验证失败: {}", e.getMessage());
            return ResponseBuilder.validationError(e.getMessage());
        } catch (ResponseBuilder.BusinessException e) {
            log.warn("业务处理失败: {}", e.getMessage());
            return ResponseBuilder.businessError(e.getMessage());
        } catch (Exception e) {
            log.error("系统异常", e);
            return ResponseBuilder.systemError(e);
        }
    }

    /**
     * 执行操作并返回结果（无返回值）
     * 
     * @param operation 业务操作
     * @return 统一响应结果
     */
    public static Result<Void> executeVoid(Runnable operation) {
        return execute(() -> {
            operation.run();
            return null;
        });
    }

    /**
     * 执行带数据源切换的操作
     * 
     * @param provinceId 省份ID
     * @param operation  业务操作
     * @param <T>        返回值类型
     * @return 统一响应结果
     */
    public static <T> Result<T> executeWithDataSource(Integer provinceId, Supplier<T> operation) {
        return execute(() -> DataSourceSwitchUtil.executeWithDataSource(provinceId, operation));
    }

    /**
     * 执行带数据源切换的操作（无返回值）
     * 
     * @param provinceId 省份ID
     * @param operation  业务操作
     * @return 统一响应结果
     */
    public static Result<Void> executeWithDataSourceVoid(Integer provinceId, Runnable operation) {
        return executeWithDataSource(provinceId, () -> {
            operation.run();
            return null;
        });
    }

    /**
     * 执行带参数验证的操作
     * 
     * @param validator 参数验证器
     * @param operation 业务操作
     * @param <T>       返回值类型
     * @return 统一响应结果
     */
    public static <T> Result<T> executeWithValidation(Runnable validator, Supplier<T> operation) {
        return execute(() -> {
            validator.run();
            return operation.get();
        });
    }

    /**
     * 执行带参数验证和数据源切换的操作
     * 
     * @param validator  参数验证器
     * @param provinceId 省份ID
     * @param operation  业务操作
     * @param <T>        返回值类型
     * @return 统一响应结果
     */
    public static <T> Result<T> executeWithValidationAndDataSource(Runnable validator, 
                                                                  Integer provinceId, 
                                                                  Supplier<T> operation) {
        return execute(() -> {
            validator.run();
            return DataSourceSwitchUtil.executeWithDataSource(provinceId, operation);
        });
    }

    /**
     * 执行分页查询操作
     * 
     * @param pageNo    页码
     * @param pageSize  每页大小
     * @param operation 分页查询操作
     * @param <T>       数据类型
     * @return 分页响应结果
     */
    public static <T> Result<java.util.Map<String, Object>> executePage(Integer pageNo, 
                                                                        Integer pageSize, 
                                                                        Supplier<PageResult<T>> operation) {
        return execute(() -> {
            // 验证分页参数
            ParameterValidator.validatePaginationParams(pageNo, pageSize);
            
            // 设置默认值
            int currentPage = pageNo != null ? pageNo : 1;
            int currentSize = pageSize != null ? pageSize : 10;
            
            // 执行分页查询
            PageResult<T> pageResult = operation.get();
            
            // 构建分页响应
            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("records", pageResult.getRecords());
            result.put("total", pageResult.getTotal());
            result.put("current", currentPage);
            result.put("size", currentSize);
            result.put("pages", (pageResult.getTotal() + currentSize - 1) / currentSize);
            
            return result;
        });
    }

    /**
     * 执行带日期验证的操作
     * 
     * @param date      日期参数
     * @param operation 业务操作
     * @param <T>       返回值类型
     * @return 统一响应结果
     */
    public static <T> Result<T> executeWithDateValidation(String date, 
                                                          java.util.function.Function<String, T> operation) {
        return execute(() -> {
            // 验证并解析日期
            String targetMonth = DateFormatUtil.extractTargetMonth(date);
            return operation.apply(targetMonth);
        });
    }

    /**
     * 执行标准查询操作（包含常用验证）
     * 
     * @param stationId  电站ID
     * @param provinceId 省份ID
     * @param date       日期参数
     * @param operation  业务操作
     * @param <T>        返回值类型
     * @return 统一响应结果
     */
    public static <T> Result<T> executeStandardQuery(Long stationId, 
                                                    Integer provinceId, 
                                                    String date, 
                                                    java.util.function.Function<String, T> operation) {
        return execute(() -> {
            // 参数验证
            ParameterValidator.ChainValidator.create()
                    .validateStationId(stationId)
                    .validateProvinceId(provinceId)
                    .validateDateParameter(date);
            
            // 日期格式处理
            String targetMonth = DateFormatUtil.extractTargetMonth(date);
            
            // 数据源切换 + 业务逻辑
            return DataSourceSwitchUtil.executeWithDataSource(provinceId, () -> operation.apply(targetMonth));
        });
    }

    /**
     * 执行标准分页查询操作
     * 
     * @param pageNo     页码
     * @param pageSize   每页大小
     * @param provinceId 省份ID
     * @param operation  分页查询操作
     * @param <T>        数据类型
     * @return 分页响应结果
     */
    public static <T> Result<java.util.Map<String, Object>> executeStandardPageQuery(Integer pageNo, 
                                                                                     Integer pageSize, 
                                                                                     Integer provinceId, 
                                                                                     Supplier<PageResult<T>> operation) {
        return execute(() -> {
            // 参数验证
            ParameterValidator.ChainValidator.create()
                    .validatePaginationParams(pageNo, pageSize)
                    .validateProvinceId(provinceId);
            
            // 设置默认值
            int currentPage = pageNo != null ? pageNo : 1;
            int currentSize = pageSize != null ? pageSize : 10;
            
            // 数据源切换 + 分页查询
            PageResult<T> pageResult = DataSourceSwitchUtil.executeWithDataSource(provinceId, operation);
            
            // 构建分页响应
            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("records", pageResult.getRecords());
            result.put("total", pageResult.getTotal());
            result.put("current", currentPage);
            result.put("size", currentSize);
            result.put("pages", (pageResult.getTotal() + currentSize - 1) / currentSize);
            
            return result;
        });
    }

    /**
     * 执行条件操作
     * 
     * @param condition 条件
     * @param operation 业务操作
     * @param <T>       返回值类型
     * @return 条件操作构建器
     */
    public static <T> ConditionalOperation<T> when(boolean condition, Supplier<T> operation) {
        return new ConditionalOperation<>(condition, operation);
    }

    /**
     * 分页结果包装类
     * 
     * @param <T> 数据类型
     */
    public static class PageResult<T> {
        private final java.util.List<T> records;
        private final long total;

        public PageResult(java.util.List<T> records, long total) {
            this.records = records;
            this.total = total;
        }

        public java.util.List<T> getRecords() {
            return records;
        }

        public long getTotal() {
            return total;
        }

        public static <T> PageResult<T> of(java.util.List<T> records, long total) {
            return new PageResult<>(records, total);
        }

        public static <T> PageResult<T> empty() {
            return new PageResult<>(new java.util.ArrayList<>(), 0);
        }
    }

    /**
     * 条件操作构建器
     * 
     * @param <T> 返回值类型
     */
    public static class ConditionalOperation<T> {
        private final boolean condition;
        private final Supplier<T> operation;

        private ConditionalOperation(boolean condition, Supplier<T> operation) {
            this.condition = condition;
            this.operation = operation;
        }

        /**
         * 条件为真时执行操作
         * 
         * @return 响应结果
         */
        public Result<T> thenExecute() {
            if (condition) {
                return execute(operation);
            }
            return ResponseBuilder.error("条件不满足");
        }

        /**
         * 条件为假时执行其他操作
         * 
         * @param elseOperation 其他操作
         * @return 响应结果
         */
        public Result<T> orElse(Supplier<T> elseOperation) {
            if (condition) {
                return execute(operation);
            } else {
                return execute(elseOperation);
            }
        }

        /**
         * 条件为假时返回错误
         * 
         * @param errorMessage 错误消息
         * @return 响应结果
         */
        public Result<T> orError(String errorMessage) {
            if (condition) {
                return execute(operation);
            } else {
                return ResponseBuilder.error(errorMessage);
            }
        }
    }

    /**
     * 操作执行统计
     */
    public static class ExecutionStats {
        private static final java.util.concurrent.atomic.AtomicLong totalExecutions = 
            new java.util.concurrent.atomic.AtomicLong(0);
        private static final java.util.concurrent.atomic.AtomicLong successfulExecutions = 
            new java.util.concurrent.atomic.AtomicLong(0);
        private static final java.util.concurrent.atomic.AtomicLong failedExecutions = 
            new java.util.concurrent.atomic.AtomicLong(0);

        public static void recordExecution(boolean success) {
            totalExecutions.incrementAndGet();
            if (success) {
                successfulExecutions.incrementAndGet();
            } else {
                failedExecutions.incrementAndGet();
            }
        }

        public static long getTotalExecutions() {
            return totalExecutions.get();
        }

        public static long getSuccessfulExecutions() {
            return successfulExecutions.get();
        }

        public static long getFailedExecutions() {
            return failedExecutions.get();
        }

        public static double getSuccessRate() {
            long total = totalExecutions.get();
            return total == 0 ? 0.0 : (double) successfulExecutions.get() / total;
        }

        public static void reset() {
            totalExecutions.set(0);
            successfulExecutions.set(0);
            failedExecutions.set(0);
        }

        @Override
        public String toString() {
            return String.format("ExecutionStats{total=%d, success=%d, failed=%d, successRate=%.2f%%}", 
                getTotalExecutions(), getSuccessfulExecutions(), getFailedExecutions(), getSuccessRate() * 100);
        }
    }
}
