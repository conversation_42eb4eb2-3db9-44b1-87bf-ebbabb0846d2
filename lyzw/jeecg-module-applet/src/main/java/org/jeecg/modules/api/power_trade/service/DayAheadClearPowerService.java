package org.jeecg.modules.api.power_trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.power_trade.entity.DayAheadClearPower;

import java.util.List;
import java.util.Map;

public interface DayAheadClearPowerService extends IService<DayAheadClearPower> {

    List<DayAheadClearPower> getDayDetails(Long stationId, String date);

    List<Map<String, Object>> getMonthlySum(Long stationId, String yearMonth);

    List<Map<String, Object>> getYearlySum(Long stationId, String year);
}