package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("power_side_settle")  // MyBatis-Plus 表名映射
@ApiModel(value = "PowerSideSettle", description = "统推发电侧结算单")
public class PowerSideSettle {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;  // bigint unsigned 对应 Java Long

    @ApiModelProperty(value = "关联文件ID", example = "1001")
    private Long fileId;

    @ApiModelProperty(value = "结算单元类型(1:风电, 2:光伏, 3:储能)", example = "1")
    private Integer stationType;

    @ApiModelProperty(value = "数据类型(0:购电侧, 1:售电侧)，仅用于储能结算单", example = "0")
    private Integer dataType;

    @ApiModelProperty(value = "实际上网电量(单位: MWh)", example = "100.123456")
    private BigDecimal actualInternetElectricity;

    @ApiModelProperty(value = "结算电量(单位: MWh)", example = "95.654321")
    private BigDecimal settlementElectricity;  // decimal(20,6)

    @ApiModelProperty(value = "合同电量(单位: MWh)", example = "100.000000")
    private BigDecimal contractElectricity;  // decimal(20,6)

    @ApiModelProperty(value = "偏差电量(单位: MWh)", example = "4.345679")
    private BigDecimal deviationElectricity;  // decimal(20,6)

    @ApiModelProperty(value = "结算电费(单位: 元)", example = "50000.123456")
    private BigDecimal settlementElectricFee;  // decimal(20,6)
}