package org.jeecg.modules.api.power_trade.util;

import org.jeecg.modules.api.power_trade.dto.EnergyStorageDailyCleanV2DTO;
import org.jeecg.modules.api.power_trade.dto.PowerGenerationSideDataDTO;
import org.jeecg.modules.api.power_trade.dto.UserSideDataDTO;
import org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 储能数据转换工具类
 * 负责将扁平化的 EnergyStorageDailyClean 转换为分类结构的 EnergyStorageDailyCleanV2DTO
 */
public class EnergyStorageDataConverter {

    /**
     * 批量转换储能日清数据为V2版本的分类结构
     * 
     * @param originalDataList 原始扁平化数据列表
     * @return 转换后的分类结构数据列表
     */
    public static List<EnergyStorageDailyCleanV2DTO> convertToV2DTOList(List<EnergyStorageDailyClean> originalDataList) {
        if (originalDataList == null) {
            return null;
        }
        
        return originalDataList.stream()
                .map(EnergyStorageDataConverter::convertToV2DTO)
                .collect(Collectors.toList());
    }

    /**
     * 单个数据转换：扁平化结构 -> 分类结构
     * 
     * @param original 原始扁平化数据
     * @return 转换后的分类结构数据
     */
    public static EnergyStorageDailyCleanV2DTO convertToV2DTO(EnergyStorageDailyClean original) {
        if (original == null) {
            return null;
        }

        EnergyStorageDailyCleanV2DTO v2DTO = new EnergyStorageDailyCleanV2DTO();

        // 复制基础字段
        v2DTO.setId(original.getId());
        v2DTO.setDate(original.getDate());
        v2DTO.setStationId(original.getStationId());
        v2DTO.setCreateTime(original.getCreateTime());
        v2DTO.setUpdateTime(original.getUpdateTime());
        v2DTO.setCreateBy(original.getCreateBy());
        v2DTO.setUpdateBy(original.getUpdateBy());
        v2DTO.setProvinceId(original.getProvinceId());
        v2DTO.setTradeDate(original.getTradeDate());

        // 转换用户侧数据（购电侧）
        UserSideDataDTO userSideData = convertToUserSideData(original);
        v2DTO.setUserSideData(userSideData);

        // 转换发电侧数据（售电侧）
        PowerGenerationSideDataDTO powerGenerationSideData = convertToPowerGenerationSideData(original);
        v2DTO.setPowerGenerationSideData(powerGenerationSideData);

        return v2DTO;
    }

    /**
     * 转换用户侧数据（购电侧）
     * 提取所有以 user 开头的字段，去除前缀
     * 
     * @param original 原始数据
     * @return 用户侧数据DTO
     */
    private static UserSideDataDTO convertToUserSideData(EnergyStorageDailyClean original) {
        UserSideDataDTO userSideData = new UserSideDataDTO();

        // 映射字段：去除 "user" 前缀，转换为驼峰命名
        userSideData.setDayAheadDeviationPower(original.getUserDayAheadDeviationPower());
        userSideData.setDayAheadDeviationAveragePrice(original.getUserDayAheadDeviationAveragePrice());
        userSideData.setDayAheadDeviationFee(original.getUserDayAheadDeviationFee());
        userSideData.setRealtimeDeviationPower(original.getUserRealtimeDeviationPower());
        userSideData.setRealtimeDeviationAveragePrice(original.getUserRealtimeDeviationAveragePrice());
        userSideData.setRealtimeDeviationFee(original.getUserRealtimeDeviationFee());
        userSideData.setTotalPower(original.getUserTotalPower());
        userSideData.setTotalFee(original.getUserTotalFee());

        return userSideData;
    }

    /**
     * 转换发电侧数据（售电侧）
     * 提取所有以 powerGeneration 开头的字段，去除前缀
     * 
     * @param original 原始数据
     * @return 发电侧数据DTO
     */
    private static PowerGenerationSideDataDTO convertToPowerGenerationSideData(EnergyStorageDailyClean original) {
        PowerGenerationSideDataDTO powerGenerationSideData = new PowerGenerationSideDataDTO();

        // 映射字段：去除 "powerGeneration" 前缀，转换为驼峰命名
        powerGenerationSideData.setDayAheadDeviationPower(original.getPowerGenerationDayAheadDeviationPower());
        powerGenerationSideData.setDayAheadDeviationAveragePrice(original.getPowerGenerationDayAheadDeviationAveragePrice());
        powerGenerationSideData.setDayAheadDeviationFee(original.getPowerGenerationDayAheadDeviationFee());
        powerGenerationSideData.setRealtimeDeviationPower(original.getPowerGenerationRealtimeDeviationPower());
        powerGenerationSideData.setRealtimeDeviationAveragePrice(original.getPowerGenerationRealtimeDeviationAveragePrice());
        powerGenerationSideData.setRealtimeDeviationFee(original.getPowerGenerationRealtimeDeviationFee());
        powerGenerationSideData.setTotalPower(original.getPowerGenerationTotalPower());
        powerGenerationSideData.setTotalFee(original.getPowerGenerationTotalFee());

        return powerGenerationSideData;
    }

    /**
     * 反向转换：分类结构 -> 扁平化结构
     * 用于向后兼容或数据持久化
     * 
     * @param v2DTO 分类结构数据
     * @return 扁平化结构数据
     */
    public static EnergyStorageDailyClean convertFromV2DTO(EnergyStorageDailyCleanV2DTO v2DTO) {
        if (v2DTO == null) {
            return null;
        }

        EnergyStorageDailyClean original = new EnergyStorageDailyClean();

        // 复制基础字段
        original.setId(v2DTO.getId());
        original.setDate(v2DTO.getDate());
        original.setStationId(v2DTO.getStationId());
        original.setCreateTime(v2DTO.getCreateTime());
        original.setUpdateTime(v2DTO.getUpdateTime());
        original.setCreateBy(v2DTO.getCreateBy());
        original.setUpdateBy(v2DTO.getUpdateBy());
        original.setProvinceId(v2DTO.getProvinceId());
        original.setTradeDate(v2DTO.getTradeDate());

        // 转换用户侧数据
        if (v2DTO.getUserSideData() != null) {
            UserSideDataDTO userSideData = v2DTO.getUserSideData();
            original.setUserDayAheadDeviationPower(userSideData.getDayAheadDeviationPower());
            original.setUserDayAheadDeviationAveragePrice(userSideData.getDayAheadDeviationAveragePrice());
            original.setUserDayAheadDeviationFee(userSideData.getDayAheadDeviationFee());
            original.setUserRealtimeDeviationPower(userSideData.getRealtimeDeviationPower());
            original.setUserRealtimeDeviationAveragePrice(userSideData.getRealtimeDeviationAveragePrice());
            original.setUserRealtimeDeviationFee(userSideData.getRealtimeDeviationFee());
            original.setUserTotalPower(userSideData.getTotalPower());
            original.setUserTotalFee(userSideData.getTotalFee());
        }

        // 转换发电侧数据
        if (v2DTO.getPowerGenerationSideData() != null) {
            PowerGenerationSideDataDTO powerGenerationSideData = v2DTO.getPowerGenerationSideData();
            original.setPowerGenerationDayAheadDeviationPower(powerGenerationSideData.getDayAheadDeviationPower());
            original.setPowerGenerationDayAheadDeviationAveragePrice(powerGenerationSideData.getDayAheadDeviationAveragePrice());
            original.setPowerGenerationDayAheadDeviationFee(powerGenerationSideData.getDayAheadDeviationFee());
            original.setPowerGenerationRealtimeDeviationPower(powerGenerationSideData.getRealtimeDeviationPower());
            original.setPowerGenerationRealtimeDeviationAveragePrice(powerGenerationSideData.getRealtimeDeviationAveragePrice());
            original.setPowerGenerationRealtimeDeviationFee(powerGenerationSideData.getRealtimeDeviationFee());
            original.setPowerGenerationTotalPower(powerGenerationSideData.getTotalPower());
            original.setPowerGenerationTotalFee(powerGenerationSideData.getTotalFee());
        }

        return original;
    }
}
