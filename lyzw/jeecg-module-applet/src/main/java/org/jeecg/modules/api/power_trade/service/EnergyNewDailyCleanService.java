package org.jeecg.modules.api.power_trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.power_trade.entity.EnergyNewDailyClean;

import java.util.List;

public interface EnergyNewDailyCleanService extends IService<EnergyNewDailyClean> {
    
    /**
     * 按日查询明细数据
     */
    List<EnergyNewDailyClean> selectByDay(Long stationId, String date);

    /**
     * 按月汇总查询
     */
    List<EnergyNewDailyClean> selectByMonth(Long stationId, String date);

    /**
     * 按年汇总查询
     */
    List<EnergyNewDailyClean> selectByYear(Long stationId, String date);
}
