package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 电站交易信息DTO
 * 用于电站总览和电站年度交易电量信息接口的统一返回
 */
@Data
@ApiModel(value = "电站交易信息DTO", description = "电站交易相关信息的统一数据传输对象")
public class StationTradingDTO {

    @ApiModelProperty(value = "电站ID", required = true, example = "123")
    private Long stationId;

    @ApiModelProperty(value = "电站名称", required = true, example = "永武风电")
    private String stationName;

    @ApiModelProperty(value = "电站类型 (1-风电, 2-光伏, 3-储能)", required = true, example = "1")
    private Integer stationType;

    @ApiModelProperty(value = "电站容量(MW)", example = "143.75")
    private BigDecimal capacity;

    @ApiModelProperty(value = "省份ID", required = true, example = "1")
    private Integer provinceId;

    @ApiModelProperty(value = "累计结算电量(MWh)", required = true, example = "12500.50")
    private BigDecimal totalSettlementElectricity;

    @ApiModelProperty(value = "结算电量单位", example = "MWh")
    private String settlementElectricityUnit = "MWh";

    @ApiModelProperty(value = "交易均价(元/MWh)", required = true, example = "385.25")
    private BigDecimal avgTradePrice;

    @ApiModelProperty(value = "交易均价单位", example = "元/MWh")
    private String avgTradePriceUnit = "元/MWh";

    @ApiModelProperty(value = "累计结算电费(万元)", example = "4812.69")
    private BigDecimal totalSettlementElectricFee;

    @ApiModelProperty(value = "年份", example = "2024")
    private String year;

    @ApiModelProperty(value = "月份", example = "07")
    private String month;

    @ApiModelProperty(value = "月度数据列表 (年度查询时返回)", notes = "包含12个月的详细数据")
    private List<MonthlyTradingData> monthlyDataList;

    @ApiModelProperty(value = "记录数量", example = "15")
    private Integer recordCount;

    /**
     * 月度交易数据内部类
     */
    @Data
    @ApiModel(value = "月度交易数据", description = "单月的交易数据详情")
    public static class MonthlyTradingData {

        @ApiModelProperty(value = "年份", required = true, example = "2024")
        private String year;

        @ApiModelProperty(value = "月份", required = true, example = "01")
        private String month;

        @ApiModelProperty(value = "月度结算电量(MWh)", required = true, example = "1200.50")
        private BigDecimal totalElectricity;

        @ApiModelProperty(value = "月度当月电量(MWh)", example = "1150.30")
        private BigDecimal currentMonthPower;

        @ApiModelProperty(value = "月度交易均价(元/MWh)", required = true, example = "380.25")
        private BigDecimal averagePrice;

        @ApiModelProperty(value = "月度记录数量", example = "5")
        private Integer recordCount;

        @ApiModelProperty(value = "实际上网电量总计(MWh)", example = "1180.20")
        private BigDecimal totalActualInternetElectricity;

        @ApiModelProperty(value = "合同电量总计(MWh)", example = "1100.00")
        private BigDecimal totalContractElectricity;

        @ApiModelProperty(value = "偏差电量总计(MWh)", example = "50.50")
        private BigDecimal totalDeviationElectricity;

        @ApiModelProperty(value = "结算电费总计(万元)", example = "456.30")
        private BigDecimal totalSettlementElectricFee;
    }
}
