package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.power_trade.entity.RealtimeClearPower;
import org.jeecg.modules.api.power_trade.mapper.RealtimeClearPowerMapper;
import org.jeecg.modules.api.power_trade.service.RealtimeClearPowerService;
import org.springframework.stereotype.Service;

@Service
public class RealtimeClearPowerServiceImpl extends ServiceImpl<RealtimeClearPowerMapper, RealtimeClearPower> implements RealtimeClearPowerService {
}
