package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("月度结算数据DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MonthlySettlementDTO {
    @ApiModelProperty(value = "结算电量(MWh)", notes = "当月结算电量")
    private Double settlePower;

    @ApiModelProperty(value = "结算均价(元/MWh)", notes = "当月结算平均价格")
    private Double settlementAveragePrice;
}