package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 光伏风电日清分数据DTO
 * 用于光伏风电接口的返回
 */
@Data
@ApiModel(value = "光伏风电日清分数据DTO", description = "光伏风电日清分数据传输对象")
public class EnergyNewDailyCleanDTO {

    @ApiModelProperty(value = "记录ID", example = "12090")
    private Long id;

    @ApiModelProperty(value = "日期", required = true, example = "2025-07-01")
    private Date date;

    @ApiModelProperty(value = "电站ID", required = true, example = "2")
    private Long stationId;

    @ApiModelProperty(value = "中长期电量(MWh)", example = "514.60")
    private BigDecimal midLongTermPower;

    @ApiModelProperty(value = "中长期电价(元/MWh)", example = "565.60")
    private BigDecimal midLongTermPrice;

    @ApiModelProperty(value = "中长期电费(元)", example = "291051.20")
    private BigDecimal midLongTermFee;

    @ApiModelProperty(value = "保障电量(MWh)", example = "545.40")
    private BigDecimal guaranteePower;

    @ApiModelProperty(value = "保障电价(元/MWh)", example = "384.40")
    private BigDecimal guaranteePrice;

    @ApiModelProperty(value = "保障电费(元)", example = "209633.00")
    private BigDecimal guaranteeFee;

    @ApiModelProperty(value = "日前偏差电量(MWh)", example = "-324.40")
    private BigDecimal dayAheadDeviationPower;

    @ApiModelProperty(value = "日前偏差电价(元/MWh)", example = "0.00")
    private BigDecimal dayAheadDeviationPrice;

    @ApiModelProperty(value = "日前偏差电费(元)", example = "0.00")
    private BigDecimal dayAheadDeviationFee;

    @ApiModelProperty(value = "实时偏差电量(MWh)", example = "460.40")
    private BigDecimal realtimeDeviationPower;

    @ApiModelProperty(value = "实时偏差电价(元/MWh)", example = "2.70")
    private BigDecimal realtimeDeviationPrice;

    @ApiModelProperty(value = "实时偏差电费(元)", example = "1243.40")
    private BigDecimal realtimeDeviationFee;

    @ApiModelProperty(value = "超额收益回收(元)", example = "0.00")
    private BigDecimal excessProfitRecovery;

    @ApiModelProperty(value = "日前收益回收(元)", example = "-111.30")
    private BigDecimal dayAheadProfitRecovery;

    @ApiModelProperty(value = "总电量(MWh)", example = "1196.00")
    private BigDecimal totalPower;

    @ApiModelProperty(value = "总电费(元)", example = "501816.00")
    private BigDecimal totalFee;

    @ApiModelProperty(value = "结算均价(元/MWh)", example = "420.00")
    private BigDecimal settlementAvgPrice;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
