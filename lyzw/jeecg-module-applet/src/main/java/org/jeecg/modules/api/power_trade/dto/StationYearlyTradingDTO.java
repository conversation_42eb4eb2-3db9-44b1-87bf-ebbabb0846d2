package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.api.power_trade.entity.Station;

/**
 * 电站年度交易详情DTO
 * 用于电站年度交易电量信息接口的返回
 */
@Data
@ApiModel(value = "电站年度交易详情DTO", description = "电站年度交易电量信息的详细数据传输对象")
public class StationYearlyTradingDTO {

    @ApiModelProperty(value = "电站基础信息", required = true)
    private StationInfo stationInfo;

    @ApiModelProperty(value = "年度交易信息", required = true)
    private StationTradingDTO tradingInfo;

    @ApiModelProperty(value = "查询年份", required = true, example = "2024")
    private String year;

    @ApiModelProperty(value = "省份ID", required = true, example = "1")
    private Integer provinceId;

    /**
     * 电站基础信息内部类
     */
    @Data
    @ApiModel(value = "电站基础信息", description = "电站的基本信息")
    public static class StationInfo {

        @ApiModelProperty(value = "电站ID", required = true, example = "123")
        private Long id;

        @ApiModelProperty(value = "电站名称", required = true, example = "永武风电")
        private String name;

        @ApiModelProperty(value = "调度名称", example = "永武风电")
        private String dispatchName;

        @ApiModelProperty(value = "省份ID", required = true, example = "1")
        private Integer provinceId;

        @ApiModelProperty(value = "电站编号", example = "J00637")
        private String stationNo;

        @ApiModelProperty(value = "装机容量(MW)", example = "143.75")
        private Double capacity;

        @ApiModelProperty(value = "电站类型 (1-风电, 2-光伏, 3-储能)", required = true, example = "1")
        private Integer type;

        @ApiModelProperty(value = "交易状态 (0-未参与, 1-参与)", example = "1")
        private Integer tradeStatus;

        @ApiModelProperty(value = "电力状态", example = "1")
        private Integer powerStatus;

        @ApiModelProperty(value = "日前申报状态", example = "1")
        private Integer dayAheadDeclarationStatus;

        @ApiModelProperty(value = "经度", example = "117.89")
        private Double longitude;

        @ApiModelProperty(value = "纬度", example = "33.29")
        private Double latitude;

        @ApiModelProperty(value = "功率", example = "100.5")
        private Double power;

        @ApiModelProperty(value = "排序号", example = "2")
        private Integer sortNo;

        /**
         * 从Station实体转换为StationInfo
         */
        public static StationInfo fromStation(Station station) {
            if (station == null) {
                return null;
            }
            
            StationInfo info = new StationInfo();
            info.setId(station.getId());
            info.setName(station.getName());
            info.setDispatchName(station.getDispatchName());
            info.setProvinceId(station.getProvinceId());
            info.setStationNo(station.getStationNo());
            info.setCapacity(station.getCapacity());
            info.setType(station.getType());
            info.setTradeStatus(station.getTradeStatus());
            info.setPowerStatus(station.getPowerStatus());
            info.setDayAheadDeclarationStatus(station.getDayAheadDeclarationStatus());
            info.setLongitude(station.getLongitude());
            info.setLatitude(station.getLatitude());
            info.setPower(station.getPower());
            info.setSortNo(station.getSortNo() != null ? station.getSortNo().intValue() : null);
            
            return info;
        }
    }
}
