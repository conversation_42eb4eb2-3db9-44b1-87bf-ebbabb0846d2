package org.jeecg.modules.api.power_trade.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 统一结算数据Mapper
 * 提供基于power_side_settle表的统一结算数据查询
 */
@Mapper
public interface UnifiedSettlementMapper {

    /**
     * 获取区域结算电量汇总
     * 用于首页概览数据
     * 
     * @param stationIds 电站ID列表
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @return 区域结算汇总数据
     */
    Map<String, Object> getRegionSettlementSummary(@Param("stationIds") List<Long> stationIds,
                                                   @Param("startDate") String startDate,
                                                   @Param("endDate") String endDate);

    /**
     * 获取电站结算电量明细
     * 用于交易概况页面
     * 
     * @param stationIds 电站ID列表
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @return 电站结算明细列表
     */
    List<Map<String, Object>> getStationSettlementDetails(@Param("stationIds") List<Long> stationIds,
                                                          @Param("startDate") String startDate,
                                                          @Param("endDate") String endDate);

    /**
     * 获取电站结算电量时间序列
     * 用于电站详情页面的趋势分析
     * 
     * @param stationId 电站ID
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @param groupBy 分组方式 (day/month/year)
     * @return 时间序列数据
     */
    List<Map<String, Object>> getStationSettlementTimeSeries(@Param("stationId") Long stationId,
                                                             @Param("startDate") String startDate,
                                                             @Param("endDate") String endDate,
                                                             @Param("groupBy") String groupBy);

    /**
     * 获取省份结算电量汇总
     * 用于全国数据聚合
     * 
     * @param provinceId 省份ID
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @return 省份结算汇总数据
     */
    Map<String, Object> getProvinceSettlementSummary(@Param("provinceId") Integer provinceId,
                                                     @Param("startDate") String startDate,
                                                     @Param("endDate") String endDate);

    /**
     * 获取电站月度结算数据
     * 用于月度报表
     * 
     * @param stationId 电站ID
     * @param year 年份 (yyyy)
     * @param month 月份 (MM)
     * @return 月度结算数据
     */
    Map<String, Object> getStationMonthlySettlement(@Param("stationId") Long stationId,
                                                    @Param("year") String year,
                                                    @Param("month") String month);

    /**
     * 获取电站年度结算数据
     * 用于年度报表
     * 
     * @param stationId 电站ID
     * @param year 年份 (yyyy)
     * @return 年度结算数据
     */
    Map<String, Object> getStationYearlySettlement(@Param("stationId") Long stationId,
                                                   @Param("year") String year);

    /**
     * 验证数据一致性
     * 比较新旧数据源的差异
     * 
     * @param stationIds 电站ID列表
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @return 数据对比结果
     */
    Map<String, Object> validateDataConsistency(@Param("stationIds") List<Long> stationIds,
                                               @Param("startDate") String startDate,
                                               @Param("endDate") String endDate);

    /**
     * 获取结算数据统计信息
     * 用于数据质量监控
     * 
     * @param stationIds 电站ID列表
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @return 统计信息
     */
    Map<String, Object> getSettlementStatistics(@Param("stationIds") List<Long> stationIds,
                                               @Param("startDate") String startDate,
                                               @Param("endDate") String endDate);

    /**
     * 获取电站类型结算汇总
     * 按电站类型分组统计
     * 
     * @param stationIds 电站ID列表
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @return 按类型分组的结算数据
     */
    List<Map<String, Object>> getSettlementByStationType(@Param("stationIds") List<Long> stationIds,
                                                         @Param("startDate") String startDate,
                                                         @Param("endDate") String endDate);

    /**
     * 获取最新结算数据
     * 获取指定电站的最新一次结算记录
     * 
     * @param stationIds 电站ID列表
     * @return 最新结算数据
     */
    List<Map<String, Object>> getLatestSettlementData(@Param("stationIds") List<Long> stationIds);
}
