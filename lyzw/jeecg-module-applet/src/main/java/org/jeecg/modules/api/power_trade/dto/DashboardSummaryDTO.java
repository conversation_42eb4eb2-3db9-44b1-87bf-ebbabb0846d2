package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@ApiModel("首页概览数据DTO")
@Data
public class DashboardSummaryDTO {
    @ApiModelProperty(value = "场站类型统计信息", notes = "包含各类场站的统计信息")
    private StationTypeStatisticsDTO stationTypeStatistics;

    @ApiModelProperty(value = "能源类型数量统计", notes = "按能源类型分类的场站数量统计")
    private Map<Integer, Integer> energyTypeCount;

    // 新增字段，用于支持修正后的数据获取逻辑
    @ApiModelProperty(value = "累计发电量", notes = "该区域内所有电站的发电数据总计")
    private BigDecimal accumulatedPower;

    @ApiModelProperty(value = "计划发电量", notes = "计划发电量")
    private BigDecimal plannedPower;

    @ApiModelProperty(value = "结算均价", notes = "与交易概况一致的结算均价")
    private BigDecimal settlementAvgPrice;

    @ApiModelProperty(value = "结算电量", notes = "与交易概况一致的结算电量")
    private BigDecimal settlementPower;

    @ApiModelProperty(value = "限电量", notes = "限电量")
    private BigDecimal limitedPower;

    /**
     * 构造方法
     */
    public DashboardSummaryDTO() {
        this.accumulatedPower = BigDecimal.ZERO;
        this.plannedPower = BigDecimal.ZERO;
        this.settlementAvgPrice = BigDecimal.ZERO;
        this.settlementPower = BigDecimal.ZERO;
        this.limitedPower = BigDecimal.ZERO;
    }
}

