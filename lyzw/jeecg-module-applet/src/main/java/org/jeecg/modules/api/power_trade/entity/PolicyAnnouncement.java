package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 政策公告实体
 */
@Data
@TableName("policy_announcement")
@ApiModel(value = "PolicyAnnouncement", description = "政策公告实体")
public class PolicyAnnouncement {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @TableField("title")
    @ApiModelProperty(value = "政策公告标题", example = "关于电力市场政策的通知")
    private String title;

    @TableField("content")
    @ApiModelProperty(value = "政策公告内容", example = "政策内容详情...")
    private String content;

    @TableField("summary")
    @ApiModelProperty(value = "摘要", example = "政策摘要...")
    private String summary;

    @TableField("type_id")
    @ApiModelProperty(value = "政策类型ID", example = "1")
    private Integer typeId;

    @TableField("file_url")
    @ApiModelProperty(value = "附件文件URL", example = "http://example.com/files/policy.pdf")
    private String fileUrl;

    @TableField("file_name")
    @ApiModelProperty(value = "附件文件名", example = "policy.pdf")
    private String fileName;

    @TableField("publish_date")
    @ApiModelProperty(value = "发布日期", example = "2024-01-01")
    private Date publishDate;

    @TableField("status")
    @ApiModelProperty(value = "状态(1:发布,0:草稿)", example = "1")
    private Integer status;

    @TableField("view_count")
    @ApiModelProperty(value = "浏览次数", example = "100")
    private Integer viewCount;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private Date createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间", example = "2024-01-01 12:00:00")
    private Date updateTime;

    @TableField("create_by")
    @ApiModelProperty(value = "创建人", example = "admin")
    private String createBy;

    @TableField("update_by")
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updateBy;

    // 非数据库字段
    @TableField(exist = false)
    @ApiModelProperty(value = "政策类型名称(非数据库字段)", example = "市场政策")
    private String typeName;

    @TableField(exist = false)
    @ApiModelProperty(value = "标签列表(非数据库字段)")
    private List<String> tags;
}
