package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("day_ahead_node_clear_electricity")
public class DayAheadNodeClearElectricity {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @TableField("date")
    @ApiModelProperty(value = "日期", example = "2023-01-01")
    private Date date;

    @TableField("value")
    @ApiModelProperty(value = "预测值(单位:千瓦时)", example = "100.123456")
    private BigDecimal value;

    @TableField("time")
    @ApiModelProperty(value = "时间标识(如0015表示00:15)", example = "0015")
    private String time;

    @TableField("station_id")
    @ApiModelProperty(value = "电站ID")
    private Long stationId;

    @TableField("station_name")
    @ApiModelProperty(value = "电站名称")
    private String stationName;

    @TableField("province_id")
    @ApiModelProperty(value = "省份ID")
    private Long provinceId;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    private Date createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间", example = "2023-01-01 12:00:00")
    private Date updateTime;
}