package org.jeecg.modules.api.power_trade.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.dto.SettlementAggregationDTO;
import org.jeecg.modules.api.power_trade.dto.StationSettlementDTO;
import org.jeecg.modules.api.power_trade.mapper.PowerSideSettleMapper;
import org.jeecg.modules.api.power_trade.mapper.UnifiedSettlementMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 统一结算数据服务
 * 提供首页和交易概况的统一结算电量数据源
 */
@Slf4j
@Service
public class UnifiedSettlementService {

    @Autowired
    private PowerSideSettleMapper powerSideSettleMapper;
    
    @Autowired(required = false)
    private UnifiedSettlementMapper unifiedSettlementMapper;
    
    @Autowired
    private ParallelQueryManager parallelQueryManager;
    
    @Value("${settlement.data.source:new}")
    private String dataSource;
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 获取区域结算电量汇总（用于首页）
     * 
     * @param stationIds 电站ID列表
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 结算电量汇总数据
     */
    public SettlementAggregationDTO getRegionSettlementSummary(List<Long> stationIds, 
                                                               String startDate, 
                                                               String endDate) {
        if (stationIds == null || stationIds.isEmpty()) {
            log.warn("电站ID列表为空，返回空结果");
            return new SettlementAggregationDTO();
        }
        
        // 如果未指定日期范围，使用当年数据
        if (startDate == null || endDate == null) {
            startDate = getCurrentYearStart();
            endDate = getCurrentDate();
        }
        
        log.info("查询区域结算电量汇总 - 电站数: {}, 开始日期: {}, 结束日期: {}", 
                stationIds.size(), startDate, endDate);
        
        try {
            // 使用统一查询Mapper
            if (unifiedSettlementMapper != null && "new".equals(dataSource)) {
                return getRegionSettlementFromUnifiedMapper(stationIds, startDate, endDate);
            }
            
            // 兼容模式：使用原有PowerSideSettleMapper
            return getRegionSettlementFromPowerSideSettle(stationIds, startDate, endDate);
            
        } catch (Exception e) {
            log.error("查询区域结算电量汇总失败: {}", e.getMessage(), e);
            return new SettlementAggregationDTO();
        }
    }
    
    /**
     * 从统一Mapper获取区域结算数据
     */
    private SettlementAggregationDTO getRegionSettlementFromUnifiedMapper(List<Long> stationIds, 
                                                                          String startDate, 
                                                                          String endDate) {
        return parallelQueryManager.executeAsync(() -> {
            Map<String, Object> result = unifiedSettlementMapper
                .getRegionSettlementSummary(stationIds, startDate, endDate);
            return convertToSettlementAggregation(result);
        }).join();
    }
    
    /**
     * 从PowerSideSettle表获取区域结算数据
     */
    private SettlementAggregationDTO getRegionSettlementFromPowerSideSettle(List<Long> stationIds, 
                                                                            String startDate, 
                                                                            String endDate) {
        return parallelQueryManager.executeAsync(() -> {
            // 并行查询每个电站的结算数据
            List<CompletableFuture<Map<String, BigDecimal>>> futures = stationIds.stream()
                .map(stationId -> parallelQueryManager.executeAsync(() -> 
                    powerSideSettleMapper.getStationSettlementSummary(
                        stationId, startDate, endDate, null, null)))
                .collect(Collectors.toList());
            
            // 等待所有查询完成
            List<Map<String, BigDecimal>> allResults = futures.stream()
                .map(CompletableFuture::join)
                .filter(map -> map != null && !map.isEmpty())
                .collect(Collectors.toList());
            
            // 聚合结果
            return aggregateSettlementResults(allResults);
        }).join();
    }
    
    /**
     * 获取电站结算电量明细（用于交易概况）
     * 
     * @param stationIds 电站ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 电站结算电量明细列表
     */
    public List<StationSettlementDTO> getStationSettlementDetails(List<Long> stationIds,
                                                                  String startDate,
                                                                  String endDate) {
        if (stationIds == null || stationIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 如果未指定日期范围，使用当年数据
        if (startDate == null || endDate == null) {
            startDate = getCurrentYearStart();
            endDate = getCurrentDate();
        }
        
        log.info("查询电站结算电量明细 - 电站数: {}, 开始日期: {}, 结束日期: {}", 
                stationIds.size(), startDate, endDate);
        
        try {
            // 使用统一查询Mapper
            if (unifiedSettlementMapper != null && "new".equals(dataSource)) {
                return getStationDetailsFromUnifiedMapper(stationIds, startDate, endDate);
            }
            
            // 兼容模式：使用原有PowerSideSettleMapper
            return getStationDetailsFromPowerSideSettle(stationIds, startDate, endDate);
            
        } catch (Exception e) {
            log.error("查询电站结算电量明细失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 从统一Mapper获取电站结算明细
     */
    private List<StationSettlementDTO> getStationDetailsFromUnifiedMapper(List<Long> stationIds, 
                                                                          String startDate, 
                                                                          String endDate) {
        return parallelQueryManager.executeAsync(() -> {
            List<Map<String, Object>> results = unifiedSettlementMapper
                .getStationSettlementDetails(stationIds, startDate, endDate);
            return convertToStationSettlementList(results);
        }).join();
    }
    
    /**
     * 从PowerSideSettle表获取电站结算明细
     */
    private List<StationSettlementDTO> getStationDetailsFromPowerSideSettle(List<Long> stationIds, 
                                                                            String startDate, 
                                                                            String endDate) {
        // 并行查询每个电站的结算数据
        List<CompletableFuture<StationSettlementDTO>> futures = stationIds.stream()
            .map(stationId -> parallelQueryManager.executeAsync(() -> {
                Map<String, BigDecimal> data = powerSideSettleMapper.getStationSettlementSummary(
                    stationId, startDate, endDate, null, null);
                return convertToStationSettlement(stationId, data);
            }))
            .collect(Collectors.toList());
        
        // 等待所有查询完成
        return futures.stream()
            .map(CompletableFuture::join)
            .filter(dto -> dto != null && dto.getStationId() != null)
            .collect(Collectors.toList());
    }
    
    /**
     * 聚合结算结果
     */
    private SettlementAggregationDTO aggregateSettlementResults(List<Map<String, BigDecimal>> results) {
        SettlementAggregationDTO dto = new SettlementAggregationDTO();
        
        if (results == null || results.isEmpty()) {
            return dto;
        }
        
        BigDecimal totalSettlementPower = BigDecimal.ZERO;
        BigDecimal totalSettlementFee = BigDecimal.ZERO;
        
        for (Map<String, BigDecimal> result : results) {
            BigDecimal power = result.getOrDefault("totalSettlementElectricity", BigDecimal.ZERO);
            BigDecimal fee = result.getOrDefault("totalSettlementElectricFee", BigDecimal.ZERO);
            
            totalSettlementPower = totalSettlementPower.add(power);
            totalSettlementFee = totalSettlementFee.add(fee);
        }
        
        // 计算平均价格
        BigDecimal avgPrice = BigDecimal.ZERO;
        if (totalSettlementPower.compareTo(BigDecimal.ZERO) > 0) {
            avgPrice = totalSettlementFee.divide(totalSettlementPower, 2, BigDecimal.ROUND_HALF_UP);
        }
        
        dto.setSettlementPower(totalSettlementPower.doubleValue());
        dto.setSettlementAvgPrice(avgPrice.doubleValue());
        dto.setTotalStations(results.size());
        
        return dto;
    }
    
    /**
     * 转换为结算聚合DTO
     */
    private SettlementAggregationDTO convertToSettlementAggregation(Map<String, Object> data) {
        if (data == null || data.isEmpty()) {
            return new SettlementAggregationDTO();
        }
        
        SettlementAggregationDTO dto = new SettlementAggregationDTO();
        
        // 设置结算电量
        Object powerObj = data.get("totalSettlementPower");
        if (powerObj instanceof BigDecimal) {
            dto.setSettlementPower(((BigDecimal) powerObj).doubleValue());
        } else if (powerObj instanceof Number) {
            dto.setSettlementPower(((Number) powerObj).doubleValue());
        }
        
        // 设置结算均价
        Object priceObj = data.get("avgSettlementPrice");
        if (priceObj instanceof BigDecimal) {
            dto.setSettlementAvgPrice(((BigDecimal) priceObj).doubleValue());
        } else if (priceObj instanceof Number) {
            dto.setSettlementAvgPrice(((Number) priceObj).doubleValue());
        }
        
        // 设置电站数量
        Object stationCountObj = data.get("stationCount");
        if (stationCountObj instanceof Number) {
            dto.setTotalStations(((Number) stationCountObj).intValue());
        }
        
        return dto;
    }
    
    /**
     * 转换为电站结算DTO
     */
    private StationSettlementDTO convertToStationSettlement(Long stationId, Map<String, BigDecimal> data) {
        if (data == null || data.isEmpty()) {
            return null;
        }
        
        StationSettlementDTO dto = new StationSettlementDTO();
        dto.setStationId(stationId);
        
        BigDecimal power = data.getOrDefault("totalSettlementElectricity", BigDecimal.ZERO);
        BigDecimal fee = data.getOrDefault("totalSettlementElectricFee", BigDecimal.ZERO);
        BigDecimal price = data.getOrDefault("avgTradePrice", BigDecimal.ZERO);
        
        dto.setTotalSettlementPower(power);
        dto.setTotalSettlementFee(fee);
        dto.setAvgSettlementPrice(price);
        
        return dto;
    }
    
    /**
     * 转换为电站结算列表
     */
    private List<StationSettlementDTO> convertToStationSettlementList(List<Map<String, Object>> results) {
        if (results == null || results.isEmpty()) {
            return new ArrayList<>();
        }
        
        return results.stream()
            .map(this::convertMapToStationSettlement)
            .filter(dto -> dto != null && dto.getStationId() != null)
            .collect(Collectors.toList());
    }
    
    /**
     * 将Map转换为电站结算DTO
     */
    private StationSettlementDTO convertMapToStationSettlement(Map<String, Object> data) {
        if (data == null || data.isEmpty()) {
            return null;
        }
        
        StationSettlementDTO dto = new StationSettlementDTO();
        
        // 设置电站ID
        Object stationIdObj = data.get("station_id");
        if (stationIdObj instanceof Number) {
            dto.setStationId(((Number) stationIdObj).longValue());
        }
        
        // 设置电站名称
        Object stationNameObj = data.get("stationName");
        if (stationNameObj instanceof String) {
            dto.setStationName((String) stationNameObj);
        }
        
        // 设置电站类型
        Object stationTypeObj = data.get("stationType");
        if (stationTypeObj instanceof Number) {
            dto.setStationType(((Number) stationTypeObj).intValue());
        }
        
        // 设置结算电量
        Object powerObj = data.get("totalSettlementPower");
        if (powerObj instanceof BigDecimal) {
            dto.setTotalSettlementPower((BigDecimal) powerObj);
        } else if (powerObj instanceof Number) {
            dto.setTotalSettlementPower(BigDecimal.valueOf(((Number) powerObj).doubleValue()));
        }
        
        // 设置结算电费
        Object feeObj = data.get("totalSettlementFee");
        if (feeObj instanceof BigDecimal) {
            dto.setTotalSettlementFee((BigDecimal) feeObj);
        } else if (feeObj instanceof Number) {
            dto.setTotalSettlementFee(BigDecimal.valueOf(((Number) feeObj).doubleValue()));
        }
        
        // 设置结算均价
        Object priceObj = data.get("avgSettlementPrice");
        if (priceObj instanceof BigDecimal) {
            dto.setAvgSettlementPrice((BigDecimal) priceObj);
        } else if (priceObj instanceof Number) {
            dto.setAvgSettlementPrice(BigDecimal.valueOf(((Number) priceObj).doubleValue()));
        }
        
        return dto;
    }
    
    /**
     * 获取当前日期
     */
    private String getCurrentDate() {
        return LocalDate.now().format(DATE_FORMATTER);
    }
    
    /**
     * 获取当年开始日期
     */
    private String getCurrentYearStart() {
        return LocalDate.now().withDayOfYear(1).format(DATE_FORMATTER);
    }
}
