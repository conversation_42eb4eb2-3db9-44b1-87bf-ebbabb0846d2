package org.jeecg.modules.api.power_trade.service;

import org.jeecg.modules.api.power.param.PowerGenerationTrendDto;
import org.jeecg.modules.api.power.param.PowerGenerationTrendQueryParam;
import org.jeecg.modules.api.power_trade.dto.DashboardSummaryDTO;
import org.jeecg.modules.api.power_trade.dto.SettlementSummaryDTO;

import java.util.List;
import java.util.Map;

/**
 * 电力交易首页概览数据服务接口
 *
 * <AUTHOR> Agent
 * @since 2025-07-27
 */
public interface PowerDashboardService {

    /**
     * 获取首页概览数据
     *
     * @param provinceId 省份ID (0-全国汇总, 1-江苏, 2-安徽)
     * @return 首页概览数据
     */
    DashboardSummaryDTO getDashboardSummary(Integer provinceId);

    /**
     * 获取全国汇总的首页概览数据
     *
     * @return 全国汇总的首页概览数据
     */
    DashboardSummaryDTO getNationalDashboardSummary();

    /**
     * 获取单个省份的首页概览数据
     *
     * @param provinceId 省份ID
     * @return 单个省份的首页概览数据
     */
    DashboardSummaryDTO getSingleProvinceDashboardSummary(Integer provinceId);

    /**
     * 获取电站交易概况
     *
     * @param stationId 电站ID（可选，为空时返回省份汇总数据）
     * @param provinceId 省份ID
     * @param dimension 查询维度: 1-月度 2-年度
     * @param month 月份(格式: yyyy-MM)，仅在dimension=1时有效
     * @param year 年份(格式: yyyy)，仅在dimension=2时有效
     * @return 电站交易概况列表
     */
    List<SettlementSummaryDTO> getSettlementSummary(Long stationId, Integer provinceId,
                                                   Integer dimension, String month, String year);

    /**
     * 获取全国汇总的电站交易概况
     *
     * @param stationId 电站ID
     * @param dimension 查询维度
     * @param month 月份
     * @param year 年份
     * @return 全国汇总的电站交易概况
     */
    List<SettlementSummaryDTO> getNationalSettlementSummary(Long stationId, Integer dimension,
                                                           String month, String year);

    /**
     * 获取单个省份的电站交易概况
     *
     * @param stationId 电站ID
     * @param provinceId 省份ID
     * @param dimension 查询维度
     * @param month 月份
     * @param year 年份
     * @return 单个省份的电站交易概况
     */
    List<SettlementSummaryDTO> getSingleProvinceSettlementSummary(Long stationId, Integer provinceId,
                                                                 Integer dimension, String month, String year);

    /**
     * 获取发电趋势数据
     *
     * @param param 查询参数
     * @return 发电趋势数据列表
     */
    List<PowerGenerationTrendDto> getPowerGenerationTrend(PowerGenerationTrendQueryParam param);

    /**
     * 获取全国汇总的发电趋势数据
     *
     * @param param 查询参数
     * @return 全国汇总的发电趋势数据
     */
    List<PowerGenerationTrendDto> getNationalPowerGenerationTrend(PowerGenerationTrendQueryParam param);

    /**
     * 获取单个省份的发电趋势数据
     *
     * @param param 查询参数
     * @return 单个省份的发电趋势数据
     */
    List<PowerGenerationTrendDto> getSingleProvincePowerGenerationTrend(PowerGenerationTrendQueryParam param);

    /**
     * 获取电量电价数据
     *
     * @param provinceId 省份ID，0表示根据电站ID自动获取省份
     * @param stationId 电站ID
     * @param date 日期
     * @param dimension 维度: 1-年度, 2-月度, 3-日度
     * @return 电量电价数据
     */
    Object getElectricityPriceData(Integer provinceId, Long stationId, String date, String dimension);

    /**
     * 根据电站ID获取省份ID
     *
     * @param stationId 电站ID
     * @return 省份ID
     */
    Integer getProvinceIdByStationId(Long stationId);

    /**
     * 获取发电趋势数据
     *
     * @param provinceId 省份ID
     * @param dimension  时间维度 (day/month/year)
     * @param stationId  场站ID
     * @param date       查询日期/年月/年份
     * @return 发电趋势数据
     */
    Map<String, Object> getPowerGenerationTrend(Integer provinceId, String dimension, Long stationId, String date);

}
