package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("market_notice")
public class MarketNotice {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @TableField("notice_title")
    @ApiModelProperty(value = "公告标题", example = "关于电力市场交易的通知")
    private String noticeTitle;

    @TableField("type_id")
    @ApiModelProperty(value = "公告类型ID(关联公告类型表主键)", example = "1")
    private Integer typeId;

    @TableField("notice_date")
    @ApiModelProperty(value = "披露日期", example = "2023-01-01")
    private Date noticeDate;

    @TableField("notice_label")
    @ApiModelProperty(value = "公告标签", example = "电力交易,市场公告")
    private String noticeLabel;

    @TableField("notice_content")
    @ApiModelProperty(value = "公告内容", example = "本次电力市场交易将于XX月XX日进行...")
    private String noticeContent;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    private Date createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间", example = "2023-01-01 12:00:00")
    private Date updateTime;

    @TableField("create_by")
    @ApiModelProperty(value = "创建人", example = "admin")
    private String createBy;

    @TableField("update_by")
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updateBy;

    @TableField(exist = false)
    @ApiModelProperty(value = "公告类型名称(非数据库字段)", example = "交易公告")
    private String typeName;

    @TableField(exist = false)
    @ApiModelProperty(value = "公告附件列表(非数据库字段)")
    private List<NoticeFile> noticeFiles;
}