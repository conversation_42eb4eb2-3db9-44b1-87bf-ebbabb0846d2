<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.EnergyNewDailyCleanMapper">

    <!-- 按日查询明细数据 -->
    <select id="selectByDay" resultType="org.jeecg.modules.api.power_trade.entity.EnergyNewDailyClean">
        SELECT
            id,
            date,
            station_id as stationId,
            mid_long_term_power as midLongTermPower,
            mid_long_term_price as midLongTermPrice,
            mid_long_term_fee as midLongTermFee,
            guarantee_power as guaranteePower,
            guarantee_price as guaranteePrice,
            guarantee_fee as guaranteeFee,
            day_ahead_deviation_power as dayAheadDeviationPower,
            day_ahead_deviation_price as dayAheadDeviationPrice,
            day_ahead_deviation_fee as dayAheadDeviationFee,
            realtime_deviation_power as realtimeDeviationPower,
            realtime_deviation_price as realtimeDeviationPrice,
            realtime_deviation_fee as realtimeDeviationFee,
            excess_profit_recovery as excessProfitRecovery,
            day_ahead_profit_recovery as dayAheadProfitRecovery,
            total_power as totalPower,
            total_fee as totalFee,
            settlement_avg_price as settlementAvgPrice,
            create_time as createTime,
            update_time as updateTime,
            create_by as createBy,
            update_by as updateBy
        FROM energy_new_daily_clean
        WHERE station_id = #{stationId}
        AND DATE(date) = #{date}
        ORDER BY date
    </select>

    <!-- 按月查询所有日期明细数据 -->
    <select id="selectByMonth" resultType="org.jeecg.modules.api.power_trade.entity.EnergyNewDailyClean">
        SELECT
            id,
            date,
            station_id as stationId,
            mid_long_term_power as midLongTermPower,
            mid_long_term_price as midLongTermPrice,
            mid_long_term_fee as midLongTermFee,
            guarantee_power as guaranteePower,
            guarantee_price as guaranteePrice,
            guarantee_fee as guaranteeFee,
            day_ahead_deviation_power as dayAheadDeviationPower,
            day_ahead_deviation_price as dayAheadDeviationPrice,
            day_ahead_deviation_fee as dayAheadDeviationFee,
            realtime_deviation_power as realtimeDeviationPower,
            realtime_deviation_price as realtimeDeviationPrice,
            realtime_deviation_fee as realtimeDeviationFee,
            excess_profit_recovery as excessProfitRecovery,
            day_ahead_profit_recovery as dayAheadProfitRecovery,
            total_power as totalPower,
            total_fee as totalFee,
            settlement_avg_price as settlementAvgPrice,
            create_time as createTime,
            update_time as updateTime,
            create_by as createBy,
            update_by as updateBy
        FROM energy_new_daily_clean
        WHERE station_id = #{stationId}
        AND DATE_FORMAT(date, '%Y-%m') = #{yearMonth}
        ORDER BY date
    </select>

    <!-- 按年查询所有日期明细数据 -->
    <select id="selectByYear" resultType="org.jeecg.modules.api.power_trade.entity.EnergyNewDailyClean">
        SELECT
            id,
            date,
            station_id as stationId,
            mid_long_term_power as midLongTermPower,
            mid_long_term_price as midLongTermPrice,
            mid_long_term_fee as midLongTermFee,
            guarantee_power as guaranteePower,
            guarantee_price as guaranteePrice,
            guarantee_fee as guaranteeFee,
            day_ahead_deviation_power as dayAheadDeviationPower,
            day_ahead_deviation_price as dayAheadDeviationPrice,
            day_ahead_deviation_fee as dayAheadDeviationFee,
            realtime_deviation_power as realtimeDeviationPower,
            realtime_deviation_price as realtimeDeviationPrice,
            realtime_deviation_fee as realtimeDeviationFee,
            excess_profit_recovery as excessProfitRecovery,
            day_ahead_profit_recovery as dayAheadProfitRecovery,
            total_power as totalPower,
            total_fee as totalFee,
            settlement_avg_price as settlementAvgPrice,
            create_time as createTime,
            update_time as updateTime,
            create_by as createBy,
            update_by as updateBy
        FROM energy_new_daily_clean
        WHERE station_id = #{stationId}
        AND YEAR(date) = #{year}
        ORDER BY date
    </select>

    <!-- 按日期范围查询日交易信息 -->
    <select id="selectByDateRange" resultType="org.jeecg.modules.api.power_trade.entity.EnergyNewDailyClean">
        SELECT
            id,
            date,
            station_id as stationId,
            mid_long_term_power as midLongTermPower,
            mid_long_term_price as midLongTermPrice,
            mid_long_term_fee as midLongTermFee,
            guarantee_power as guaranteePower,
            guarantee_price as guaranteePrice,
            guarantee_fee as guaranteeFee,
            day_ahead_deviation_power as dayAheadDeviationPower,
            day_ahead_deviation_price as dayAheadDeviationPrice,
            day_ahead_deviation_fee as dayAheadDeviationFee,
            realtime_deviation_power as realtimeDeviationPower,
            realtime_deviation_price as realtimeDeviationPrice,
            realtime_deviation_fee as realtimeDeviationFee,
            excess_profit_recovery as excessProfitRecovery,
            day_ahead_profit_recovery as dayAheadProfitRecovery,
            total_power as totalPower,
            total_fee as totalFee,
            settlement_avg_price as settlementAvgPrice,
            create_time as createTime,
            update_time as updateTime,
            create_by as createBy,
            update_by as updateBy
        FROM energy_new_daily_clean
        WHERE date BETWEEN #{startDate} AND #{endDate}
        <if test="stationType != null and stationType != ''">
            <!-- 这里需要根据实际业务逻辑添加电站类型过滤条件 -->
            <!-- 可能需要关联station表来获取电站类型信息 -->
        </if>
        ORDER BY date ASC, station_id ASC
    </select>

</mapper>