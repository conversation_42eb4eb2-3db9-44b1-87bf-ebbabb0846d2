package org.jeecg.modules.api.power_trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean;

import java.util.List;

public interface EnergyStorageDailyCleanMapper extends BaseMapper<EnergyStorageDailyClean> {

    /**
     * 按日查询明细数据
     */
    List<EnergyStorageDailyClean> selectByDay(@Param("stationId") String stationId,
                                              @Param("date") String date);

    /**
     * 按月汇总查询
     */
    List<EnergyStorageDailyClean> selectByMonth(@Param("stationId") String stationId,
                                                @Param("yearMonth") String yearMonth);

    /**
     * 按年汇总查询
     */
    List<EnergyStorageDailyClean> selectByYear(@Param("stationId") String stationId,
                                               @Param("year") String year);

    /**
     * 按日期范围查询储能日交易信息
     */
    List<EnergyStorageDailyClean> selectByDateRange(@Param("startDate") String startDate,
                                                    @Param("endDate") String endDate);
}