package org.jeecg.modules.api.power_trade.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 电站列表数据VO
 * 用于电站总览接口，包含电站基本信息、结算数据和市场交易数据
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024
 */
@Data
@ApiModel(value = "StationListVO", description = "电站列表信息，包含基本信息、结算数据和市场交易数据")
public class StationListVO {

    // ==================== 电站基本信息 ====================

    @ApiModelProperty(value = "电站ID", example = "1001", required = true)
    private Long stationId;

    @ApiModelProperty(value = "电站名称", example = "阳光电站", required = true)
    private String stationName;

    @ApiModelProperty(value = "电站类型", example = "风电", notes = "风电、光伏、储能等", required = true)
    private String stationType;

    @ApiModelProperty(value = "装机容量(MW)", example = "100.50", notes = "电站的总装机容量，单位为兆瓦")
    private BigDecimal capacity;

    @ApiModelProperty(value = "省份ID", example = "1", notes = "1-江苏, 2-安徽", required = true)
    private Integer provinceId;

    @ApiModelProperty(value = "省份名称", example = "江苏省", required = true)
    private String provinceName;

    @ApiModelProperty(value = "交易状态", example = "1", notes = "1-参与交易, 0-未参与交易", required = true)
    private Integer tradeStatus;

    // ==================== 查询参数信息 ====================

    @ApiModelProperty(value = "查询的年份", example = "2024", notes = "查询数据对应的年份")
    private String queryYear;

    @ApiModelProperty(value = "查询的月份", example = "01", notes = "查询数据对应的月份，格式为MM")
    private String queryMonth;

    // ==================== 结算数据 ====================

    @ApiModelProperty(value = "该电站结算总电量", example = "150.25", notes = "该电站在查询时间范围内的结算总电量")
    private Double stationSettlementPower;

    @ApiModelProperty(value = "结算电量单位", example = "GWh", notes = "结算电量的计量单位")
    private String settlementPowerUnit;

    @ApiModelProperty(value = "该电站结算均价", example = "350.80", notes = "该电站在查询时间范围内的结算平均价格")
    private Double stationSettlementAvgPrice;

    @ApiModelProperty(value = "结算均价单位", example = "元/MWh", notes = "结算价格的计量单位")
    private String settlementAvgPriceUnit;

    @ApiModelProperty(value = "该电站标杆电价", example = "300.00", notes = "该电站对应的标杆电价")
    private Double stationCoalBenchmarkPrice;

    @ApiModelProperty(value = "标杆电价单位", example = "元/MWh", notes = "标杆电价的计量单位")
    private String coalBenchmarkPriceUnit;

    @ApiModelProperty(value = "该电站结算记录数量", example = "12", notes = "该电站在查询时间范围内的结算记录总数")
    private Integer stationSettlementCount;

    @ApiModelProperty(value = "该电站是否有结算数据", example = "true", notes = "标识该电站是否存在结算数据")
    private Boolean hasSettlementData;

    // ==================== 市场交易数据 ====================

    @ApiModelProperty(value = "市场交易总电量", example = "200.50", notes = "该电站在查询时间范围内的市场交易总电量")
    private Double marketTradePower;

    @ApiModelProperty(value = "市场交易电量单位", example = "GWh", notes = "市场交易电量的计量单位")
    private String marketTradePowerUnit;

    @ApiModelProperty(value = "市场交易平均价格", example = "380.60", notes = "该电站市场交易的加权平均价格")
    private Double marketTradeAvgPrice;

    @ApiModelProperty(value = "市场交易价格单位", example = "元/MWh", notes = "市场交易价格的计量单位")
    private String marketTradeAvgPriceUnit;

    @ApiModelProperty(value = "市场交易平均放电价格", example = "360.40", notes = "该电站市场交易的平均放电价格")
    private Double marketTradeAvgDischargePrice;

    @ApiModelProperty(value = "市场交易月份数量", example = "8", notes = "该电站在查询年份内参与市场交易的月份数量")
    private Integer marketTradeMonthCount;

    @ApiModelProperty(value = "该电站是否有市场交易数据", example = "true", notes = "标识该电站是否存在市场交易数据")
    private Boolean hasMarketTradeData;

    // ==================== 综合分析数据 ====================

    @ApiModelProperty(value = "交易电量占比", example = "75.5", notes = "市场交易电量占总发电量的百分比")
    private Double tradeRatio;

    @ApiModelProperty(value = "预估交易收入", example = "76200.50", notes = "基于市场交易数据计算的预估收入，单位：元")
    private Double estimatedTradeIncome;

    @ApiModelProperty(value = "收入单位", example = "元", notes = "预估交易收入的计量单位")
    private String incomeUnit;
}