package org.jeecg.modules.api.power_trade.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.api.power_trade.entity.PolicyAnnouncement;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 政策公告视图对象
 */
@Data
@ApiModel(value = "PolicyAnnouncementVO", description = "政策公告视图对象")
public class PolicyAnnouncementVO {

    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "政策公告标题", example = "关于电力市场政策的通知")
    private String title;

    @ApiModelProperty(value = "政策公告内容", example = "政策内容详情...")
    private String content;

    @ApiModelProperty(value = "摘要", example = "政策摘要...")
    private String summary;

    @ApiModelProperty(value = "政策类型ID", example = "1")
    private Integer typeId;

    @ApiModelProperty(value = "政策类型名称", example = "市场政策")
    private String typeName;

    @ApiModelProperty(value = "附件文件URL", example = "http://example.com/files/policy.pdf")
    private String fileUrl;

    @ApiModelProperty(value = "附件文件名", example = "policy.pdf")
    private String fileName;

    @ApiModelProperty(value = "发布日期", example = "2024-01-01")
    private Date publishDate;

    @ApiModelProperty(value = "浏览次数", example = "100")
    private Integer viewCount;

    @ApiModelProperty(value = "标签列表", example = "[\"江苏\", \"市场政策\"]")
    private List<String> tags;

    @ApiModelProperty(value = "附件数量", example = "1")
    private Integer attachmentCount;

    @ApiModelProperty(value = "省份标识", example = "江苏")
    private String provinceTag;

    @ApiModelProperty(value = "封面图片URL", example = "http://example.com/cover.jpg")
    private String coverUrl;

    /**
     * 从实体转换为VO
     */
    public static PolicyAnnouncementVO fromEntity(PolicyAnnouncement entity) {
        if (entity == null) {
            return null;
        }

        PolicyAnnouncementVO vo = new PolicyAnnouncementVO();
        vo.setId(entity.getId());
        vo.setTitle(entity.getTitle());
        vo.setContent(entity.getContent());
        vo.setSummary(entity.getSummary());
        vo.setTypeId(entity.getTypeId());
        vo.setTypeName(entity.getTypeName());
        vo.setFileUrl(entity.getFileUrl());
        vo.setFileName(entity.getFileName());
        vo.setPublishDate(entity.getPublishDate());
        vo.setViewCount(entity.getViewCount());
        vo.setTags(entity.getTags());
        
        // 设置附件数量
        vo.setAttachmentCount(entity.getFileUrl() != null && !entity.getFileUrl().trim().isEmpty() ? 1 : 0);
        
        return vo;
    }

    /**
     * 设置省份相关信息
     */
    public void setProvinceInfo(String provinceName) {
        this.provinceTag = provinceName;
        if (this.tags == null) {
            this.tags = Arrays.asList(provinceName, this.typeName != null ? this.typeName : "政策公告");
        } else if (!this.tags.contains(provinceName)) {
            this.tags.add(0, provinceName);
        }
    }

    /**
     * 获取简短摘要（用于列表显示）
     */
    public String getShortSummary() {
        if (summary != null && summary.length() > 100) {
            return summary.substring(0, 100) + "...";
        }
        return summary;
    }

    /**
     * 获取简短标题（用于列表显示）
     */
    public String getShortTitle() {
        if (title != null && title.length() > 50) {
            return title.substring(0, 50) + "...";
        }
        return title;
    }

    /**
     * 检查是否有附件
     */
    public boolean hasAttachment() {
        return fileUrl != null && !fileUrl.trim().isEmpty();
    }

    /**
     * 获取文件扩展名
     */
    public String getFileExtension() {
        if (fileName != null && fileName.contains(".")) {
            return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        }
        return null;
    }

    /**
     * 获取格式化的发布日期字符串
     */
    public String getFormattedPublishDate() {
        if (publishDate != null) {
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
            return sdf.format(publishDate);
        }
        return null;
    }

    /**
     * 获取相对发布时间（如：3天前）
     */
    public String getRelativePublishTime() {
        if (publishDate == null) {
            return null;
        }
        
        long diff = System.currentTimeMillis() - publishDate.getTime();
        long days = diff / (24 * 60 * 60 * 1000);
        
        if (days == 0) {
            return "今天";
        } else if (days == 1) {
            return "昨天";
        } else if (days < 7) {
            return days + "天前";
        } else if (days < 30) {
            return (days / 7) + "周前";
        } else if (days < 365) {
            return (days / 30) + "个月前";
        } else {
            return (days / 365) + "年前";
        }
    }
}
