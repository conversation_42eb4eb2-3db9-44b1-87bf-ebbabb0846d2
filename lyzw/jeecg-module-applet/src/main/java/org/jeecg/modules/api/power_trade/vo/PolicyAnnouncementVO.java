package org.jeecg.modules.api.power_trade.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.api.power_trade.entity.PolicyAnnouncement;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 政策公告视图对象
 */
@Data
@ApiModel(value = "PolicyAnnouncementVO", description = "政策公告视图对象")
public class PolicyAnnouncementVO {

    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "附件文件URL", example = "http://example.com/files/policy.pdf")
    private String fileUrl;

    @ApiModelProperty(value = "附件文件名", example = "policy.pdf")
    private String fileName;

}
