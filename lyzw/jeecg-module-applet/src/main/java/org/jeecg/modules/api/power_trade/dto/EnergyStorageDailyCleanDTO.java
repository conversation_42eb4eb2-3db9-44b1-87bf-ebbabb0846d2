package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 储能日清分数据DTO
 * 用于储能接口的返回
 */
@Data
@ApiModel(value = "储能日清分数据DTO", description = "储能日清分数据传输对象")
public class EnergyStorageDailyCleanDTO {

    @ApiModelProperty(value = "记录ID", example = "12090")
    private Long id;

    @ApiModelProperty(value = "日期", required = true, example = "2025-07-01")
    private Date date;

    @ApiModelProperty(value = "电站ID", required = true, example = "2")
    private Long stationId;

    @ApiModelProperty(value = "用户侧日前偏差电量(MWh)", example = "376.80")
    private BigDecimal userDayAheadDeviationPower;

    @ApiModelProperty(value = "用户侧日前偏差均价(元/MWh)", example = "832.70")
    private BigDecimal userDayAheadDeviationAveragePrice;

    @ApiModelProperty(value = "用户侧日前偏差电费(元)", example = "313778.30")
    private BigDecimal userDayAheadDeviationFee;

    @ApiModelProperty(value = "用户侧实时偏差电量(MWh)", example = "-43.90")
    private BigDecimal userRealtimeDeviationPower;

    @ApiModelProperty(value = "用户侧实时偏差均价(元/MWh)", example = "-119.70")
    private BigDecimal userRealtimeDeviationAveragePrice;

    @ApiModelProperty(value = "用户侧实时偏差电费(元)", example = "5251.30")
    private BigDecimal userRealtimeDeviationFee;

    @ApiModelProperty(value = "用户侧总电量(MWh)", example = "199.10")
    private BigDecimal userTotalPower;

    @ApiModelProperty(value = "用户侧总电费(元)", example = "89633.00")
    private BigDecimal userTotalFee;

    @ApiModelProperty(value = "发电侧日前偏差电量(MWh)", example = "51.50")
    private BigDecimal powerGenerationDayAheadDeviationPower;

    @ApiModelProperty(value = "发电侧日前偏差均价(元/MWh)", example = "384.40")
    private BigDecimal powerGenerationDayAheadDeviationAveragePrice;

    @ApiModelProperty(value = "发电侧日前偏差电费(元)", example = "19801.80")
    private BigDecimal powerGenerationDayAheadDeviationFee;

    @ApiModelProperty(value = "发电侧实时偏差电量(MWh)", example = "276.60")
    private BigDecimal powerGenerationRealtimeDeviationPower;

    @ApiModelProperty(value = "发电侧实时偏差均价(元/MWh)", example = "447.90")
    private BigDecimal powerGenerationRealtimeDeviationAveragePrice;

    @ApiModelProperty(value = "发电侧实时偏差电费(元)", example = "123868.40")
    private BigDecimal powerGenerationRealtimeDeviationFee;

    @ApiModelProperty(value = "发电侧总电量(MWh)", example = "1133.30")
    private BigDecimal powerGenerationTotalPower;

    @ApiModelProperty(value = "发电侧总电费(元)", example = "460279.00")
    private BigDecimal powerGenerationTotalFee;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
