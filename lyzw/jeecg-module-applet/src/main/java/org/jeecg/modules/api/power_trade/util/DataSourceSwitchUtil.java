package org.jeecg.modules.api.power_trade.util;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.ProvinceDataSourceUtil;

import java.util.function.Supplier;

/**
 * 数据源切换工具类
 * 统一管理多数据源切换逻辑，确保资源正确释放
 * 
 * <AUTHOR> Team
 * @since 2025-01-01
 */
@Slf4j
public class DataSourceSwitchUtil {

    /**
     * 执行带数据源切换的操作（有返回值）
     * 
     * @param provinceId 省份ID
     * @param operation  业务操作
     * @param <T>        返回值类型
     * @return 操作结果
     * @throws ParameterValidator.ValidationException 省份ID不支持时抛出
     */
    public static <T> T executeWithDataSource(Integer provinceId, Supplier<T> operation) {
        // 验证省份ID
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            throw new ParameterValidator.ValidationException("不支持的省份ID: " + provinceId);
        }
        
        // 记录当前数据源
        String originalDataSource = DynamicDataSourceContextHolder.peek();
        
        log.debug("切换数据源 - 省份ID: {}, 数据源: {}, 原数据源: {}", provinceId, dsKey, originalDataSource);
        
        // 切换数据源并执行操作
        DynamicDataSourceContextHolder.push(dsKey);
        try {
            T result = operation.get();
            log.debug("数据源操作完成 - 省份ID: {}, 数据源: {}", provinceId, dsKey);
            return result;
        } catch (Exception e) {
            log.error("数据源操作失败 - 省份ID: {}, 数据源: {}", provinceId, dsKey, e);
            throw e;
        } finally {
            // 确保数据源正确恢复
            DynamicDataSourceContextHolder.clear();
            if (originalDataSource != null) {
                DynamicDataSourceContextHolder.push(originalDataSource);
            }
            log.debug("数据源已恢复 - 省份ID: {}, 恢复到: {}", provinceId, originalDataSource);
        }
    }

    /**
     * 执行带数据源切换的操作（无返回值）
     * 
     * @param provinceId 省份ID
     * @param operation  业务操作
     * @throws ParameterValidator.ValidationException 省份ID不支持时抛出
     */
    public static void executeWithDataSource(Integer provinceId, Runnable operation) {
        executeWithDataSource(provinceId, () -> {
            operation.run();
            return null;
        });
    }

    /**
     * 执行带数据源切换的操作（带异常处理）
     * 
     * @param provinceId      省份ID
     * @param operation       业务操作
     * @param exceptionHandler 异常处理器
     * @param <T>             返回值类型
     * @return 操作结果，异常时返回null
     */
    public static <T> T executeWithDataSourceSafely(Integer provinceId, 
                                                    Supplier<T> operation, 
                                                    java.util.function.Consumer<Exception> exceptionHandler) {
        try {
            return executeWithDataSource(provinceId, operation);
        } catch (Exception e) {
            if (exceptionHandler != null) {
                exceptionHandler.accept(e);
            } else {
                log.error("数据源操作异常 - 省份ID: {}", provinceId, e);
            }
            return null;
        }
    }

    /**
     * 执行带数据源切换的操作（带默认值）
     * 
     * @param provinceId   省份ID
     * @param operation    业务操作
     * @param defaultValue 默认值
     * @param <T>          返回值类型
     * @return 操作结果，异常时返回默认值
     */
    public static <T> T executeWithDataSourceOrDefault(Integer provinceId, 
                                                       Supplier<T> operation, 
                                                       T defaultValue) {
        return executeWithDataSourceSafely(provinceId, operation, e -> 
            log.warn("数据源操作失败，返回默认值 - 省份ID: {}, 默认值: {}", provinceId, defaultValue, e));
    }

    /**
     * 批量执行多省份数据源操作
     * 
     * @param provinceIds 省份ID列表
     * @param operation   业务操作
     * @param <T>         返回值类型
     * @return 操作结果列表
     */
    public static <T> java.util.List<T> executeWithMultipleDataSources(java.util.List<Integer> provinceIds, 
                                                                       java.util.function.Function<Integer, T> operation) {
        if (provinceIds == null || provinceIds.isEmpty()) {
            return new java.util.ArrayList<>();
        }
        
        return provinceIds.stream()
                .map(provinceId -> executeWithDataSourceSafely(provinceId, 
                    () -> operation.apply(provinceId), 
                    e -> log.error("省份{}数据源操作失败", provinceId, e)))
                .filter(java.util.Objects::nonNull)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 验证省份ID是否支持
     * 
     * @param provinceId 省份ID
     * @return 是否支持
     */
    public static boolean isProvinceSupported(Integer provinceId) {
        return ProvinceDataSourceUtil.getDataSourceKey(provinceId) != null;
    }

    /**
     * 获取省份对应的数据源名称
     * 
     * @param provinceId 省份ID
     * @return 数据源名称，不支持时返回null
     */
    public static String getDataSourceName(Integer provinceId) {
        return ProvinceDataSourceUtil.getDataSourceKey(provinceId);
    }

    /**
     * 获取省份名称
     * 
     * @param provinceId 省份ID
     * @return 省份名称，不支持时返回null
     */
    public static String getProvinceName(Integer provinceId) {
        return ProvinceDataSourceUtil.getProvinceName(provinceId);
    }

    /**
     * 获取当前数据源
     * 
     * @return 当前数据源名称
     */
    public static String getCurrentDataSource() {
        return DynamicDataSourceContextHolder.peek();
    }

    /**
     * 数据源操作结果包装类
     * 
     * @param <T> 结果类型
     */
    public static class DataSourceResult<T> {
        private final T data;
        private final boolean success;
        private final String errorMessage;
        private final Integer provinceId;
        private final String dataSource;

        private DataSourceResult(T data, boolean success, String errorMessage, Integer provinceId, String dataSource) {
            this.data = data;
            this.success = success;
            this.errorMessage = errorMessage;
            this.provinceId = provinceId;
            this.dataSource = dataSource;
        }

        public static <T> DataSourceResult<T> success(T data, Integer provinceId, String dataSource) {
            return new DataSourceResult<>(data, true, null, provinceId, dataSource);
        }

        public static <T> DataSourceResult<T> failure(String errorMessage, Integer provinceId, String dataSource) {
            return new DataSourceResult<>(null, false, errorMessage, provinceId, dataSource);
        }

        public T getData() {
            return data;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public Integer getProvinceId() {
            return provinceId;
        }

        public String getDataSource() {
            return dataSource;
        }

        @Override
        public String toString() {
            return String.format("DataSourceResult{success=%s, provinceId=%d, dataSource='%s', errorMessage='%s'}", 
                success, provinceId, dataSource, errorMessage);
        }
    }

    /**
     * 执行带结果包装的数据源操作
     * 
     * @param provinceId 省份ID
     * @param operation  业务操作
     * @param <T>        返回值类型
     * @return 包装的操作结果
     */
    public static <T> DataSourceResult<T> executeWithResult(Integer provinceId, Supplier<T> operation) {
        String dataSource = getDataSourceName(provinceId);
        
        if (dataSource == null) {
            return DataSourceResult.failure("不支持的省份ID: " + provinceId, provinceId, null);
        }
        
        try {
            T result = executeWithDataSource(provinceId, operation);
            return DataSourceResult.success(result, provinceId, dataSource);
        } catch (Exception e) {
            return DataSourceResult.failure(e.getMessage(), provinceId, dataSource);
        }
    }

    /**
     * 数据源操作统计信息
     */
    public static class DataSourceStats {
        private int totalOperations = 0;
        private int successfulOperations = 0;
        private int failedOperations = 0;
        private final java.util.Map<Integer, Integer> provinceOperationCount = new java.util.HashMap<>();

        public void recordOperation(Integer provinceId, boolean success) {
            totalOperations++;
            if (success) {
                successfulOperations++;
            } else {
                failedOperations++;
            }
            provinceOperationCount.merge(provinceId, 1, Integer::sum);
        }

        public double getSuccessRate() {
            return totalOperations == 0 ? 0.0 : (double) successfulOperations / totalOperations;
        }

        public int getTotalOperations() {
            return totalOperations;
        }

        public int getSuccessfulOperations() {
            return successfulOperations;
        }

        public int getFailedOperations() {
            return failedOperations;
        }

        public java.util.Map<Integer, Integer> getProvinceOperationCount() {
            return new java.util.HashMap<>(provinceOperationCount);
        }

        @Override
        public String toString() {
            return String.format("DataSourceStats{total=%d, success=%d, failed=%d, successRate=%.2f%%}", 
                totalOperations, successfulOperations, failedOperations, getSuccessRate() * 100);
        }
    }
}
