package org.jeecg.modules.api.power_trade.service;

import org.jeecg.modules.api.power_trade.dto.ElectricityDataDTO;

import java.util.List;
import java.util.Map;

public interface IElectricityDataService {

    /**
     * 根据省份获取电量数据
     * @param provinceId 省份ID
     * @param stationId 电站ID
     * @param date 日期
     * @param dimension 维度
     * @return 电量数据
     */
    Object getElectricityDataByProvince(Integer provinceId, Long stationId, String date, String dimension);
}