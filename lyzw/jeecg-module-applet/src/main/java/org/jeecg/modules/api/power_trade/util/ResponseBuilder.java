package org.jeecg.modules.api.power_trade.util;

import org.jeecg.common.api.vo.Result;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 统一响应结果构建工具类
 * 标准化API响应格式，提供统一的成功和失败响应构建方法
 * 
 * <AUTHOR> Team
 * @since 2025-01-01
 */
@Slf4j
public class ResponseBuilder {

    /**
     * 成功响应（带数据）
     * 
     * @param data 响应数据
     * @param <T>  数据类型
     * @return 成功响应结果
     */
    public static <T> Result<T> success(T data) {
        return Result.OK(data);
    }

    /**
     * 成功响应（无数据）
     * 
     * @return 成功响应结果
     */
    public static Result<Void> success() {
        return Result.OK();
    }

    /**
     * 成功响应（带消息）
     * 
     * @param message 成功消息
     * @param <T>     数据类型
     * @return 成功响应结果
     */
    public static <T> Result<T> success(String message) {
        Result<T> result = Result.OK();
        result.setMessage(message);
        return result;
    }

    /**
     * 成功响应（带数据和消息）
     * 
     * @param data    响应数据
     * @param message 成功消息
     * @param <T>     数据类型
     * @return 成功响应结果
     */
    public static <T> Result<T> success(T data, String message) {
        Result<T> result = Result.OK(data);
        result.setMessage(message);
        return result;
    }

    /**
     * 错误响应
     * 
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 错误响应结果
     */
    public static <T> Result<T> error(String message) {
        return Result.error(message);
    }

    /**
     * 错误响应（带错误码）
     * 
     * @param code    错误码
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 错误响应结果
     */
    public static <T> Result<T> error(Integer code, String message) {
        return Result.error(code, message);
    }

    /**
     * 参数验证错误响应
     * 
     * @param message 验证错误消息
     * @param <T>     数据类型
     * @return 参数验证错误响应结果
     */
    public static <T> Result<T> validationError(String message) {
        return Result.error(400, "参数验证失败: " + message);
    }

    /**
     * 业务异常响应
     * 
     * @param message 业务异常消息
     * @param <T>     数据类型
     * @return 业务异常响应结果
     */
    public static <T> Result<T> businessError(String message) {
        return Result.error(500, "业务处理失败: " + message);
    }

    /**
     * 数据不存在响应
     * 
     * @param resource 资源名称
     * @param <T>      数据类型
     * @return 数据不存在响应结果
     */
    public static <T> Result<T> notFound(String resource) {
        return Result.error(404, resource + "不存在");
    }

    /**
     * 权限不足响应
     * 
     * @param <T> 数据类型
     * @return 权限不足响应结果
     */
    public static <T> Result<T> forbidden() {
        return Result.error(403, "权限不足");
    }

    /**
     * 权限不足响应（带消息）
     * 
     * @param message 权限错误消息
     * @param <T>     数据类型
     * @return 权限不足响应结果
     */
    public static <T> Result<T> forbidden(String message) {
        return Result.error(403, "权限不足: " + message);
    }

    /**
     * 系统异常响应
     * 
     * @param <T> 数据类型
     * @return 系统异常响应结果
     */
    public static <T> Result<T> systemError() {
        return Result.error(500, "系统异常，请稍后重试");
    }

    /**
     * 系统异常响应（带异常信息）
     * 
     * @param exception 异常对象
     * @param <T>       数据类型
     * @return 系统异常响应结果
     */
    public static <T> Result<T> systemError(Exception exception) {
        log.error("系统异常", exception);
        return Result.error(500, "系统异常: " + exception.getMessage());
    }

    /**
     * 分页数据成功响应
     * 
     * @param data       分页数据
     * @param total      总记录数
     * @param pageNo     当前页码
     * @param pageSize   每页大小
     * @param <T>        数据类型
     * @return 分页成功响应结果
     */
    public static <T> Result<Map<String, Object>> pageSuccess(List<T> data, long total, int pageNo, int pageSize) {
        Map<String, Object> pageResult = new java.util.HashMap<>();
        pageResult.put("records", data);
        pageResult.put("total", total);
        pageResult.put("current", pageNo);
        pageResult.put("size", pageSize);
        pageResult.put("pages", (total + pageSize - 1) / pageSize);
        
        return Result.OK(pageResult);
    }

    /**
     * 空数据成功响应
     * 
     * @param message 提示消息
     * @param <T>     数据类型
     * @return 空数据成功响应结果
     */
    public static <T> Result<List<T>> emptySuccess(String message) {
        Result<List<T>> result = Result.OK(new java.util.ArrayList<>());
        result.setMessage(message);
        return result;
    }

    /**
     * 条件响应构建器
     * 
     * @param condition 条件
     * @param <T>       数据类型
     * @return 条件响应构建器
     */
    public static <T> ConditionalResponseBuilder<T> when(boolean condition) {
        return new ConditionalResponseBuilder<>(condition);
    }

    /**
     * 异常安全的响应构建
     * 
     * @param supplier 数据提供者
     * @param <T>      数据类型
     * @return 响应结果
     */
    public static <T> Result<T> safeExecute(java.util.function.Supplier<T> supplier) {
        try {
            T data = supplier.get();
            return success(data);
        } catch (ParameterValidator.ValidationException e) {
            return validationError(e.getMessage());
        } catch (BusinessException e) {
            return businessError(e.getMessage());
        } catch (Exception e) {
            return systemError(e);
        }
    }

    /**
     * 异常安全的响应构建（无返回值）
     * 
     * @param runnable 执行操作
     * @return 响应结果
     */
    public static Result<Void> safeExecute(Runnable runnable) {
        return safeExecute(() -> {
            runnable.run();
            return null;
        });
    }

    /**
     * 条件响应构建器
     * 
     * @param <T> 数据类型
     */
    public static class ConditionalResponseBuilder<T> {
        private final boolean condition;

        private ConditionalResponseBuilder(boolean condition) {
            this.condition = condition;
        }

        /**
         * 条件为真时的响应
         * 
         * @param supplier 数据提供者
         * @return 条件响应构建器
         */
        public ConditionalResponseBuilder<T> thenReturn(java.util.function.Supplier<Result<T>> supplier) {
            if (condition) {
                return new ConditionalResponseBuilder<T>(false) {
                    @Override
                    public Result<T> orElse(java.util.function.Supplier<Result<T>> elseSupplier) {
                        return supplier.get();
                    }
                };
            }
            return this;
        }

        /**
         * 条件为假时的响应
         * 
         * @param supplier 数据提供者
         * @return 响应结果
         */
        public Result<T> orElse(java.util.function.Supplier<Result<T>> supplier) {
            return supplier.get();
        }

        /**
         * 条件为假时返回错误
         * 
         * @param message 错误消息
         * @return 响应结果
         */
        public Result<T> orError(String message) {
            return error(message);
        }

        /**
         * 条件为假时返回验证错误
         * 
         * @param message 验证错误消息
         * @return 响应结果
         */
        public Result<T> orValidationError(String message) {
            return validationError(message);
        }
    }

    /**
     * 业务异常类
     */
    public static class BusinessException extends RuntimeException {
        public BusinessException(String message) {
            super(message);
        }

        public BusinessException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 响应状态码常量
     */
    public static class StatusCode {
        public static final int SUCCESS = 200;
        public static final int BAD_REQUEST = 400;
        public static final int UNAUTHORIZED = 401;
        public static final int FORBIDDEN = 403;
        public static final int NOT_FOUND = 404;
        public static final int INTERNAL_SERVER_ERROR = 500;
        public static final int SERVICE_UNAVAILABLE = 503;
    }

    /**
     * 响应消息常量
     */
    public static class Message {
        public static final String SUCCESS = "操作成功";
        public static final String PARAM_ERROR = "参数错误";
        public static final String SYSTEM_ERROR = "系统异常";
        public static final String DATA_NOT_FOUND = "数据不存在";
        public static final String PERMISSION_DENIED = "权限不足";
        public static final String OPERATION_FAILED = "操作失败";
        public static final String QUERY_SUCCESS = "查询成功";
        public static final String SAVE_SUCCESS = "保存成功";
        public static final String UPDATE_SUCCESS = "更新成功";
        public static final String DELETE_SUCCESS = "删除成功";
    }
}
