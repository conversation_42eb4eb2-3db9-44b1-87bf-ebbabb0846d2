package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@ApiModel("电站交易概况DTO")
@Data
@NoArgsConstructor
public class SettlementSummaryDTO {

    @ApiModelProperty(value = "电站ID")
    private Long stationId;

    @ApiModelProperty(value = "电站名称")
    private String stationName;

    @ApiModelProperty(value = "电站类型")
    private Integer stationType;

    @ApiModelProperty(value = "累计结算电量")
    private BigDecimal totalSettlementElectricity;

    @ApiModelProperty(value = "累计交易均价")
    private BigDecimal avgTradePrice;

    @ApiModelProperty(value = "累计结算电费")
    private BigDecimal totalSettlementElectricFee;

    /**
     * 全参数构造函数
     */
    public SettlementSummaryDTO(Long stationId, String stationName, Integer stationType,
                               BigDecimal totalSettlementElectricity, BigDecimal avgTradePrice,
                               BigDecimal totalSettlementElectricFee) {
        this.stationId = stationId;
        this.stationName = stationName;
        this.stationType = stationType;
        this.totalSettlementElectricity = totalSettlementElectricity;
        this.avgTradePrice = avgTradePrice;
        this.totalSettlementElectricFee = totalSettlementElectricFee;
    }
}