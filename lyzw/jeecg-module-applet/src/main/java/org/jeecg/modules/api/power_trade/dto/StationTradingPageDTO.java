package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 电站交易信息分页响应DTO
 * 用于电站总览接口的分页返回
 */
@Data
@ApiModel(value = "电站交易信息分页响应DTO", description = "电站交易总览的分页数据传输对象")
public class StationTradingPageDTO {

    @ApiModelProperty(value = "电站交易数据列表", required = true)
    private List<StationTradingDTO> records;

    @ApiModelProperty(value = "总记录数", required = true, example = "150")
    private Long total;

    @ApiModelProperty(value = "每页条数", required = true, example = "10")
    private Long size;

    @ApiModelProperty(value = "当前页码", required = true, example = "1")
    private Long current;

    @ApiModelProperty(value = "总页数", required = true, example = "15")
    private Long pages;

    @ApiModelProperty(value = "查询维度 (1-月度, 2-年度)", required = true, example = "1")
    private Integer dimension;

    @ApiModelProperty(value = "查询年份", required = true, example = "2024")
    private String year;

    @ApiModelProperty(value = "查询月份 (月度查询时返回)", example = "07")
    private String month;

    @ApiModelProperty(value = "省份ID", required = true, example = "1")
    private Integer provinceId;

    @ApiModelProperty(value = "电站名称搜索关键词", example = "风电")
    private String searchName;
}
