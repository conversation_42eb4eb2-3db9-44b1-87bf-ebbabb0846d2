package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.power_trade.entity.TradeDiaryFile;
import org.jeecg.modules.api.power_trade.mapper.TradeDiaryFileMapper;
import org.jeecg.modules.api.power_trade.service.ITradeDiaryFileService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 交易日历附件服务实现
 */
@Service
public class TradeDiaryFileServiceImpl extends ServiceImpl<TradeDiaryFileMapper, TradeDiaryFile> implements ITradeDiaryFileService {

    @Override
    public List<TradeDiaryFile> getFilesByDiaryId(Long tradeDiaryId) {
        return baseMapper.selectFilesByDiaryId(tradeDiaryId);
    }

    @Override
    public Map<Long, List<TradeDiaryFile>> getFilesByDiaryIds(List<Long> tradeDiaryIds) {
        if (tradeDiaryIds == null || tradeDiaryIds.isEmpty()) {
            return new HashMap<>();
        }
        
        List<TradeDiaryFile> files = baseMapper.selectFilesByDiaryIds(tradeDiaryIds);
        
        // 按交易日历ID分组
        return files.stream().collect(Collectors.groupingBy(TradeDiaryFile::getTradeDiaryId));
    }
} 