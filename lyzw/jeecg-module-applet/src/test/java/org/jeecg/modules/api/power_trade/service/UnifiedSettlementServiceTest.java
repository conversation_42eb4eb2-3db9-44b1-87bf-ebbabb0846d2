package org.jeecg.modules.api.power_trade.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.dto.SettlementAggregationDTO;
import org.jeecg.modules.api.power_trade.dto.StationSettlementDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 统一结算数据服务测试
 * 验证首页和交易概况的结算电量数据一致性
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class UnifiedSettlementServiceTest {

    @Autowired
    private UnifiedSettlementService unifiedSettlementService;

    /**
     * 测试区域结算电量汇总
     */
    @Test
    public void testGetRegionSettlementSummary() {
        log.info("=== 测试区域结算电量汇总 ===");
        
        // 测试电站ID列表
        List<Long> stationIds = Arrays.asList(1L, 2L, 3L, 4L, 5L);
        
        try {
            // 获取区域结算汇总
            SettlementAggregationDTO result = unifiedSettlementService.getRegionSettlementSummary(
                stationIds, null, null);
            
            assertNotNull(result, "结算汇总结果不应为空");
            
            log.info("区域结算汇总结果:");
            log.info("  结算电量: {} MWh", result.getSettlementPower());
            log.info("  结算均价: {} 元/MWh", result.getSettlementAvgPrice());
            log.info("  总结算电费: {} 元", result.getTotalSettlementFee());
            log.info("  电站数量: {}", result.getTotalStations());
            log.info("  数据质量: {}", result.getDataQualityScore());
            
            // 验证数据合理性
            if (result.getSettlementPower() != null && result.getSettlementPower() > 0) {
                assertTrue(result.getSettlementPower() > 0, "结算电量应大于0");
                log.info("✓ 结算电量数据正常");
            }
            
            if (result.getSettlementAvgPrice() != null && result.getSettlementAvgPrice() > 0) {
                assertTrue(result.getSettlementAvgPrice() > 0, "结算均价应大于0");
                assertTrue(result.getSettlementAvgPrice() < 1000, "结算均价应在合理范围内");
                log.info("✓ 结算均价数据正常");
            }
            
        } catch (Exception e) {
            log.warn("区域结算汇总测试异常（可能需要数据库数据）: {}", e.getMessage());
        }
    }

    /**
     * 测试电站结算电量明细
     */
    @Test
    public void testGetStationSettlementDetails() {
        log.info("=== 测试电站结算电量明细 ===");
        
        // 测试电站ID列表
        List<Long> stationIds = Arrays.asList(1L, 2L, 3L);
        
        try {
            // 获取电站结算明细
            List<StationSettlementDTO> results = unifiedSettlementService.getStationSettlementDetails(
                stationIds, null, null);
            
            assertNotNull(results, "电站结算明细结果不应为空");
            
            log.info("电站结算明细结果数量: {}", results.size());
            
            for (StationSettlementDTO dto : results) {
                log.info("电站结算明细:");
                log.info("  电站ID: {}", dto.getStationId());
                log.info("  电站名称: {}", dto.getStationName());
                log.info("  电站类型: {} ({})", dto.getStationType(), dto.getStationTypeName());
                log.info("  结算电量: {} MWh", dto.getTotalSettlementPower());
                log.info("  结算均价: {} 元/MWh", dto.getAvgSettlementPrice());
                log.info("  结算电费: {} 元", dto.getTotalSettlementFee());
                log.info("  数据质量: {}", dto.getDataQualityScore());
                
                // 验证数据完整性
                if (dto.isDataComplete()) {
                    log.info("✓ 电站{}数据完整", dto.getStationId());
                } else {
                    log.warn("⚠ 电站{}数据不完整", dto.getStationId());
                }
                
                // 同步新旧字段
                dto.syncFields();
                log.info("✓ 电站{}字段同步完成", dto.getStationId());
            }
            
        } catch (Exception e) {
            log.warn("电站结算明细测试异常（可能需要数据库数据）: {}", e.getMessage());
        }
    }

    /**
     * 测试数据一致性
     */
    @Test
    public void testDataConsistency() {
        log.info("=== 测试数据一致性 ===");
        
        List<Long> stationIds = Arrays.asList(1L, 2L, 3L);
        
        try {
            // 获取区域汇总数据
            SettlementAggregationDTO regionSummary = unifiedSettlementService.getRegionSettlementSummary(
                stationIds, null, null);
            
            // 获取电站明细数据
            List<StationSettlementDTO> stationDetails = unifiedSettlementService.getStationSettlementDetails(
                stationIds, null, null);
            
            if (regionSummary.getSettlementPower() != null && !stationDetails.isEmpty()) {
                // 计算明细数据的总和
                double totalPowerFromDetails = stationDetails.stream()
                    .filter(dto -> dto.getTotalSettlementPower() != null)
                    .mapToDouble(dto -> dto.getTotalSettlementPower().doubleValue())
                    .sum();
                
                double totalFeeFromDetails = stationDetails.stream()
                    .filter(dto -> dto.getTotalSettlementFee() != null)
                    .mapToDouble(dto -> dto.getTotalSettlementFee().doubleValue())
                    .sum();
                
                log.info("数据一致性检查:");
                log.info("  区域汇总结算电量: {} MWh", regionSummary.getSettlementPower());
                log.info("  明细汇总结算电量: {} MWh", totalPowerFromDetails);
                log.info("  区域汇总结算电费: {} 元", regionSummary.getTotalSettlementFee());
                log.info("  明细汇总结算电费: {} 元", totalFeeFromDetails);
                
                // 验证数据一致性（允许小数点误差）
                double powerDifference = Math.abs(regionSummary.getSettlementPower() - totalPowerFromDetails);
                if (powerDifference < 0.01) {
                    log.info("✓ 结算电量数据一致");
                } else {
                    log.warn("⚠ 结算电量数据不一致，差异: {} MWh", powerDifference);
                }
                
                if (regionSummary.getTotalSettlementFee() != null) {
                    double feeDifference = Math.abs(regionSummary.getTotalSettlementFee() - totalFeeFromDetails);
                    if (feeDifference < 0.01) {
                        log.info("✓ 结算电费数据一致");
                    } else {
                        log.warn("⚠ 结算电费数据不一致，差异: {} 元", feeDifference);
                    }
                }
            }
            
        } catch (Exception e) {
            log.warn("数据一致性测试异常（可能需要数据库数据）: {}", e.getMessage());
        }
    }

    /**
     * 测试空数据处理
     */
    @Test
    public void testEmptyDataHandling() {
        log.info("=== 测试空数据处理 ===");
        
        // 测试空电站列表
        SettlementAggregationDTO emptyResult = unifiedSettlementService.getRegionSettlementSummary(
            Arrays.asList(), null, null);
        
        assertNotNull(emptyResult, "空数据结果不应为null");
        log.info("✓ 空电站列表处理正常");
        
        // 测试null电站列表
        SettlementAggregationDTO nullResult = unifiedSettlementService.getRegionSettlementSummary(
            null, null, null);
        
        assertNotNull(nullResult, "null数据结果不应为null");
        log.info("✓ null电站列表处理正常");
        
        // 测试不存在的电站ID
        List<Long> nonExistentIds = Arrays.asList(99999L, 99998L);
        SettlementAggregationDTO nonExistentResult = unifiedSettlementService.getRegionSettlementSummary(
            nonExistentIds, null, null);
        
        assertNotNull(nonExistentResult, "不存在电站ID的结果不应为null");
        log.info("✓ 不存在电站ID处理正常");
    }

    /**
     * 测试日期范围查询
     */
    @Test
    public void testDateRangeQuery() {
        log.info("=== 测试日期范围查询 ===");
        
        List<Long> stationIds = Arrays.asList(1L, 2L);
        
        try {
            // 测试指定日期范围
            SettlementAggregationDTO result1 = unifiedSettlementService.getRegionSettlementSummary(
                stationIds, "2024-01-01", "2024-12-31");
            
            assertNotNull(result1, "指定日期范围结果不应为空");
            log.info("✓ 指定日期范围查询正常");
            
            // 测试当年数据（默认）
            SettlementAggregationDTO result2 = unifiedSettlementService.getRegionSettlementSummary(
                stationIds, null, null);
            
            assertNotNull(result2, "当年数据查询结果不应为空");
            log.info("✓ 当年数据查询正常");
            
            // 比较结果
            if (result1.getSettlementPower() != null && result2.getSettlementPower() != null) {
                log.info("指定范围结算电量: {} MWh", result1.getSettlementPower());
                log.info("当年数据结算电量: {} MWh", result2.getSettlementPower());
            }
            
        } catch (Exception e) {
            log.warn("日期范围查询测试异常（可能需要数据库数据）: {}", e.getMessage());
        }
    }

    /**
     * 测试性能
     */
    @Test
    public void testPerformance() {
        log.info("=== 测试查询性能 ===");
        
        List<Long> stationIds = Arrays.asList(1L, 2L, 3L, 4L, 5L);
        int testCount = 10;
        
        // 测试区域汇总查询性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < testCount; i++) {
            try {
                unifiedSettlementService.getRegionSettlementSummary(stationIds, null, null);
            } catch (Exception e) {
                // 忽略数据库相关异常
            }
        }
        long regionSummaryTime = System.currentTimeMillis() - startTime;
        
        // 测试电站明细查询性能
        startTime = System.currentTimeMillis();
        for (int i = 0; i < testCount; i++) {
            try {
                unifiedSettlementService.getStationSettlementDetails(stationIds, null, null);
            } catch (Exception e) {
                // 忽略数据库相关异常
            }
        }
        long stationDetailsTime = System.currentTimeMillis() - startTime;
        
        log.info("性能测试结果（{}次查询）:", testCount);
        log.info("  区域汇总查询: {} ms, 平均: {} ms/次", regionSummaryTime, regionSummaryTime / testCount);
        log.info("  电站明细查询: {} ms, 平均: {} ms/次", stationDetailsTime, stationDetailsTime / testCount);
        
        // 验证性能在合理范围内
        assertTrue(regionSummaryTime / testCount < 1000, "区域汇总查询平均时间应小于1秒");
        assertTrue(stationDetailsTime / testCount < 2000, "电站明细查询平均时间应小于2秒");
        
        log.info("✓ 查询性能测试通过");
    }
}
