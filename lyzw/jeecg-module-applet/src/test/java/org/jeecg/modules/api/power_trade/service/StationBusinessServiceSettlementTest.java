package org.jeecg.modules.api.power_trade.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.service.impl.StationBusinessServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 电站业务服务结算数据测试
 * 验证buildMonthlyTradingData方法的结算电量和交易均价计算
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class StationBusinessServiceSettlementTest {

    @Autowired
    private StationBusinessServiceImpl stationBusinessService;

    @Autowired
    private StationService stationService;

    /**
     * 测试buildMonthlyTradingData方法的结算数据计算
     */
    @Test
    public void testBuildMonthlyTradingDataSettlement() {
        log.info("=== 测试电站月度结算数据计算 ===");
        
        try {
            // 获取一个测试电站
            Station testStation = getTestStation();
            if (testStation == null) {
                log.warn("没有找到测试电站，跳过测试");
                return;
            }
            
            // 测试参数
            String year = "2024";
            String month = "01";
            
            // 使用反射调用私有方法
            Method method = StationBusinessServiceImpl.class.getDeclaredMethod(
                "buildMonthlyTradingData", Station.class, String.class, String.class);
            method.setAccessible(true);
            
            // 执行方法
            Map<String, Object> result = (Map<String, Object>) method.invoke(
                stationBusinessService, testStation, year, month);
            
            assertNotNull(result, "结果不应为空");
            
            // 验证基本字段
            assertEquals(year, result.get("year"), "年份应该正确");
            assertEquals(month, result.get("month"), "月份应该正确");
            assertNotNull(result.get("current_month_power"), "当月发电量不应为空");
            
            // 验证结算数据字段
            assertNotNull(result.get("totalSettlementElectricity"), "累计结算电量不应为空");
            assertNotNull(result.get("averagePrice"), "交易均价不应为空");
            assertNotNull(result.get("totalSettlementElectricFee"), "结算电费不应为空");
            assertNotNull(result.get("settlementRecordCount"), "结算记录数不应为空");
            
            // 获取结算数据
            BigDecimal totalSettlementElectricity = (BigDecimal) result.get("totalSettlementElectricity");
            BigDecimal averagePrice = (BigDecimal) result.get("averagePrice");
            BigDecimal totalSettlementElectricFee = (BigDecimal) result.get("totalSettlementElectricFee");
            Integer settlementRecordCount = (Integer) result.get("settlementRecordCount");
            
            log.info("电站{}在{}年{}月的结算数据:", testStation.getId(), year, month);
            log.info("  累计结算电量: {} GWh", totalSettlementElectricity);
            log.info("  交易均价: {} 元/kWh", averagePrice);
            log.info("  结算电费: {} 万元", totalSettlementElectricFee);
            log.info("  结算记录数: {}", settlementRecordCount);
            
            // 验证数据合理性
            assertTrue(totalSettlementElectricity.compareTo(BigDecimal.ZERO) >= 0, 
                "累计结算电量应该大于等于0");
            assertTrue(averagePrice.compareTo(BigDecimal.ZERO) >= 0, 
                "交易均价应该大于等于0");
            assertTrue(totalSettlementElectricFee.compareTo(BigDecimal.ZERO) >= 0, 
                "结算电费应该大于等于0");
            assertTrue(settlementRecordCount >= 0, 
                "结算记录数应该大于等于0");
            
            // 验证计算逻辑：如果有结算电量和电费，均价应该正确
            if (totalSettlementElectricity.compareTo(BigDecimal.ZERO) > 0 && 
                totalSettlementElectricFee.compareTo(BigDecimal.ZERO) > 0) {
                
                BigDecimal expectedAveragePrice = totalSettlementElectricFee.divide(
                    totalSettlementElectricity, 6, BigDecimal.ROUND_HALF_UP);
                
                assertEquals(0, expectedAveragePrice.compareTo(averagePrice), 
                    "交易均价计算应该正确");
                
                log.info("✓ 交易均价计算验证通过");
            }
            
            log.info("✓ 电站月度结算数据计算测试通过");
            
        } catch (Exception e) {
            log.error("测试过程中出现异常: {}", e.getMessage(), e);
            fail("测试不应该抛出异常");
        }
    }

    /**
     * 测试多个月份的结算数据
     */
    @Test
    public void testMultipleMonthsSettlement() {
        log.info("=== 测试多个月份的结算数据 ===");
        
        try {
            Station testStation = getTestStation();
            if (testStation == null) {
                log.warn("没有找到测试电站，跳过测试");
                return;
            }
            
            String year = "2024";
            String[] months = {"01", "02", "03", "04", "05", "06"};
            
            Method method = StationBusinessServiceImpl.class.getDeclaredMethod(
                "buildMonthlyTradingData", Station.class, String.class, String.class);
            method.setAccessible(true);
            
            for (String month : months) {
                Map<String, Object> result = (Map<String, Object>) method.invoke(
                    stationBusinessService, testStation, year, month);
                
                assertNotNull(result, "月份" + month + "的结果不应为空");
                
                BigDecimal totalSettlementElectricity = (BigDecimal) result.get("totalSettlementElectricity");
                BigDecimal averagePrice = (BigDecimal) result.get("averagePrice");
                Integer recordCount = (Integer) result.get("settlementRecordCount");
                
                log.info("{}年{}月 - 结算电量: {} GWh, 均价: {} 元/kWh, 记录数: {}", 
                        year, month, totalSettlementElectricity, averagePrice, recordCount);
                
                // 验证数据一致性
                assertNotNull(totalSettlementElectricity, "结算电量不应为空");
                assertNotNull(averagePrice, "交易均价不应为空");
                assertNotNull(recordCount, "记录数不应为空");
            }
            
            log.info("✓ 多个月份结算数据测试通过");
            
        } catch (Exception e) {
            log.error("多月份测试过程中出现异常: {}", e.getMessage(), e);
            fail("测试不应该抛出异常");
        }
    }

    /**
     * 测试边界情况
     */
    @Test
    public void testBoundaryConditions() {
        log.info("=== 测试边界情况 ===");
        
        try {
            Station testStation = getTestStation();
            if (testStation == null) {
                log.warn("没有找到测试电站，跳过测试");
                return;
            }
            
            Method method = StationBusinessServiceImpl.class.getDeclaredMethod(
                "buildMonthlyTradingData", Station.class, String.class, String.class);
            method.setAccessible(true);
            
            // 测试未来月份（应该没有数据）
            String futureYear = "2025";
            String futureMonth = "12";
            
            Map<String, Object> futureResult = (Map<String, Object>) method.invoke(
                stationBusinessService, testStation, futureYear, futureMonth);
            
            assertNotNull(futureResult, "未来月份结果不应为空");
            
            BigDecimal futureElectricity = (BigDecimal) futureResult.get("totalSettlementElectricity");
            BigDecimal futurePrice = (BigDecimal) futureResult.get("averagePrice");
            Integer futureRecordCount = (Integer) futureResult.get("settlementRecordCount");
            
            // 未来月份应该没有数据
            assertEquals(BigDecimal.ZERO, futureElectricity, "未来月份结算电量应为0");
            assertEquals(BigDecimal.ZERO, futurePrice, "未来月份交易均价应为0");
            assertEquals(0, futureRecordCount.intValue(), "未来月份记录数应为0");
            
            log.info("✓ 未来月份边界测试通过");
            
            // 测试历史月份（可能有数据）
            String historyYear = "2023";
            String historyMonth = "01";
            
            Map<String, Object> historyResult = (Map<String, Object>) method.invoke(
                stationBusinessService, testStation, historyYear, historyMonth);
            
            assertNotNull(historyResult, "历史月份结果不应为空");
            
            log.info("✓ 历史月份边界测试通过");
            
        } catch (Exception e) {
            log.error("边界测试过程中出现异常: {}", e.getMessage(), e);
            fail("测试不应该抛出异常");
        }
    }

    /**
     * 测试性能
     */
    @Test
    public void testPerformance() {
        log.info("=== 测试查询性能 ===");
        
        try {
            Station testStation = getTestStation();
            if (testStation == null) {
                log.warn("没有找到测试电站，跳过测试");
                return;
            }
            
            Method method = StationBusinessServiceImpl.class.getDeclaredMethod(
                "buildMonthlyTradingData", Station.class, String.class, String.class);
            method.setAccessible(true);
            
            String year = "2024";
            String month = "01";
            int testCount = 5;
            
            long totalTime = 0;
            
            for (int i = 0; i < testCount; i++) {
                long startTime = System.currentTimeMillis();
                
                Map<String, Object> result = (Map<String, Object>) method.invoke(
                    stationBusinessService, testStation, year, month);
                
                long duration = System.currentTimeMillis() - startTime;
                totalTime += duration;
                
                assertNotNull(result, "结果不应为空");
                
                log.debug("第{}次查询耗时: {}ms", i + 1, duration);
            }
            
            double avgTime = (double) totalTime / testCount;
            log.info("性能测试结果 - 平均查询时间: {:.2f}ms", avgTime);
            
            // 验证性能在合理范围内（应该小于5秒）
            assertTrue(avgTime < 5000, "平均查询时间应该小于5秒");
            
            log.info("✓ 性能测试通过");
            
        } catch (Exception e) {
            log.error("性能测试过程中出现异常: {}", e.getMessage(), e);
            fail("测试不应该抛出异常");
        }
    }

    /**
     * 获取测试电站
     */
    private Station getTestStation() {
        try {
            // 获取第一个可用的电站作为测试数据
            return stationService.list().stream()
                .filter(station -> station.getId() != null)
                .findFirst()
                .orElse(null);
        } catch (Exception e) {
            log.warn("获取测试电站失败: {}", e.getMessage());
            return null;
        }
    }
}
