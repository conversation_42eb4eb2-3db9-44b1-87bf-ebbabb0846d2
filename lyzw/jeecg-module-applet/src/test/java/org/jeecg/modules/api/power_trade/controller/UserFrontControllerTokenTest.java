package org.jeecg.modules.api.power_trade.controller;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.LocalCacheUtil;
import org.jeecg.modules.api.power_trade.entity.UserFront;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.lang.reflect.Field;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UserFrontController Token有效期测试
 * 验证Token有效期修改为12小时的效果
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class UserFrontControllerTokenTest {

    @Autowired
    private UserFrontController userFrontController;

    @Autowired
    private LocalCacheUtil localCacheUtil;

    /**
     * 测试Token有效期常量定义
     */
    @Test
    public void testTokenExpireConstant() throws Exception {
        // 使用反射获取私有常量
        Field field = UserFrontController.class.getDeclaredField("FRONT_TOKEN_EXPIRE_SECONDS");
        field.setAccessible(true);
        long expireSeconds = (Long) field.get(null);
        
        // 验证常量值为12小时 = 43200秒
        long expectedSeconds = 12 * 60 * 60;
        assertEquals(expectedSeconds, expireSeconds, "Token有效期应该为12小时（43200秒）");
        
        log.info("Token有效期常量验证通过: {} 秒 = {} 小时", expireSeconds, expireSeconds / 3600.0);
    }

    /**
     * 测试登录后Token缓存有效期设置
     * 注意：此测试需要有效的用户数据，实际运行时可能需要mock
     */
    @Test
    public void testLoginTokenExpire() {
        // 创建测试用户数据
        UserFront testUser = new UserFront();
        testUser.setPersonNumber("test001");
        testUser.setPassword("test123");
        testUser.setWechatStatus(1); // 设置为允许访问
        
        try {
            // 执行登录（注意：这里可能需要mock userFrontService）
            Result<?> result = userFrontController.login(testUser);
            
            if (result.isSuccess()) {
                // 获取返回的token
                JSONObject data = (JSONObject) result.getResult();
                String token = data.getString("token");
                
                assertNotNull(token, "登录应该返回token");
                
                // 验证token在缓存中存在
                String cacheKey = CommonConstant.PREFIX_USER_TOKEN + token;
                Object cachedToken = localCacheUtil.get(cacheKey);
                assertNotNull(cachedToken, "Token应该被缓存");
                
                // 验证缓存的TTL（剩余生存时间）
                long ttl = localCacheUtil.getExpire(cacheKey);
                log.info("Token缓存TTL: {} 秒", ttl);
                
                // TTL应该接近12小时（考虑执行时间误差，允许一定范围）
                long expectedTtl = 12 * 60 * 60; // 12小时
                assertTrue(ttl > expectedTtl - 60, "TTL应该大于12小时减去1分钟");
                assertTrue(ttl <= expectedTtl, "TTL应该不超过12小时");
                
                log.info("Token有效期验证通过: TTL = {} 秒 ≈ {} 小时", ttl, ttl / 3600.0);
                
            } else {
                log.warn("登录失败，跳过Token有效期验证: {}", result.getMessage());
            }
            
        } catch (Exception e) {
            log.warn("登录测试异常（可能需要mock数据）: {}", e.getMessage());
            // 在没有完整测试环境时，这是正常的
        }
    }

    /**
     * 测试Token过期后的行为
     */
    @Test
    public void testTokenExpireBehavior() {
        // 手动创建一个短期token用于测试
        String testToken = "test_token_" + System.currentTimeMillis();
        String cacheKey = CommonConstant.PREFIX_USER_TOKEN + testToken;
        
        // 设置一个很短的过期时间（2秒）用于测试
        localCacheUtil.set(cacheKey, testToken);
        localCacheUtil.expire(cacheKey, 2);
        
        // 验证token存在
        assertNotNull(localCacheUtil.get(cacheKey), "Token应该存在");
        
        try {
            // 等待token过期
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 验证token已过期
        assertNull(localCacheUtil.get(cacheKey), "Token应该已过期");
        
        log.info("Token过期行为验证通过");
    }

    /**
     * 测试退出登录时Token清理
     */
    @Test
    public void testLogoutTokenCleanup() {
        // 手动创建一个token用于测试
        String testToken = "logout_test_token_" + System.currentTimeMillis();
        String cacheKey = CommonConstant.PREFIX_USER_TOKEN + testToken;
        
        // 设置token
        localCacheUtil.set(cacheKey, testToken);
        localCacheUtil.expire(cacheKey, 3600); // 1小时
        
        // 验证token存在
        assertNotNull(localCacheUtil.get(cacheKey), "Token应该存在");
        
        // 手动清理token（模拟logout行为）
        localCacheUtil.del(cacheKey);
        
        // 验证token已被清理
        assertNull(localCacheUtil.get(cacheKey), "Token应该已被清理");
        
        log.info("退出登录Token清理验证通过");
    }

    /**
     * 性能测试：验证Token操作的性能
     */
    @Test
    public void testTokenPerformance() {
        int testCount = 1000;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < testCount; i++) {
            String testToken = "perf_test_token_" + i;
            String cacheKey = CommonConstant.PREFIX_USER_TOKEN + testToken;
            
            // 设置token
            localCacheUtil.set(cacheKey, testToken);
            localCacheUtil.expire(cacheKey, 12 * 60 * 60); // 12小时
            
            // 读取token
            Object cachedToken = localCacheUtil.get(cacheKey);
            assertNotNull(cachedToken);
            
            // 清理token
            localCacheUtil.del(cacheKey);
        }
        
        long duration = System.currentTimeMillis() - startTime;
        double avgTime = (double) duration / testCount;
        
        log.info("Token操作性能测试完成: {} 次操作耗时 {} ms, 平均每次 {:.2f} ms", 
                testCount, duration, avgTime);
        
        // 验证性能在合理范围内（每次操作应该小于10ms）
        assertTrue(avgTime < 10.0, "Token操作平均时间应该小于10ms");
    }

    /**
     * 并发测试：验证Token操作的线程安全性
     */
    @Test
    public void testTokenConcurrency() throws InterruptedException {
        int threadCount = 10;
        int operationsPerThread = 100;
        
        Thread[] threads = new Thread[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    String testToken = "concurrent_test_token_" + threadId + "_" + j;
                    String cacheKey = CommonConstant.PREFIX_USER_TOKEN + testToken;
                    
                    try {
                        // 设置token
                        localCacheUtil.set(cacheKey, testToken);
                        localCacheUtil.expire(cacheKey, 12 * 60 * 60);
                        
                        // 读取token
                        Object cachedToken = localCacheUtil.get(cacheKey);
                        assertNotNull(cachedToken);
                        
                        // 短暂等待
                        Thread.sleep(1);
                        
                        // 清理token
                        localCacheUtil.del(cacheKey);
                        
                    } catch (Exception e) {
                        log.error("并发测试异常: {}", e.getMessage(), e);
                        fail("并发操作不应该出现异常");
                    }
                }
            });
        }
        
        // 启动所有线程
        long startTime = System.currentTimeMillis();
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        long duration = System.currentTimeMillis() - startTime;
        int totalOperations = threadCount * operationsPerThread;
        
        log.info("Token并发测试完成: {} 个线程, 每线程 {} 次操作, 总耗时 {} ms", 
                threadCount, operationsPerThread, duration);
        
        // 验证所有操作都在合理时间内完成
        assertTrue(duration < 30000, "并发操作应该在30秒内完成");
    }

    /**
     * 边界测试：测试极端情况
     */
    @Test
    public void testTokenBoundaryConditions() {
        // 测试空token
        try {
            String cacheKey = CommonConstant.PREFIX_USER_TOKEN + "";
            localCacheUtil.set(cacheKey, "");
            localCacheUtil.expire(cacheKey, 12 * 60 * 60);
            log.info("空token测试通过");
        } catch (Exception e) {
            log.warn("空token测试异常: {}", e.getMessage());
        }
        
        // 测试很长的token
        try {
            String longToken = "a".repeat(1000);
            String cacheKey = CommonConstant.PREFIX_USER_TOKEN + longToken;
            localCacheUtil.set(cacheKey, longToken);
            localCacheUtil.expire(cacheKey, 12 * 60 * 60);
            
            Object cachedToken = localCacheUtil.get(cacheKey);
            assertEquals(longToken, cachedToken, "长token应该能正确缓存和读取");
            
            localCacheUtil.del(cacheKey);
            log.info("长token测试通过");
        } catch (Exception e) {
            log.warn("长token测试异常: {}", e.getMessage());
        }
        
        // 测试特殊字符token
        try {
            String specialToken = "token_with_特殊字符_!@#$%^&*()";
            String cacheKey = CommonConstant.PREFIX_USER_TOKEN + specialToken;
            localCacheUtil.set(cacheKey, specialToken);
            localCacheUtil.expire(cacheKey, 12 * 60 * 60);
            
            Object cachedToken = localCacheUtil.get(cacheKey);
            assertEquals(specialToken, cachedToken, "特殊字符token应该能正确处理");
            
            localCacheUtil.del(cacheKey);
            log.info("特殊字符token测试通过");
        } catch (Exception e) {
            log.warn("特殊字符token测试异常: {}", e.getMessage());
        }
    }
}
