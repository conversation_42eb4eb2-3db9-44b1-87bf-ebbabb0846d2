-- 新增在线表单详情sql
INSERT INTO `sys_permission`(`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1746709108126285826', '1455100420297859074', 'AUTO在线表单详情', '/online/cgformDetail/:id', 'super/online/cgform/auto/default/OnlineAutoDetail', 1, '', NULL, 1, NULL, '0', 1.00, 0, NULL, 1, 0, 1, 0, NULL, 'jeecg', '2024-01-15 09:41:18', NULL, NULL, 0, 0, NULL, 0);

-- 修改仪表盘文本组件的配置项
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '文本', `comp_type` = 'JText', `icon` = 'ant-design:font-colors-outlined', `order_num` = 14, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 8,\r\n  \"h\": 12,\r\n  \"dataType\": 1,\r\n  \"url\": \"http://api.jeecg.com/mock/42/nav\",\r\n  \"timeOut\": 0,\r\n	\"background\": \"#4A90E2\",\r\n  \"turnConfig\": {\r\n    \"url\": \"\"\r\n  },\r\n  \"chartData\": \"JeecgBoot面板设计\",\r\n  \"option\": {\r\n    \"card\": {\r\n      \"title\": \"\",\r\n      \"extra\": \"\",\r\n      \"rightHref\": \"\",\r\n      \"size\": \"default\"\r\n    },\r\n    \"body\": {\r\n      \"text\": \"\",\r\n      \"color\": \"#FFFFFF\",\r\n      \"fontWeight\": \"bold\",\r\n      \"marginLeft\": 0,\r\n      \"marginTop\": 0\r\n    }\r\n  }\r\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 10:49:04' WHERE `id` = '100110';

-- 添加“设置默认首页”的权限
INSERT INTO sys_permission (id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('1750128461040648193', '1170592628746878978', '设置默认首页', NULL, NULL, '0', NULL, NULL, '2', 'system:permission:setDefIndex', '1', NULL, '0', NULL, '1', '0', '0', '0', NULL, 'admin', STR_TO_DATE('2024-01-24 20:08:35', '%Y-%m-%d %H:%i:%s'), NULL, NULL, '0', NULL, '1', '0');

-- [jimureport]主子表循环块示例
INSERT INTO `jimu_report` (`id`, `code`, `name`, `note`, `status`, `type`, `json_str`, `api_url`, `thumb`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `api_method`, `api_code`, `template`, `view_count`, `css_str`, `js_str`, `tenant_id`) VALUES ('907480464532770816', '20240117141013', '主子报表循环块', NULL, NULL, 'datainfo', '{\"loopBlockList\":[{\"sci\":0,\"sri\":0,\"eci\":8,\"eri\":36,\"index\":1,\"db\":\"aa\"}],\"area\":{\"sri\":9,\"sci\":11,\"eri\":9,\"eci\":11,\"width\":100,\"height\":25},\"excel_config_id\":\"907480464532770816\",\"printConfig\":{\"layout\":\"portrait\",\"paginationShow\":false,\"printCallBackUrl\":\"\",\"paper\":\"A4\",\"isBackend\":false,\"width\":210,\"paginationLocation\":\"middle\",\"definition\":1,\"marginX\":10,\"height\":297,\"marginY\":10},\"hiddenCells\":[],\"zonedEditionList\":[],\"rows\":{\"0\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"merge\":[0,6],\"style\":8,\"text\":\"订货商信息\",\"height\":0},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}},\"height\":57},\"1\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"style\":10,\"text\":\"订单编号：\"},\"2\":{\"loopBlock\":1,\"merge\":[0,2],\"style\":42,\"text\":\"#{aa.order_code}\",\"height\":0},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}},\"height\":34},\"2\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"style\":10,\"text\":\"订单地址：\"},\"2\":{\"loopBlock\":1,\"merge\":[0,1],\"style\":42,\"text\":\"#{aa.descc}\",\"height\":0},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"style\":10,\"text\":\"订单日期：\"},\"5\":{\"loopBlock\":1,\"merge\":[0,1],\"style\":42,\"text\":\"#{aa.order_date}\",\"height\":0},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}},\"height\":34},\"3\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"style\":10,\"text\":\"订单姓名：\"},\"2\":{\"loopBlock\":1,\"merge\":[0,1],\"style\":42,\"text\":\"#{aa.create_by}\",\"height\":0},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"style\":10,\"text\":\"到货日期：\"},\"5\":{\"loopBlock\":1,\"merge\":[0,1],\"style\":42,\"text\":\"#{aa.create_time}\",\"height\":0},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}},\"height\":31},\"4\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"5\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"decimalPlaces\":\"4\",\"merge\":[0,6],\"style\":31,\"text\":\"订单详情\"},\"2\":{\"loopBlock\":1},\"3\":{\"loopBlock\":1},\"4\":{\"loopBlock\":1},\"5\":{\"loopBlock\":1},\"6\":{\"loopBlock\":1},\"7\":{\"loopBlock\":1},\"8\":{\"loopBlock\":1,\"text\":\"\"}},\"height\":51},\"6\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"style\":15,\"text\":\"商品编码\"},\"2\":{\"loopBlock\":1,\"style\":15,\"text\":\"商品名称\"},\"3\":{\"loopBlock\":1,\"style\":15,\"text\":\"销售时间\"},\"4\":{\"loopBlock\":1,\"style\":15,\"text\":\"销售数据量\"},\"5\":{\"loopBlock\":1,\"style\":15,\"text\":\"定价\"},\"6\":{\"loopBlock\":1,\"style\":15,\"text\":\"优惠价\"},\"7\":{\"loopBlock\":1,\"style\":15,\"text\":\"付款金额\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}},\"height\":42},\"7\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"style\":18,\"text\":\"#{bb.product_name}\"},\"2\":{\"loopBlock\":1,\"style\":18,\"text\":\"#{bb.product_name}\"},\"3\":{\"loopBlock\":1,\"style\":18,\"text\":\"#{bb.product_name}\"},\"4\":{\"loopBlock\":1,\"style\":18,\"text\":\"#{bb.num}\"},\"5\":{\"loopBlock\":1,\"decimalPlaces\":\"4\",\"style\":19,\"text\":\"#{bb.price}\"},\"6\":{\"loopBlock\":1,\"decimalPlaces\":\"1\",\"style\":19,\"text\":\"#{bb.price}\"},\"7\":{\"loopBlock\":1,\"style\":18,\"text\":\"#{bb.pro_type}\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"8\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"9\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"10\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"style\":39,\"text\":\"备注：\"},\"2\":{\"loopBlock\":1,\"style\":33,\"text\":\" \"},\"3\":{\"loopBlock\":1,\"style\":33,\"text\":\" \"},\"4\":{\"loopBlock\":1,\"style\":33,\"text\":\" \"},\"5\":{\"loopBlock\":1,\"style\":33,\"text\":\" \"},\"6\":{\"loopBlock\":1,\"style\":33,\"text\":\" \"},\"7\":{\"loopBlock\":1,\"style\":34,\"text\":\" \"},\"8\":{\"loopBlock\":1,\"text\":\"\"}},\"height\":25},\"11\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"merge\":[0,6],\"style\":41,\"text\":\"1、查看信息，在浏览器输入“?did=1”或“?did=2”\",\"height\":0},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}},\"height\":37},\"12\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"style\":35,\"text\":\" \"},\"2\":{\"loopBlock\":1,\"text\":\" \"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"style\":36,\"text\":\" \"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"13\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"style\":37,\"text\":\" \"},\"2\":{\"loopBlock\":1,\"style\":28,\"text\":\" \"},\"3\":{\"loopBlock\":1,\"style\":28,\"text\":\" \"},\"4\":{\"loopBlock\":1,\"style\":28,\"text\":\" \"},\"5\":{\"loopBlock\":1,\"style\":28,\"text\":\" \"},\"6\":{\"loopBlock\":1,\"style\":28,\"text\":\" \"},\"7\":{\"loopBlock\":1,\"style\":38,\"text\":\" \"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"14\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"15\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1},\"2\":{\"loopBlock\":1},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"16\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1},\"2\":{\"loopBlock\":1},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"17\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"18\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"19\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"20\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"21\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1},\"2\":{\"loopBlock\":1},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"22\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"23\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"24\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"25\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"26\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1},\"2\":{\"loopBlock\":1},\"3\":{\"loopBlock\":1},\"4\":{\"loopBlock\":1},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"27\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"28\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"29\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"30\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"31\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"32\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"33\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"34\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"35\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"36\":{\"cells\":{\"0\":{\"loopBlock\":1,\"text\":\"\"},\"1\":{\"loopBlock\":1,\"text\":\"\"},\"2\":{\"loopBlock\":1,\"text\":\"\"},\"3\":{\"loopBlock\":1,\"text\":\"\"},\"4\":{\"loopBlock\":1,\"text\":\"\"},\"5\":{\"loopBlock\":1,\"text\":\"\"},\"6\":{\"loopBlock\":1,\"text\":\"\"},\"7\":{\"loopBlock\":1,\"text\":\"\"},\"8\":{\"loopBlock\":1,\"text\":\"\"}}},\"len\":102},\"dbexps\":[],\"toolPrintSizeObj\":{\"printType\":\"A4\",\"widthPx\":718,\"heightPx\":1047},\"dicts\":[],\"rpbar\":{\"show\":true,\"pageSize\":\"\",\"btnList\":[]},\"fixedPrintHeadRows\":[],\"fixedPrintTailRows\":[],\"freeze\":\"A1\",\"dataRectWidth\":682,\"displayConfig\":{},\"background\":false,\"name\":\"sheet1\",\"autofilter\":{},\"styles\":[{\"border\":{\"top\":[\"thin\",\"#000\"],\"left\":[\"thin\",\"#000\"],\"bottom\":[\"thin\",\"#000\"],\"right\":[\"thin\",\"#000\"]}},{\"border\":{\"top\":[\"thin\",\"#000\"],\"left\":[\"thin\",\"#000\"],\"bottom\":[\"thin\",\"#000\"],\"right\":[\"thin\",\"#000\"]},\"align\":\"center\"},{\"border\":{\"top\":[\"thin\",\"#000\"],\"left\":[\"thin\",\"#000\"],\"bottom\":[\"thin\",\"#000\"],\"right\":[\"thin\",\"#000\"]},\"bgcolor\":\"#5b9cd6\",\"align\":\"center\"},{\"font\":{\"size\":18}},{\"font\":{\"size\":18,\"bold\":true}},{\"align\":\"center\"},{\"align\":\"center\",\"font\":{\"size\":18,\"bold\":true}},{\"bgcolor\":\"#5b9cd6\",\"align\":\"center\"},{\"align\":\"center\",\"font\":{\"size\":18,\"name\":\"宋体\",\"bold\":true}},{\"bgcolor\":\"#5b9cd6\",\"align\":\"center\",\"font\":{\"name\":\"宋体\"}},{\"font\":{\"name\":\"宋体\"}},{\"bgcolor\":\"#5b9cd6\",\"color\":\"#ffffff\",\"align\":\"center\",\"font\":{\"name\":\"宋体\"}},{\"border\":{\"top\":[\"thin\",\"#5b9cd6\"],\"left\":[\"thin\",\"#5b9cd6\"],\"bottom\":[\"thin\",\"#5b9cd6\"],\"right\":[\"thin\",\"#5b9cd6\"]},\"bgcolor\":\"#5b9cd6\",\"color\":\"#ffffff\",\"align\":\"center\",\"font\":{\"name\":\"宋体\"}},{\"border\":{\"top\":[\"thin\",\"#5b9cd6\"],\"left\":[\"thin\",\"#5b9cd6\"],\"bottom\":[\"thin\",\"#5b9cd6\"],\"right\":[\"thin\",\"#5b9cd6\"]},\"font\":{\"name\":\"宋体\"}},{\"border\":{\"top\":[\"thin\",\"#bfbfbf\"],\"left\":[\"thin\",\"#bfbfbf\"],\"bottom\":[\"thin\",\"#bfbfbf\"],\"right\":[\"thin\",\"#bfbfbf\"]}},{\"border\":{\"top\":[\"thin\",\"#bfbfbf\"],\"left\":[\"thin\",\"#bfbfbf\"],\"bottom\":[\"thin\",\"#bfbfbf\"],\"right\":[\"thin\",\"#bfbfbf\"]},\"bgcolor\":\"#5b9cd6\",\"color\":\"#ffffff\",\"align\":\"center\",\"font\":{\"name\":\"宋体\"}},{\"border\":{\"top\":[\"thin\",\"#bfbfbf\"],\"left\":[\"thin\",\"#bfbfbf\"],\"bottom\":[\"thin\",\"#bfbfbf\"],\"right\":[\"thin\",\"#bfbfbf\"]},\"font\":{\"name\":\"宋体\"}},{},{\"border\":{\"top\":[\"thin\",\"#bfbfbf\"],\"left\":[\"thin\",\"#bfbfbf\"],\"bottom\":[\"thin\",\"#bfbfbf\"],\"right\":[\"thin\",\"#bfbfbf\"]},\"align\":\"center\",\"font\":{\"name\":\"宋体\"}},{\"border\":{\"top\":[\"thin\",\"#bfbfbf\"],\"left\":[\"thin\",\"#bfbfbf\"],\"bottom\":[\"thin\",\"#bfbfbf\"],\"right\":[\"thin\",\"#bfbfbf\"]},\"format\":\"number\",\"align\":\"center\",\"font\":{\"name\":\"宋体\"}},{\"border\":{\"top\":[\"thin\",\"#bfbfbf\"],\"left\":[\"thin\",\"#bfbfbf\"],\"bottom\":[\"thin\",\"#bfbfbf\"],\"right\":[\"thin\",\"#bfbfbf\"]},\"format\":\"normal\",\"align\":\"center\",\"font\":{\"name\":\"宋体\"}},{\"font\":{\"size\":18,\"bold\":false}},{\"font\":{\"size\":18,\"name\":\"宋体\",\"bold\":false}},{\"align\":\"center\",\"font\":{\"size\":18,\"name\":\"宋体\",\"bold\":false}},{\"font\":{\"size\":18,\"name\":\"宋体\",\"bold\":true}},{\"border\":{\"bottom\":[\"thin\",\"#000\"]}},{\"border\":{\"bottom\":[\"thin\",\"#a5a5a5\"]}},{\"border\":{\"bottom\":[\"thin\",\"#262626\"]}},{\"border\":{\"bottom\":[\"thin\",\"#595959\"]}},{\"valign\":\"bottom\",\"align\":\"center\",\"font\":{\"size\":18,\"name\":\"宋体\",\"bold\":true}},{\"valign\":\"bottom\",\"align\":\"left\",\"font\":{\"size\":18,\"name\":\"宋体\",\"bold\":true}},{\"valign\":\"middle\",\"align\":\"center\",\"font\":{\"size\":18,\"name\":\"宋体\",\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#595959\"],\"left\":[\"thin\",\"#595959\"]}},{\"border\":{\"top\":[\"thin\",\"#595959\"]}},{\"border\":{\"top\":[\"thin\",\"#595959\"],\"right\":[\"thin\",\"#595959\"]}},{\"border\":{\"left\":[\"thin\",\"#595959\"]}},{\"border\":{\"right\":[\"thin\",\"#595959\"]}},{\"border\":{\"left\":[\"thin\",\"#595959\"],\"bottom\":[\"thin\",\"#595959\"]}},{\"border\":{\"bottom\":[\"thin\",\"#595959\"],\"right\":[\"thin\",\"#595959\"]}},{\"border\":{\"top\":[\"thin\",\"#595959\"],\"left\":[\"thin\",\"#595959\"]},\"font\":{\"name\":\"宋体\"}},{\"border\":{\"left\":[\"thin\",\"#595959\"],\"right\":[\"thin\",\"#595959\"]}},{\"border\":{\"left\":[\"thin\",\"#595959\"],\"right\":[\"thin\",\"#595959\"]},\"font\":{\"name\":\"宋体\"}},{\"border\":{\"bottom\":[\"thin\",\"#595959\"]},\"font\":{\"name\":\"宋体\"}}],\"validations\":[],\"cols\":{\"0\":{\"width\":39},\"1\":{\"width\":73},\"2\":{\"width\":89},\"3\":{\"width\":101},\"4\":{\"width\":80},\"8\":{\"width\":29},\"len\":100},\"merges\":[\"B1:H1\",\"C2:E2\",\"C3:D3\",\"F3:G3\",\"C4:D4\",\"F4:G4\",\"B6:H6\",\"B12:H12\"]}', NULL, 'https://static.jeecg.com/designreport/images/未标题-1_1617266678584.png', 'admin', '2024-01-17 14:10:13', 'admin', '2024-01-24 20:02:03', 0, NULL, NULL, 1, 76, NULL, NULL, NULL);
INSERT INTO `jimu_report_link` (`id`, `report_id`, `parameter`, `eject_type`, `link_name`, `api_method`, `link_type`, `api_url`, `link_chart_id`, `requirement`) VALUES ('907480951604711424', '907480464532770816', '{\"main\":\"aa\",\"sub\":\"bb\",\"subReport\":[{\"mainField\":\"id\",\"subParam\":\"orderId\",\"tableIndex\":1}]}', NULL, '555', NULL, '4', NULL, NULL, NULL);
INSERT INTO `jimu_report_db` (`id`, `jimu_report_id`, `create_by`, `update_by`, `create_time`, `update_time`, `db_code`, `db_ch_name`, `db_type`, `db_table_name`, `db_dyn_sql`, `db_key`, `tb_db_key`, `tb_db_table_name`, `java_type`, `java_value`, `api_url`, `api_method`, `is_list`, `is_page`, `db_source`, `db_source_type`, `json_data`, `api_convert`) VALUES ('907482846256762880', '907480464532770816', 'admin', NULL, '2024-01-17 14:19:41', '2024-01-17 14:19:41', 'aa', 'aa', '0', NULL, 'select * from rep_demo_order_main', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', '1', '8f90daf47d15d35ca6cf420748b8b9ba', 'mysql', '', NULL);
INSERT INTO `jimu_report_db` (`id`, `jimu_report_id`, `create_by`, `update_by`, `create_time`, `update_time`, `db_code`, `db_ch_name`, `db_type`, `db_table_name`, `db_dyn_sql`, `db_key`, `tb_db_key`, `tb_db_table_name`, `java_type`, `java_value`, `api_url`, `api_method`, `is_list`, `is_page`, `db_source`, `db_source_type`, `json_data`, `api_convert`) VALUES ('907483416753410048', '907480464532770816', 'admin', NULL, '2024-01-17 14:21:57', '2024-01-17 14:21:57', 'bb', 'bb', '0', NULL, 'select * from rep_demo_order_product where order_fk_id = \'${orderId}\'', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', '0', '8f90daf47d15d35ca6cf420748b8b9ba', 'mysql', '', '');


-- ----------------------------
-- Table structure for rep_demo_order_main
-- ----------------------------
DROP TABLE IF EXISTS `rep_demo_order_main`;
CREATE TABLE `rep_demo_order_main` (
                                       `id` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
                                       `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建日期',
                                       `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新人',
                                       `update_time` datetime DEFAULT NULL COMMENT '更新日期',
                                       `order_code` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '订单编码',
                                       `order_date` datetime DEFAULT NULL COMMENT '下单时间',
                                       `descc` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
                                       `xiala` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '下拉多选',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Records of rep_demo_order_main
-- ----------------------------

INSERT INTO `rep_demo_order_main` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `order_code`, `order_date`, `descc`, `xiala`) VALUES ('1256629667445714946', 'admin', '2020-05-03 01:00:34', 'admin', '2020-11-26 15:22:35', 'CN20221', '2020-05-03 00:00:00', '15', NULL);
INSERT INTO `rep_demo_order_main` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `order_code`, `order_date`, `descc`, `xiala`) VALUES ('1551943088862896130', 'admin', '2022-07-26 22:50:40', 'admin', '2022-10-29 17:16:26', 'CN20222', '2020-05-03 00:00:00', '', '');
INSERT INTO `rep_demo_order_main` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `order_code`, `order_date`, `descc`, `xiala`) VALUES ('1586557968995545089', 'admin', '2022-10-30 11:17:51', 'admin', '2022-11-07 15:07:24', 'CN20225', '2020-05-03 00:00:00', '111', '');
INSERT INTO `rep_demo_order_main` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `order_code`, `order_date`, `descc`, `xiala`) VALUES ('1589514956490637313', 'admin', '2022-11-07 15:07:52', 'admin', '2022-11-07 15:14:50', 'CN20223', '2022-11-07 00:00:00', NULL, '1,2');
INSERT INTO `rep_demo_order_main` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `order_code`, `order_date`, `descc`, `xiala`) VALUES ('1589516804530339842', 'admin', '2022-11-07 15:15:13', 'admin', '2022-11-23 17:20:56', 'CN20224', '2022-11-07 00:00:00', '222', '1,2');
INSERT INTO `rep_demo_order_main` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `order_code`, `order_date`, `descc`, `xiala`) VALUES ('1683074969561157634', 'admin', '2023-07-23 19:21:57', 'admin', '2023-12-06 15:26:44', 'CN20231', '2020-05-03 00:00:00', '111', '1,2');


-- ----------------------------
-- Table structure for rep_demo_order_product
-- ----------------------------
DROP TABLE IF EXISTS `rep_demo_order_product`;
CREATE TABLE `rep_demo_order_product` (
                                          `id` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
                                          `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建日期',
                                          `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新人',
                                          `update_time` datetime DEFAULT NULL COMMENT '更新日期',
                                          `product_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '产品名字',
                                          `price` double(32,0) DEFAULT NULL COMMENT '价格',
                                          `num` int DEFAULT NULL COMMENT '数量',
                                          `descc` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
                                          `order_fk_id` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '订单外键ID',
                                          `pro_type` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '产品类型',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Records of rep_demo_order_product
-- ----------------------------

INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1331860890813284353', 'admin', '2020-11-26 15:22:35', NULL, NULL, '水果手机', 44, 44, '', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1551943088930004994', 'admin', '2022-10-29 17:16:26', NULL, NULL, '水果手机1', 11, 11, '11', '1551943088862896130', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1551943088930004995', 'admin', '2022-07-26 22:50:40', NULL, NULL, '水果手机2', 2, 2, '2', '1551943088862896130', '2');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15665749948861', 'admin', '2020-02-24 02:05:38', NULL, NULL, '水果手机3', 33, NULL, '', '402831816a38e7fd016a38e825c90003', '');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1586557969318506498', 'admin', '2022-10-30 11:20:31', NULL, NULL, '小米手机3', 20003, 33, '3', '1586557968995545089', '2');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1586557969381421058', 'admin', '2022-10-30 11:17:51', NULL, NULL, '华为手机1', 80001, 31, '11', '1586557968995545089', '2');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883233', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883234', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机2', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883235', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机3', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883236', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机4', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883237', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机5', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883238', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机6', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883239', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机7', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883240', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机8', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883241', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机9', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883242', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机10', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883243', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机11', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883244', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机12', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883245', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机13', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883246', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机14', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883247', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机15', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388229280883248', 'admin', '2020-11-26 15:22:35', NULL, NULL, '华为手机1', 25, 35, '345', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388231401967996', 'admin', '2020-07-11 11:36:36', NULL, NULL, '小米手机', 25, 35, '445', '1256629667445714946', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('15884388463052345317', 'admin', '2020-07-11 11:36:36', NULL, NULL, '小米手机1', 55, 55, '55', '1256629667445714946', '2');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1589514837779251202', 'admin', '2022-11-07 15:07:24', NULL, NULL, '小米手机2', 2323, 2323, '', '1586557968995545089', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1589514956499025921', 'admin', '2022-11-07 15:07:52', NULL, NULL, '小米手机3', 222, 222, '222', '1589514956490637313', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1589514956515803137', 'admin', '2022-11-07 15:07:52', NULL, NULL, '小米手机4', NULL, NULL, '', '1589514956490637313', '');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1589516804542922753', 'admin', '2022-11-23 17:20:56', NULL, NULL, '小米手机5', NULL, NULL, NULL, '1589516804530339842', NULL);
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1589516804551311361', 'admin', '2022-11-07 15:15:13', NULL, NULL, '小米手机6', NULL, NULL, NULL, '1589516804530339842', NULL);
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1597149156278525953', 'admin', '2022-11-28 16:43:27', NULL, NULL, '水果手机4', 2, 2, '22', '1597149156089782273', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1683074969716346881', 'admin', '2023-12-06 15:26:44', NULL, NULL, '水果手机5', 5000, 21, '121', '1683074969561157634', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1714472725034704898', 'admin', '2023-12-06 15:26:44', NULL, NULL, '办公椅子', 50, 100, NULL, '1683074969561157634', '1');
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1732300515406647298', 'admin', '2023-12-06 15:26:44', NULL, NULL, '笔记本电脑', 3, 3, NULL, '1683074969561157634', NULL);
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('1732300515406647299', 'admin', '2023-12-06 15:26:44', NULL, NULL, '鼠标', 3, 3, NULL, '1683074969561157634', NULL);
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('402831816a38e7fd016a38e7fdeb0001', 'admin', '2019-04-20 12:01:29', NULL, NULL, '笔记本', 100, 10, NULL, '402831816a38e7fd016a38e7fddf0000', NULL);
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('402831816a38e7fd016a38e7fdf10002', 'admin', '2019-04-20 12:01:29', NULL, NULL, '显示器', 300, 1, NULL, '402831816a38e7fd016a38e7fddf0000', NULL);
INSERT INTO `rep_demo_order_product` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `product_name`, `price`, `num`, `descc`, `order_fk_id`, `pro_type`) VALUES ('4028810c6b40244b016b406884080005', 'admin', '2020-02-24 02:05:38', NULL, NULL, '键盘', NULL, 33, '', '402831816a38e7fd016a38e825c90003', '');


-- AI助手
DELETE FROM `sys_permission` where id = '1750696917064790018';
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1750696917064790018', '1438108176273760258', 'AI助手', '/ai', 'dashboard/ai/index', 1, '', NULL, 1, NULL, '0', 4.00, 0, 'ant-design:android-filled', 1, 0, 0, 0, NULL, 'admin', '2024-01-26 09:47:26', 'admin', '2024-01-26 20:42:23', 0, 0, NULL, 0);

-- 模版配置：库存管理可视化大屏
INSERT INTO onl_drag_page (id, name, path, background_color, background_image, design_type, theme, style, cover_url, template, protection_code, type, iz_template, create_by, create_time, update_by, update_time, low_app_id, tenant_id, update_count, visits_num) VALUES ('910394028067438592', '库存管理可视化大屏', '/drag/page/view/910394028067438592', NULL, NULL, 100, 'default', 'default', NULL, '[{\"component\":\"JText\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":0,\"x\":0,\"h\":8,\"i\":\"377bfc6b-26f1-4fb0-8fe1-0acbc39149e2\",\"y\":0,\"orderNum\":0,\"pageCompId\":\"912280749117849600\"},{\"component\":\"JGrowCard\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":10,\"x\":0,\"h\":31,\"i\":\"94fb7d28-1f93-4506-8310-f39f6ca8f356\",\"y\":8,\"orderNum\":10,\"pageCompId\":\"912280749151404032\"},{\"component\":\"JText\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":41,\"x\":0,\"h\":5,\"i\":\"79eb8d80-8720-4dac-b4ab-a1bd295d3fa5\",\"y\":39,\"orderNum\":65,\"pageCompId\":\"912280749168181248\"},{\"component\":\"JList\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":51,\"x\":0,\"h\":11,\"i\":\"3e12ea9a-04b8-4f1a-819a-1666be83bb2c\",\"y\":44,\"orderNum\":51,\"pageCompId\":\"912280749189152768\"},{\"component\":\"JText\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":63,\"x\":0,\"h\":5,\"i\":\"b1bc2b1b-4e38-4042-942d-50978e79236f\",\"y\":55,\"orderNum\":61,\"pageCompId\":\"912280749218512896\"},{\"component\":\"JList\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":73,\"x\":0,\"h\":11,\"i\":\"ee6f5621-53ef-4d0f-a6c9-fd33982b2be1\",\"y\":60,\"orderNum\":71,\"pageCompId\":\"912280749239484416\"},{\"component\":\"JText\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":84,\"x\":0,\"h\":5,\"i\":\"9d1b768d-17ac-461b-8597-41a969fd1589\",\"y\":71,\"orderNum\":82,\"pageCompId\":\"912280749256261632\"},{\"component\":\"JList\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":94,\"x\":0,\"h\":11,\"i\":\"380b67f4-ada6-47df-83a0-83b9c62fa435\",\"y\":76,\"orderNum\":92,\"pageCompId\":\"912280749273038848\"},{\"component\":\"JText\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":105,\"x\":0,\"h\":5,\"i\":\"0b6da57d-9804-4bfa-8ab8-54d690f8a09a\",\"y\":87,\"orderNum\":103,\"pageCompId\":\"912280749289816064\"},{\"component\":\"JList\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":115,\"x\":0,\"h\":11,\"i\":\"c2551fe6-5a8f-4d45-bb3a-aa86dd6b46cd\",\"y\":92,\"orderNum\":113,\"pageCompId\":\"912280749314981888\"},{\"component\":\"JBar\",\"pcX\":5,\"w\":10,\"moved\":false,\"pcY\":41,\"x\":5,\"h\":20,\"i\":\"acecbdec-0b6b-4744-aeed-3f969e7915b6\",\"y\":39,\"orderNum\":124,\"pageCompId\":\"912280749335953408\"},{\"component\":\"JPie\",\"pcX\":15,\"w\":9,\"moved\":false,\"pcY\":41,\"x\":15,\"h\":20,\"i\":\"e5b3c319-4457-456a-86a1-551301d1354f\",\"y\":39,\"orderNum\":124,\"pageCompId\":\"912280749348536320\"},{\"component\":\"JBar\",\"pcX\":5,\"w\":10,\"moved\":false,\"pcY\":67,\"x\":5,\"h\":22,\"i\":\"a3420701-faac-4d38-b7e2-9c12c3dc45e2\",\"y\":59,\"orderNum\":124,\"pageCompId\":\"912280749365313536\"},{\"component\":\"JBar\",\"pcX\":15,\"w\":9,\"moved\":false,\"pcY\":67,\"x\":15,\"h\":21,\"i\":\"a100e000-aee6-4138-8c72-0e4a830ec8de\",\"y\":59,\"orderNum\":124,\"pageCompId\":\"912280749382090752\"},{\"component\":\"JMultipleBar\",\"pcX\":5,\"w\":10,\"moved\":false,\"pcY\":95,\"x\":5,\"h\":22,\"i\":\"8055a2a9-1e24-4c28-8092-7c39cc219e78\",\"y\":81,\"orderNum\":124,\"pageCompId\":\"912280749398867968\"},{\"component\":\"JMultipleBar\",\"pcX\":15,\"w\":9,\"moved\":false,\"pcY\":95,\"x\":15,\"h\":23,\"i\":\"7d4ec916-020b-4f35-baff-87951bf2aa48\",\"y\":80,\"orderNum\":124,\"pageCompId\":\"912280749419839488\"},{\"component\":\"JCommonTable\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":125,\"x\":0,\"h\":45,\"i\":\"49592c96-4da5-4afb-9c15-fa7bea72bbc1\",\"y\":103,\"orderNum\":131,\"pageCompId\":\"912280749440811008\"}]', NULL, '1', '1', 'admin', '2024-01-25 15:07:40', 'admin', '2024-01-30 20:04:50', NULL, 0, 53, 5);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338468331520', NULL, '910394028067438592', NULL, 'JText', '{\"chartData\":\"库存管理可视化大屏\",\"borderColor\":\"#059DA8\",\"size\":{\"width\":1817,\"height\":78},\"background\":\"#059DA8\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"fontSize\":30,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338501885952', NULL, '910394028067438592', NULL, 'JGrowCard', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"库存管理-卡片\",\"query\":[],\"h\":19,\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryManagement\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"chartData\":\"[  {    \\\"title\\\": \\\"访问数\\\",    \\\"icon\\\": \\\"icon-jeecg-qianbao\\\",    \\\"value\\\": 2000,    \\\"total\\\": 120000,    \\\"prefix\\\": \\\"$\\\",    \\\"color\\\": \\\"green\\\",    \\\"action\\\": \\\"月\\\"  },  {    \\\"title\\\": \\\"成交额\\\",    \\\"icon\\\": \\\"icon-jeecg-youhuiquan\\\",    \\\"value\\\": 20000,    \\\"total\\\": 500000,    \\\"prefix\\\": \\\"$\\\",    \\\"color\\\": \\\"blue\\\",    \\\"action\\\": \\\"月\\\"  },  {    \\\"title\\\": \\\"下载数\\\",    \\\"icon\\\": \\\"icon-jeecg-tupian\\\",    \\\"value\\\": 8000,    \\\"prefix\\\": \\\"$\\\",    \\\"total\\\": 120000,    \\\"color\\\": \\\"orange\\\",    \\\"action\\\": \\\"周\\\"  },  {    \\\"title\\\": \\\"成交数\\\",    \\\"icon\\\": \\\"icon-jeecg-jifen\\\",    \\\"value\\\": 5000,    \\\"prefix\\\": \\\"$\\\",    \\\"total\\\": 50000,    \\\"color\\\": \\\"purple\\\",    \\\"action\\\": \\\"年\\\"  }]\",\"size\":{\"width\":1817,\"height\":331},\"dataSetId\":\"910406419257802752\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"icon\",\"text\":\"icon\",\"value\":\"icon\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"},{\"label\":\"action\",\"text\":\"action\",\"value\":\"action\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"icon\":{\"scriptUrl\":\"//at.alicdn.com/t/font_3237315_b3fqd960glt.js\",\"fontSize\":20},\"body\":{\"horizontal\":7,\"color\":\"#000000\",\"vertical\":5,\"span\":8},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"更多\",\"title\":\"统计卡片\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338527051776', NULL, '910394028067438592', NULL, 'JText', '{\"chartData\":\"AIR 库存情况\",\"borderColor\":\"#059DA8\",\"size\":{\"width\":371,\"height\":45},\"background\":\"#059DA8\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":7,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338543828992', NULL, '910394028067438592', NULL, 'JList', '{\"borderColor\":\"#FFFFFF\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"描述\"},{\"mapping\":\"value\",\"filed\":\"时间\"},{\"mapping\":\"\",\"filed\":\"封面\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"air库存情况\",\"query\":[],\"h\":24,\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryStatus?type=air\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/list\",\"timeOut\":-1,\"chartData\":\"[  {    \\\"title\\\": \\\"通知一\\\",    \\\"date\\\": \\\"10000\\\"  },  {    \\\"title\\\": \\\"通知二\\\",    \\\"date\\\": \\\"20000\\\"  },  {    \\\"title\\\": \\\"通知三\\\",    \\\"date\\\": \\\"30000\\\"  },  {    \\\"title\\\": \\\"通知四\\\",    \\\"date\\\": \\\"40000\\\"  }]\",\"size\":{\"width\":371,\"height\":111},\"dataSetId\":\"910419343896526848\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"layout\":\"horizontal\",\"showTitlePrefix\":false,\"titleFontSize\":15,\"showTimePrefix\":false,\"body\":{\"color\":\"#000000\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338564800512', NULL, '910394028067438592', NULL, 'JText', '{\"chartData\":\"LITE 库存情况\",\"borderColor\":\"#059DA8\",\"size\":{\"width\":371,\"height\":45},\"background\":\"#059DA8\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":7,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338585772032', NULL, '910394028067438592', NULL, 'JList', '{\"borderColor\":\"#FFFFFF\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"描述\"},{\"mapping\":\"value\",\"filed\":\"时间\"},{\"mapping\":\"\",\"filed\":\"封面\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"lite库存情况\",\"query\":[],\"h\":24,\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryStatus?type=lite\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/list\",\"timeOut\":-1,\"chartData\":\"[{\\\"title\\\":\\\"通知一\\\",\\\"date\\\":\\\"2022-3-9 14:20:21\\\"},{\\\"title\\\":\\\"通知二\\\",\\\"date\\\":\\\"2022-3-8 14:20:21\\\"},{\\\"title\\\":\\\"通知三\\\",\\\"date\\\":\\\"2022-3-7 14:20:21\\\"},{\\\"title\\\":\\\"通知四\\\",\\\"date\\\":\\\"2022-3-4 14:20:21\\\"}]\",\"size\":{\"width\":371,\"height\":111},\"dataSetId\":\"910423953398874112\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"layout\":\"horizontal\",\"showTitlePrefix\":false,\"titleFontSize\":15,\"showTimePrefix\":false}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338602549248', NULL, '910394028067438592', NULL, 'JText', '{\"chartData\":\"SUPER 库存情况\",\"borderColor\":\"#059DA8\",\"size\":{\"width\":371,\"height\":45},\"background\":\"#059DA8\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":7,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338615132160', NULL, '910394028067438592', NULL, 'JList', '{\"borderColor\":\"#FFFFFF\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"描述\"},{\"mapping\":\"value\",\"filed\":\"时间\"},{\"mapping\":\"\",\"filed\":\"封面\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"super库存情况\",\"query\":[],\"h\":24,\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryStatus?type=super\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/list\",\"timeOut\":-1,\"chartData\":\"[{\\\"title\\\":\\\"通知一\\\",\\\"date\\\":\\\"2022-3-9 14:20:21\\\"},{\\\"title\\\":\\\"通知二\\\",\\\"date\\\":\\\"2022-3-8 14:20:21\\\"},{\\\"title\\\":\\\"通知三\\\",\\\"date\\\":\\\"2022-3-7 14:20:21\\\"},{\\\"title\\\":\\\"通知四\\\",\\\"date\\\":\\\"2022-3-4 14:20:21\\\"}]\",\"size\":{\"width\":371,\"height\":111},\"dataSetId\":\"910425301976662016\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"layout\":\"horizontal\",\"showTitlePrefix\":false,\"titleFontSize\":15,\"showTimePrefix\":false}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338640297984', NULL, '910394028067438592', NULL, 'JText', '{\"chartData\":\"ULTRA 库存情况\",\"borderColor\":\"#059DA8\",\"size\":{\"width\":371,\"height\":45},\"background\":\"#059DA8\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":7,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338661269504', NULL, '910394028067438592', NULL, 'JList', '{\"borderColor\":\"#FFFFFF\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"描述\"},{\"mapping\":\"value\",\"filed\":\"时间\"},{\"mapping\":\"\",\"filed\":\"封面\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"ultra库存情况\",\"query\":[],\"h\":24,\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryStatus?type=ultra\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/list\",\"timeOut\":-1,\"chartData\":\"[{\\\"title\\\":\\\"通知一\\\",\\\"date\\\":\\\"2022-3-9 14:20:21\\\"},{\\\"title\\\":\\\"通知二\\\",\\\"date\\\":\\\"2022-3-8 14:20:21\\\"},{\\\"title\\\":\\\"通知三\\\",\\\"date\\\":\\\"2022-3-7 14:20:21\\\"},{\\\"title\\\":\\\"通知四\\\",\\\"date\\\":\\\"2022-3-4 14:20:21\\\"}]\",\"size\":{\"width\":371,\"height\":111},\"dataSetId\":\"910427052079366144\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"layout\":\"horizontal\",\"showTitlePrefix\":false,\"titleFontSize\":15,\"showTimePrefix\":false,\"body\":{\"color\":\"#000000\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338678046720', NULL, '910394028067438592', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"产品库存占比情况\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=storkProportion\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":751,\"height\":210},\"dataSetId\":\"910430104345690112\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"},\"interval\":2},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"rotate\":0,\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":50,\"left\":76,\"bottom\":67,\"show\":false,\"right\":3},\"series\":[{\"barWidth\":39,\"data\":[],\"color\":[\"#207B85\",\"#2C5E5A\",\"#36756E\"],\"itemStyle\":{\"color\":\"#009BA7\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"产品库存占比情况\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338699018240', NULL, '910394028067438592', NULL, 'JPie', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"仓库状态情况\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=warehouseStatus\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":-1,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":675,\"height\":210},\"dataSetId\":\"910431120222896128\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"bottom\":115,\"show\":false},\"legend\":{\"orient\":\"vertical\"},\"series\":[{\"data\":[],\"color\":[\"#059DA8\",\"#62A69C\",\"#489C8F\"],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 0.5)\"}},\"label\":{\"color\":\"#000000\",\"show\":false},\"type\":\"pie\",\"radius\":\"50%\"}],\"tooltip\":{\"show\":true,\"trigger\":\"item\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":true,\"text\":\"仓库状态情况\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338711601152', NULL, '910394028067438592', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"当月出库情况\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=outbound\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":751,\"height\":232},\"dataSetId\":\"910435726206222336\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"interval\":2}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":48,\"left\":62,\"bottom\":62,\"show\":false},\"series\":[{\"barWidth\":40,\"data\":[],\"color\":[\"#075A63\",\"#285754\",\"#37706A\",\"#63968F\",\"#09A7B3\"],\"itemStyle\":{\"color\":\"#059DA8\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"当月出库情况\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338728378368', NULL, '910394028067438592', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"当月入库情况\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=warehousing\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":675,\"height\":221},\"dataSetId\":\"910441161197928448\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"interval\":2}},\"grid\":{\"top\":45,\"bottom\":58,\"show\":false},\"series\":[{\"barWidth\":40,\"data\":[],\"itemStyle\":{\"color\":\"#059DA8\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"当月入库情况\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338745155584', NULL, '910394028067438592', NULL, 'JMultipleBar', '{\"borderColor\":\"#FFFFFF\",\"dataMapping\":[{\"mapping\":\"type\",\"filed\":\"分组\"},{\"mapping\":\"title\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"产品库存覆盖率情况\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=coverage\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"1991\\\",\\\"value\\\":3,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1992\\\",\\\"value\\\":4,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1993\\\",\\\"value\\\":3.5,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1994\\\",\\\"value\\\":5,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1995\\\",\\\"value\\\":4.9,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1996\\\",\\\"value\\\":6,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1997\\\",\\\"value\\\":7,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1998\\\",\\\"value\\\":9,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1999\\\",\\\"value\\\":13,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1991\\\",\\\"value\\\":3,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1992\\\",\\\"value\\\":4,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1993\\\",\\\"value\\\":3.5,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1994\\\",\\\"value\\\":5,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1995\\\",\\\"value\\\":4.9,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1996\\\",\\\"value\\\":6,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1997\\\",\\\"value\\\":7,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1998\\\",\\\"value\\\":9,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1999\\\",\\\"value\\\":13,\\\"type\\\":\\\"Bor\\\"}]\",\"size\":{\"width\":751,\"height\":232},\"dataSetId\":\"910442989620871168\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"type\",\"text\":\"type\",\"value\":\"type\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#FFFFFF\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"type\":\"value\",\"nameTextStyle\":{\"color\":\"#FFFFFF\"}},\"grid\":{\"top\":50,\"left\":94,\"bottom\":65},\"series\":[{\"color\":[\"#05A2AD\",\"#67ABA1\",\"#4AA194\"],\"itemStyle\":{\"color\":\"#64B5F6\"}}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"产品库存覆盖率情况\",\"textStyle\":{\"color\":\"#FFFFFF\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338770321408', NULL, '910394028067438592', NULL, 'JMultipleBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"type\",\"filed\":\"分组\"},{\"mapping\":\"title\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"产品库龄分布情况\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=stockAge\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"1991\\\",\\\"value\\\":3,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1992\\\",\\\"value\\\":4,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1993\\\",\\\"value\\\":3.5,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1994\\\",\\\"value\\\":5,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1995\\\",\\\"value\\\":4.9,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1996\\\",\\\"value\\\":6,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1997\\\",\\\"value\\\":7,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1998\\\",\\\"value\\\":9,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1999\\\",\\\"value\\\":13,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1991\\\",\\\"value\\\":3,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1992\\\",\\\"value\\\":4,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1993\\\",\\\"value\\\":3.5,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1994\\\",\\\"value\\\":5,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1995\\\",\\\"value\\\":4.9,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1996\\\",\\\"value\\\":6,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1997\\\",\\\"value\\\":7,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1998\\\",\\\"value\\\":9,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1999\\\",\\\"value\\\":13,\\\"type\\\":\\\"Bor\\\"}]\",\"size\":{\"width\":675,\"height\":243},\"dataSetId\":\"910447275230674944\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"type\",\"text\":\"type\",\"value\":\"type\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":54,\"bottom\":54},\"series\":[{\"color\":[\"#09A7B3\",\"#499E91\",\"#03A2AD\",\"#68F7D8\"]}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"产品库龄分布情况\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286338791292928', NULL, '910394028067438592', NULL, 'JCommonTable', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"产品库存状态监控\",\"query\":[],\"h\":42,\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=monitor\",\"drillData\":[],\"timeOut\":-1,\"chartData\":\"[  {    \\\"name\\\": \\\"4月\\\",    \\\"value\\\": 50  },  {    \\\"name\\\": \\\"2月\\\",    \\\"value\\\": 200  },  {    \\\"name\\\": \\\"3月\\\",    \\\"value\\\": 300  },  {    \\\"name\\\": \\\"4月\\\",    \\\"value\\\": 400  },  {    \\\"name\\\": \\\"5月\\\",    \\\"value\\\": 50  },  {    \\\"name\\\": \\\"6月\\\",    \\\"value\\\": 120  }]\",\"size\":{\"width\":1817,\"height\":485},\"dataSetId\":\"910455640270880768\",\"fieldOption\":[{\"label\":\"仓库\",\"text\":\"仓库\",\"value\":\"name\"},{\"label\":\"系列\",\"text\":\"系列\",\"value\":\"series\"},{\"label\":\"市场名\",\"text\":\"市场名\",\"value\":\"marketName\"},{\"label\":\"机型\",\"text\":\"机型\",\"value\":\"model\"},{\"label\":\"产品状态\",\"text\":\"产品状态\",\"value\":\"productStatus\"},{\"label\":\"当前库存\",\"text\":\"当前库存\",\"value\":\"inventory\"},{\"label\":\"库存状态\",\"text\":\"库存状态\",\"value\":\"inventoryStatus\"}],\"dataSetType\":\"api\",\"seriesType\":[],\"background\":\"#FFFFFF\",\"w\":24,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"columns\":[{\"izShow\":\"Y\",\"dataIndex\":\"name\",\"title\":\"仓库\"},{\"izShow\":\"Y\",\"dataIndex\":\"series\",\"title\":\"系列\"},{\"izShow\":\"Y\",\"dataIndex\":\"marketName\",\"title\":\"市场名\"},{\"izShow\":\"Y\",\"dataIndex\":\"model\",\"title\":\"机型\"},{\"izShow\":\"Y\",\"dataIndex\":\"productStatus\",\"title\":\"产品状态\"},{\"izShow\":\"Y\",\"dataIndex\":\"inventory\",\"title\":\"当前库存\"},{\"izShow\":\"Y\",\"dataIndex\":\"inventoryStatus\",\"title\":\"库存状态\"}],\"body\":{\"color\":\"#000000\"}}}', 'admin', '2024-01-30 20:27:02', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910401375061983232', '库存管理可视化大屏', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-25 15:36:52', 'admin', '2024-01-25 17:31:14', 'admin', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910406419257802752', '库存管理-卡片', '', '910401375061983232', '', 'https://apijeecgcom/mock/51/inventoryManagement', '', '0', 'api', 'get', '2024-01-25 15:56:55', 'admin', '2024-01-25 16:02:28', 'admin', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910419343896526848', 'air库存情况', '', '910401375061983232', '', 'https://apijeecgcom/mock/51/inventoryStatus?type=air', '', '0', 'api', 'get', '2024-01-25 16:48:16', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910423953398874112', 'lite库存情况', '', '910401375061983232', '', 'https://apijeecgcom/mock/51/inventoryStatus?type=lite', '', '0', 'api', 'get', '2024-01-25 17:06:35', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910425301976662016', 'super库存情况', '', '910401375061983232', '', 'https://apijeecgcom/mock/51/inventoryStatus?type=super', '', '0', 'api', 'get', '2024-01-25 17:11:57', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910427052079366144', 'ultra库存情况', '', '910401375061983232', '', 'https://apijeecgcom/mock/51/inventoryStatus?type=ultra', '', '0', 'api', 'get', '2024-01-25 17:18:54', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910430104345690112', '产品库存占比情况', '', '910401375061983232', '', 'https://apijeecgcom/mock/51/inventoryChart?type=storkProportion', '', '0', 'api', 'get', '2024-01-25 17:31:02', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910431120222896128', '仓库状态情况', '', '910401375061983232', '', 'https://apijeecgcom/mock/51/inventoryChart?type=warehouseStatus', '', '0', 'api', 'get', '2024-01-25 17:35:04', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910435726206222336', '当月出库情况', '', '910401375061983232', '', 'https://apijeecgcom/mock/51/inventoryChart?type=outbound', '', '0', 'api', 'get', '2024-01-25 17:53:22', 'admin', '2024-01-25 17:59:40', 'admin', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910441161197928448', '当月入库情况', '', '910401375061983232', '', 'https://apijeecgcom/mock/51/inventoryChart?type=warehousing', '', '0', 'api', 'get', '2024-01-25 18:14:58', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910442989620871168', '产品库存覆盖率情况', '', '910401375061983232', '', 'https://apijeecgcom/mock/51/inventoryChart?type=coverage', '', '0', 'api', 'get', '2024-01-25 18:22:14', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910447275230674944', '产品库龄分布情况', '', '910401375061983232', '', 'https://apijeecgcom/mock/51/inventoryChart?type=stockAge', '', '0', 'api', 'get', '2024-01-25 18:39:15', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910455640270880768', '产品库存状态监控', '', '910401375061983232', '', 'https://apijeecgcom/mock/51/inventoryChart?type=monitor', '', '0', 'api', 'get', '2024-01-25 19:12:30', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910407818167238656', '910406419257802752', 'title', 'title', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-25 15:56:55', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910407818200793088', '910406419257802752', 'icon', 'icon', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-25 15:56:55', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910407818234347520', '910406419257802752', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'admin', '2024-01-25 15:56:55', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910407818259513344', '910406419257802752', 'action', 'action', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 4, 'admin', '2024-01-25 16:02:28', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910419343930081280', '910419343896526848', 'title', 'title', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-25 16:48:16', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910419343967830016', '910419343896526848', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-25 16:48:16', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910423953432428544', '910423953398874112', 'title', 'title', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-25 17:06:35', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910423953465982976', '910423953398874112', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-25 17:06:35', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910425302010216448', '910425301976662016', 'title', 'title', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-25 17:11:57', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910425302043770880', '910425301976662016', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-25 17:11:57', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910427052108726272', '910427052079366144', 'title', 'title', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-25 17:18:54', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910427052146475008', '910427052079366144', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-25 17:18:54', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910430104379244544', '910430104345690112', 'title', 'title', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-25 17:31:02', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910430104412798976', '910430104345690112', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-25 17:31:02', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910431120256450560', '910431120222896128', 'title', 'title', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-25 17:35:04', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910431120290004992', '910431120222896128', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-25 17:35:04', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910437313372798976', '910435726206222336', 'title', 'title', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-25 17:53:22', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910437313410547712', '910435726206222336', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-25 17:53:22', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910441161239871488', '910441161197928448', 'title', 'title', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-25 18:14:58', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910441161277620224', '910441161197928448', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-25 18:14:58', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910442989654425600', '910442989620871168', 'title', 'title', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-25 18:22:14', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910442989692174336', '910442989620871168', 'type', 'type', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-25 18:22:14', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910442989713145856', '910442989620871168', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'admin', '2024-01-25 18:22:14', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910447275268423680', '910447275230674944', 'title', 'title', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-25 18:39:15', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910447275301978112', '910447275230674944', 'type', 'type', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-25 18:39:15', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910447275331338240', '910447275230674944', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'admin', '2024-01-25 18:39:15', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910455640329601024', '910455640270880768', 'name', '仓库', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-25 19:12:30', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910455640371544064', '910455640270880768', 'series', '系列', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-25 19:12:30', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910455640400904192', '910455640270880768', 'marketName', '市场名', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'admin', '2024-01-25 19:12:30', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910455640426070016', '910455640270880768', 'model', '机型', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'admin', '2024-01-25 19:12:30', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910455640455430144', '910455640270880768', 'productStatus', '产品状态', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 4, 'admin', '2024-01-25 19:12:30', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910455640484790272', '910455640270880768', 'inventory', '当前库存', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 5, 'admin', '2024-01-25 19:12:30', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910455640509956096', '910455640270880768', 'inventoryStatus', '库存状态', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 6, 'admin', '2024-01-25 19:12:30', NULL, NULL);

-- ---author:wangshuai---date:2024/1/30-----for:【QQYUN-7963】模版配置：某电商公司销售运营
INSERT INTO onl_drag_page (id, name, path, background_color, background_image, design_type, theme, style, cover_url, template, protection_code, type, iz_template, create_by, create_time, update_by, update_time, low_app_id, tenant_id, update_count, visits_num) VALUES ('910475721247866880', '某电商公司销售运营看板', '/drag/page/view/910475721247866880', NULL, NULL, 100, 'default', 'default', NULL, '[{\"component\":\"JText\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":0,\"x\":0,\"h\":8,\"i\":\"1aa60c03-aa11-400b-81a9-3a710ef7b17d\",\"y\":0,\"orderNum\":0,\"pageCompId\":\"912281374207557632\"},{\"component\":\"JGrowCard\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":10,\"x\":0,\"h\":17,\"i\":\"3ab3538e-5195-452c-82a7-08a1768548bb\",\"y\":8,\"orderNum\":10,\"pageCompId\":\"912281374245306368\"},{\"component\":\"JBubbleMap\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":27,\"x\":0,\"h\":42,\"i\":\"79506e2d-bfca-410b-bd5f-3fc1791af798\",\"y\":25,\"orderNum\":26,\"pageCompId\":\"912281374262083584\"},{\"component\":\"JCommonTable\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":69,\"x\":0,\"h\":45,\"i\":\"cd448408-0e55-41b3-8ab7-3b7dd8657055\",\"y\":67,\"orderNum\":72,\"pageCompId\":\"912281374287249408\"},{\"component\":\"JPie\",\"pcX\":8,\"w\":5,\"moved\":false,\"pcY\":10,\"x\":8,\"h\":35,\"i\":\"40b0322c-f6a9-4614-83b8-c77a086bc065\",\"y\":8,\"orderNum\":105,\"pageCompId\":\"912281374308220928\"},{\"component\":\"JNumber\",\"pcX\":13,\"w\":5,\"moved\":false,\"pcY\":10,\"x\":13,\"h\":17,\"i\":\"353858f6-fecf-4c42-81f4-537d24289a68\",\"y\":8,\"orderNum\":105,\"pageCompId\":\"912281374324998144\"},{\"component\":\"JNumber\",\"pcX\":13,\"w\":5,\"moved\":false,\"pcY\":27,\"x\":13,\"h\":18,\"i\":\"3a74f30d-8357-43c4-811c-a0a1c7201453\",\"y\":25,\"orderNum\":105,\"pageCompId\":\"912281374345969664\"},{\"component\":\"JPie\",\"pcX\":18,\"w\":6,\"moved\":false,\"pcY\":10,\"x\":18,\"h\":35,\"i\":\"2bd80a2b-f848-49d6-875b-05897deac11c\",\"y\":8,\"orderNum\":105,\"pageCompId\":\"912281374362746880\"},{\"component\":\"JBar\",\"pcX\":8,\"w\":8,\"moved\":false,\"pcY\":45,\"x\":8,\"h\":29,\"i\":\"043d13e0-4e90-47e4-8715-29cc400adeb1\",\"y\":43,\"orderNum\":105,\"pageCompId\":\"912281374387912704\"},{\"component\":\"JBar\",\"pcX\":16,\"w\":8,\"moved\":false,\"pcY\":45,\"x\":16,\"h\":29,\"i\":\"4b5fbe15-8931-48bb-a0f0-67855e782af4\",\"y\":43,\"orderNum\":114,\"pageCompId\":\"912281374408884224\"},{\"component\":\"JBar\",\"pcX\":8,\"w\":8,\"moved\":false,\"pcY\":74,\"x\":8,\"h\":40,\"i\":\"e2f99a5d-eaf4-4823-b1a7-eb0a878be363\",\"y\":72,\"orderNum\":114,\"pageCompId\":\"912281374438244352\"},{\"component\":\"JLine\",\"pcX\":16,\"w\":8,\"moved\":false,\"pcY\":74,\"x\":16,\"h\":40,\"i\":\"dad9aacc-549f-4418-8f55-8ca499aa83b4\",\"y\":72,\"orderNum\":114,\"pageCompId\":\"912281374459215872\"}]', NULL, '1', '1', 'admin', '2024-01-25 20:32:17', 'admin', '2024-01-30 20:07:19', NULL, 6902, 24, 5);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910478407196262400', '某电商公司销售运营', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-25 20:42:58', 'admin', '2024-01-25 20:43:03', 'admin', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910478592823574528', '销售状态', '', '910478407196262400', '', 'https://apijeecgcom/mock/51/commerceSalesOperations?type=saleStatus', '', '0', 'api', 'get', '2024-01-25 20:43:42', 'admin', '2024-01-25 20:45:22', 'admin', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910695134387552256', '各地区订单与仓库情况', '', '910478407196262400', '', 'https://apijeecgcom/mock/51/commerceSalesOperations?type=regionalOrders', '', '0', 'api', 'get', '2024-01-26 11:04:10', 'admin', '2024-01-26 11:34:02', 'admin', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910708293282349056', '季度目标完成', '', '910478407196262400', '', 'https://apijeecgcom/mock/51/commerceSalesOperations?type=quarterlyFinish', '', '0', 'api', 'get', '2024-01-26 11:56:27', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910721009699045376', '品牌销售占比', '', '910478407196262400', '', 'https://apijeecgcom/mock/51/commerceSalesOperations?type=brandSales', '', '0', 'api', 'get', '2024-01-26 12:46:59', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910732622212677632', '销售地区排行', '', '910478407196262400', '', 'https://apijeecgcom/mock/51/commerceSalesOperations?type=areaRanking', '', '0', 'api', 'get', '2024-01-26 13:33:07', 'admin', '2024-01-26 13:33:13', 'admin', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910737864308342784', '物流订单接收', '', '910478407196262400', '', 'https://apijeecgcom/mock/51/commerceSalesOperations?type=logisticsOrder', '', '0', 'api', 'get', '2024-01-26 13:53:57', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910740127152128000', '订单产生趋势', '', '910478407196262400', '', 'https://apijeecgcom/mock/51/commerceSalesOperations?type=generatingTrends', '', '0', 'api', 'get', '2024-01-26 14:02:57', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910479009821278208', '910478592823574528', 'title', 'title', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-25 20:43:42', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910479009859026944', '910478592823574528', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-25 20:43:42', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910479009884192768', '910478592823574528', 'unit', 'unit', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'admin', '2024-01-25 20:45:22', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910702652337733632', '910695134387552256', 'warehouseName', '仓库名称', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 11:04:10', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910702652383870976', '910695134387552256', 'warehouseCount', '仓库存量', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 11:04:10', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910702652413231104', '910695134387552256', 'name', '省份', 'String', NULL, NULL, 'N', NULL, NULL, NULL, 2, 'admin', '2024-01-26 11:04:10', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910702652446785536', '910695134387552256', 'value', '销售额', 'Integer', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'admin', '2024-01-26 11:04:10', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910708293328486400', '910708293282349056', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 11:56:27', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910708293370429440', '910708293282349056', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 11:56:27', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910721009736794112', '910721009699045376', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 12:46:59', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910721009778737152', '910721009699045376', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 12:46:59', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910732646887768064', '910732622212677632', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 13:33:13', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910732646933905408', '910732622212677632', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 13:33:13', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910737864350285824', '910737864308342784', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 13:53:57', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910737864400617472', '910737864308342784', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 13:53:57', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910740127202459648', '910740127152128000', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 14:02:57', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910740127244402688', '910740127152128000', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 14:02:57', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912281374207557632', NULL, '910475721247866880', NULL, 'JText', '{\"chartData\":\"某电商公司销售运营看板\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":1817,\"height\":78},\"background\":\"#0774F0\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":23,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:07:19', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911916295486226432', NULL, '910475721247866880', NULL, 'JGrowCard', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"图标\"},{\"mapping\":\"value\",\"filed\":\"数值\"},{\"mapping\":\"\",\"filed\":\"总计\"},{\"mapping\":\"\",\"filed\":\"前缀\"},{\"mapping\":\"\",\"filed\":\"颜色\"},{\"mapping\":\"unit\",\"filed\":\"单位\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"销售状态\",\"query\":[],\"h\":19,\"dataSetApi\":\"https://apijeecgcom/mock/51/commerceSalesOperations?type=saleStatus\",\"drillData\":[],\"url\":\"http://apijeecgcom/mock/42/nav\",\"timeOut\":0,\"chartData\":\"[{\\\"title\\\":\\\"访问数\\\",\\\"icon\\\":\\\"icon-jeecg-qianbao\\\",\\\"value\\\":2000,\\\"total\\\":120000,\\\"prefix\\\":\\\"$\\\",\\\"color\\\":\\\"green\\\",\\\"action\\\":\\\"月\\\"},{\\\"title\\\":\\\"成交额\\\",\\\"icon\\\":\\\"icon-jeecg-youhuiquan\\\",\\\"value\\\":20000,\\\"total\\\":500000,\\\"prefix\\\":\\\"$\\\",\\\"color\\\":\\\"blue\\\",\\\"action\\\":\\\"月\\\"},{\\\"title\\\":\\\"下载数\\\",\\\"icon\\\":\\\"icon-jeecg-tupian\\\",\\\"value\\\":8000,\\\"prefix\\\":\\\"$\\\",\\\"total\\\":120000,\\\"color\\\":\\\"orange\\\",\\\"action\\\":\\\"周\\\"},{\\\"title\\\":\\\"成交数\\\",\\\"icon\\\":\\\"icon-jeecg-jifen\\\",\\\"value\\\":5000,\\\"prefix\\\":\\\"$\\\",\\\"total\\\":50000,\\\"color\\\":\\\"purple\\\",\\\"action\\\":\\\"年\\\"}]\",\"size\":{\"width\":599,\"height\":177},\"dataSetId\":\"910478592823574528\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"},{\"label\":\"unit\",\"text\":\"unit\",\"value\":\"unit\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"icon\":{\"scriptUrl\":\"//atalicdncom/t/font_3237315_b3fqd960gltjs\",\"fontSize\":20},\"body\":{\"horizontal\":8,\"vertical\":8,\"span\":8},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"更多\",\"title\":\"统计卡片\"}}}', 'admin', '2024-01-29 19:56:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911916295511392256', NULL, '910475721247866880', NULL, 'JBubbleMap', '{\"borderColor\":\"#FFFFFF00\",\"commonOption\":{\"barSize\":10,\"gradientColor\":false,\"breadcrumb\":{\"drillDown\":false,\"textColor\":\"#000000\"},\"areaColor\":{\"color1\":\"#f7f7f7\",\"color2\":\"#fcc02e\"},\"barColor\":\"#fff176\",\"barColor2\":\"#fcc02e\",\"inRange\":{\"color\":[\"#04387b\",\"#467bc0\"]}},\"paramOption\":[],\"dataSetName\":\"各地区订单与仓库情况\",\"activeKey\":1,\"chartData\":\"[{\\\"name\\\":\\\"北京\\\",\\\"value\\\":199},{\\\"name\\\":\\\"新疆\\\",\\\"value\\\":180},{\\\"name\\\":\\\"河南\\\",\\\"value\\\":137},{\\\"name\\\":\\\"四川\\\",\\\"value\\\":125},{\\\"name\\\":\\\"黑龙江\\\",\\\"value\\\":123},{\\\"name\\\":\\\"广东\\\",\\\"value\\\":123},{\\\"name\\\":\\\"山东\\\",\\\"value\\\":119},{\\\"name\\\":\\\"福建\\\",\\\"value\\\":116},{\\\"name\\\":\\\"湖北\\\",\\\"value\\\":116},{\\\"name\\\":\\\"浙江\\\",\\\"value\\\":114},{\\\"name\\\":\\\"湖南\\\",\\\"value\\\":114},{\\\"name\\\":\\\"安徽\\\",\\\"value\\\":109},{\\\"name\\\":\\\"河北\\\",\\\"value\\\":102},{\\\"name\\\":\\\"江苏\\\",\\\"value\\\":92},{\\\"name\\\":\\\"江西\\\",\\\"value\\\":91},{\\\"name\\\":\\\"重庆\\\",\\\"value\\\":91},{\\\"name\\\":\\\"云南\\\",\\\"value\\\":83},{\\\"name\\\":\\\"吉林\\\",\\\"value\\\":82},{\\\"name\\\":\\\"山西\\\",\\\"value\\\":81},{\\\"name\\\":\\\"陕西\\\",\\\"value\\\":80},{\\\"name\\\":\\\"辽宁\\\",\\\"value\\\":67},{\\\"name\\\":\\\"贵州\\\",\\\"value\\\":62},{\\\"name\\\":\\\"广西\\\",\\\"value\\\":59},{\\\"name\\\":\\\"甘肃\\\",\\\"value\\\":56},{\\\"name\\\":\\\"内蒙古\\\",\\\"value\\\":47},{\\\"name\\\":\\\"天津\\\",\\\"value\\\":42},{\\\"name\\\":\\\"上海\\\",\\\"value\\\":24},{\\\"name\\\":\\\"宁夏\\\",\\\"value\\\":18},{\\\"name\\\":\\\"海南\\\",\\\"value\\\":14},{\\\"name\\\":\\\"青海\\\",\\\"value\\\":10},{\\\"name\\\":\\\"西藏\\\",\\\"value\\\":9}]\",\"fieldOption\":[{\"label\":\"仓库名称\",\"text\":\"仓库名称\",\"value\":\"warehouseName\"},{\"label\":\"仓库存量\",\"text\":\"仓库存量\",\"value\":\"warehouseCount\"},{\"label\":\"省份\",\"text\":\"省份\",\"value\":\"name\"},{\"label\":\"销售额\",\"text\":\"销售额\",\"value\":\"value\"}],\"seriesType\":[],\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"区域\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"jsConfig\":\"\",\"dataType\":2,\"query\":[],\"h\":50,\"dataSetApi\":\"https://apijeecgcom/mock/51/commerceSalesOperations?type=regionalOrders\",\"drillData\":[],\"url\":\"http://apijeecgcom/mock/33/radar\",\"timeOut\":0,\"size\":{\"width\":599,\"height\":452},\"dataSetId\":\"910695134387552256\",\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"w\":12,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"drillDown\":false,\"area\":{\"markerColor\":\"#DDE330\",\"shadowBlur\":10,\"markerCount\":5,\"markerOpacity\":1,\"name\":[\"中国\"],\"scatterLabelShow\":false,\"shadowColor\":\"#DDE330\",\"value\":[\"china\"],\"markerType\":\"effectScatter\"},\"geo\":{\"top\":80,\"itemStyle\":{\"normal\":{\"shadowOffsetX\":0,\"borderColor\":\"#a9a9a9\",\"shadowOffsetY\":0,\"areaColor\":\"\",\"shadowBlur\":0,\"borderWidth\":1,\"shadowColor\":\"#80d9f8\"},\"emphasis\":{\"areaColor\":\"#fff59c\",\"borderWidth\":0}},\"zoom\":1,\"label\":{\"emphasis\":{\"color\":\"#fff\",\"show\":false}},\"roam\":true},\"grid\":{\"bottom\":115,\"show\":false},\"legend\":{\"data\":[]},\"title\":{\"left\":10,\"show\":true,\"text\":\"\"},\"graphic\":[],\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"},\"visualMap\":{\"min\":0,\"top\":\"bottom\",\"max\":200,\"left\":\"5%\",\"calculable\":true,\"show\":false,\"type\":\"continuous\",\"seriesIndex\":[1]}}}', 'admin', '2024-01-29 19:56:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911916295532363776', NULL, '910475721247866880', NULL, 'JCommonTable', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"各地区订单与仓库情况\",\"query\":[],\"h\":42,\"dataSetApi\":\"https://apijeecgcom/mock/51/commerceSalesOperations?type=regionalOrders\",\"drillData\":[],\"timeOut\":-1,\"chartData\":\"[{\\\"name\\\":\\\"4月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"2月\\\",\\\"value\\\":200},{\\\"name\\\":\\\"3月\\\",\\\"value\\\":300},{\\\"name\\\":\\\"4月\\\",\\\"value\\\":400},{\\\"name\\\":\\\"5月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"6月\\\",\\\"value\\\":120}]\",\"size\":{\"width\":599,\"height\":485},\"dataSetId\":\"910695134387552256\",\"fieldOption\":[{\"label\":\"仓库名称\",\"text\":\"仓库名称\",\"value\":\"warehouseName\"},{\"label\":\"仓库存量\",\"text\":\"仓库存量\",\"value\":\"warehouseCount\"},{\"label\":\"省份\",\"text\":\"省份\",\"value\":\"name\"},{\"label\":\"销售额\",\"text\":\"销售额\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"seriesType\":[],\"background\":\"#FFFFFF\",\"w\":24,\"dataNum\":\"0\",\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"columns\":[{\"izShow\":\"Y\",\"dataIndex\":\"warehouseName\",\"title\":\"仓库名称\"},{\"izShow\":\"Y\",\"dataIndex\":\"warehouseCount\",\"title\":\"仓库存量\"},{\"izShow\":\"N\",\"dataIndex\":\"name\",\"title\":\"省份\"},{\"izShow\":\"Y\",\"dataIndex\":\"value\",\"title\":\"销售额\"}]}}', 'admin', '2024-01-29 19:56:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911916295557529600', NULL, '910475721247866880', NULL, 'JPie', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"季度目标完成\",\"query\":[],\"dataSetApi\":\"https://apijeecgcom/mock/51/commerceSalesOperations?type=quarterlyFinish\",\"drillData\":[],\"url\":\"http://apijeecgcom/mock/33/chart\",\"timeOut\":-1,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":371,\"height\":375},\"dataSetId\":\"910708293282349056\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"bottom\":115,\"show\":false},\"legend\":{\"orient\":\"vertical\"},\"series\":[{\"data\":[],\"color\":[\"#1F70E0\",\"#F0F2FA\"],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 05)\"}},\"type\":\"pie\",\"radius\":\"50%\"}],\"isRadius\":true,\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":true,\"text\":\"季度目标完成\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-29 19:56:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911916295582695424', NULL, '910475721247866880', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":1,\"dataSetName\":\"季度目标完成\",\"query\":[],\"h\":9,\"dataSetApi\":\"https://apijeecgcom/mock/51/commerceSalesOperations?type=quarterlyFinish\",\"drillData\":[],\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{  \\\"value\\\": 200}\",\"size\":{\"width\":371,\"height\":177},\"dataSetId\":\"910708293282349056\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#1C6CDE\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"季度销售额（万）\"}}}', 'admin', '2024-01-29 19:56:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911916295607861248', NULL, '910475721247866880', NULL, 'JNumber', '{\"chartData\":\"{  \\\"value\\\": \\\"39\\\"}\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":371,\"height\":188},\"background\":\"#FFFFFF\",\"w\":5,\"dataType\":1,\"h\":9,\"turnConfig\":{\"url\":\"\"},\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#1C6CDE\",\"text\":\"\",\"fontWeight\":\"bold\"},\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"同比增长（%）\"}}}', 'admin', '2024-01-29 19:56:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911916295633027072', NULL, '910475721247866880', NULL, 'JPie', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"品牌销售占比\",\"query\":[],\"dataSetApi\":\"https://apijeecgcom/mock/51/commerceSalesOperations?type=brandSales\",\"drillData\":[],\"url\":\"http://apijeecgcom/mock/33/chart\",\"timeOut\":-1,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":447,\"height\":375},\"dataSetId\":\"910721009699045376\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"left\":47,\"bottom\":115,\"show\":false},\"legend\":{\"r\":1,\"orient\":\"vertical\",\"t\":1},\"series\":[{\"data\":[],\"color\":[\"#0E52B0\",\"#118FF0\",\"#97CFFC\",\"#216DC4\",\"#60AEF7\"],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 05)\"}},\"type\":\"pie\",\"radius\":\"50%\"}],\"isRadius\":true,\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":true,\"text\":\"品牌销售占比\",\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-29 19:56:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911916295658192896', NULL, '910475721247866880', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"销售地区排行\",\"query\":[],\"dataSetApi\":\"https://apijeecgcom/mock/51/commerceSalesOperations?type=areaRanking\",\"drillData\":[],\"url\":\"http://apijeecgcom/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":599,\"height\":309},\"dataSetId\":\"910732622212677632\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"},\"interval\":2},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":90,\"bottom\":115,\"show\":false},\"series\":[{\"barWidth\":40,\"data\":[],\"showBackground\":false,\"backgroundStyle\":{\"color\":\"#51626E\"},\"itemStyle\":{\"color\":\"#428BEF\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"show\":true,\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"销售额（万元）\",\"show\":true,\"text\":\"销售地区排行\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-29 19:56:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911916295683358720', NULL, '910475721247866880', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"品牌销售占比\",\"query\":[],\"dataSetApi\":\"https://apijeecgcom/mock/51/commerceSalesOperations?type=brandSales\",\"drillData\":[],\"url\":\"http://apijeecgcom/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":599,\"height\":309},\"dataSetId\":\"910721009699045376\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"interval\":2}},\"grid\":{\"top\":90,\"bottom\":115,\"show\":false},\"series\":[{\"barWidth\":40,\"data\":[],\"itemStyle\":{\"color\":\"#428BEF\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"subtext\":\"销售额（元）\",\"show\":true,\"text\":\"品牌销售情况\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-29 19:56:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911916295712718848', NULL, '910475721247866880', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"物流订单接收\",\"query\":[],\"dataSetApi\":\"https://apijeecgcom/mock/51/commerceSalesOperations?type=logisticsOrder\",\"drillData\":[],\"url\":\"http://apijeecgcom/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":599,\"height\":430},\"dataSetId\":\"910737864308342784\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"interval\":2}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"type\":\"value\",\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":90,\"bottom\":115,\"show\":false},\"series\":[{\"barWidth\":24,\"data\":[],\"showBackground\":false,\"backgroundStyle\":{\"color\":\"#51626E\"},\"itemStyle\":{\"color\":\"#428BEF\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"subtext\":\"接收订单（件）\",\"show\":true,\"text\":\"物流订单接收\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-29 19:56:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911916295737884672', NULL, '910475721247866880', NULL, 'JLine', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"订单产生趋势\",\"query\":[],\"dataSetApi\":\"https://apijeecgcom/mock/51/commerceSalesOperations?type=generatingTrends\",\"drillData\":[],\"url\":\"http://apijeecgcom/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1000,\\\"name\\\":\\\"联想\\\"},{\\\"value\\\":7350,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":5800,\\\"name\\\":\\\"华为\\\"},{\\\"value\\\":6000,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":3000,\\\"name\\\":\\\"戴尔\\\"}]\",\"size\":{\"width\":599,\"height\":430},\"dataSetId\":\"910740127152128000\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"customColor\":[{\"color\":\"#428BEF\"}],\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":90,\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"lineType\":\"area\",\"itemStyle\":{\"color\":\"#64B5F6\"},\"type\":\"line\"}],\"tooltip\":{\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"订单数（单）\",\"left\":10,\"text\":\"订单产生趋势\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-29 19:56:37', NULL, NULL);

-- ---author:wangshuai---date:2024/1/30-----for:【QQYUN-7963】模版配置：物业消防巡检状态
INSERT INTO onl_drag_page (id, name, path, background_color, background_image, design_type, theme, style, cover_url, template, protection_code, type, iz_template, create_by, create_time, update_by, update_time, low_app_id, tenant_id, update_count, visits_num) VALUES ('910744177604083712', '物业消防巡检状态', '/drag/page/view/910744177604083712', NULL, NULL, 100, 'default', 'default', NULL, '[{\"component\":\"JText\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":0,\"x\":0,\"h\":8,\"i\":\"5d85e389-7ee4-40dd-8544-80049646ee34\",\"y\":0,\"orderNum\":0,\"pageCompId\":\"912286986744152064\"},{\"component\":\"JText\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":10,\"x\":0,\"h\":6,\"i\":\"878306f4-8ff4-412c-b8d8-744b0897ae8f\",\"y\":8,\"orderNum\":10,\"pageCompId\":\"912286986777706496\"},{\"component\":\"JList\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":20,\"x\":0,\"h\":17,\"i\":\"fe852828-ba3d-46d1-884a-1c723b870d55\",\"y\":14,\"orderNum\":20,\"pageCompId\":\"912286986798678016\"},{\"component\":\"JText\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":37,\"x\":0,\"h\":7,\"i\":\"57fd4478-440a-4a6e-a115-186e14d5047a\",\"y\":31,\"orderNum\":38,\"pageCompId\":\"912286986819649536\"},{\"component\":\"JNumber\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":47,\"x\":0,\"h\":12,\"i\":\"bb98b9db-042b-445e-8672-34182191871d\",\"y\":38,\"orderNum\":48,\"pageCompId\":\"912286986844815360\"},{\"component\":\"JNumber\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":57,\"x\":0,\"h\":12,\"i\":\"7eeab8ac-66f7-4d80-81a8-2e75dcb7093a\",\"y\":50,\"orderNum\":58,\"pageCompId\":\"912286986869981184\"},{\"component\":\"JText\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":67,\"x\":0,\"h\":7,\"i\":\"d7a87bcb-e5a6-4092-9b44-be37c284761d\",\"y\":62,\"orderNum\":69,\"pageCompId\":\"912286986890952704\"},{\"component\":\"JCommonTable\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":77,\"x\":0,\"h\":51,\"i\":\"1397ca94-7293-48bd-bb4f-673355c0355e\",\"y\":69,\"orderNum\":79,\"pageCompId\":\"912286986916118528\"},{\"component\":\"JBubbleMap\",\"pcX\":8,\"w\":8,\"moved\":false,\"pcY\":20,\"x\":8,\"h\":32,\"i\":\"f0f84536-4ae3-43ff-aadb-6a1f113b1ab9\",\"y\":14,\"orderNum\":109,\"pageCompId\":\"912286986945478656\"},{\"component\":\"JText\",\"pcX\":8,\"w\":16,\"moved\":false,\"pcY\":10,\"x\":8,\"h\":6,\"i\":\"52c28b77-268c-4dc4-805e-a48b37657be1\",\"y\":8,\"orderNum\":109,\"pageCompId\":\"912286986966450176\"},{\"component\":\"JCommonTable\",\"pcX\":16,\"w\":8,\"moved\":false,\"pcY\":20,\"x\":16,\"h\":32,\"i\":\"8b2b0a47-c1fb-47bf-b8af-9c249d903baa\",\"y\":14,\"orderNum\":109,\"pageCompId\":\"912286987025170432\"},{\"component\":\"JPie\",\"pcX\":8,\"w\":8,\"moved\":false,\"pcY\":52,\"x\":8,\"h\":34,\"i\":\"619921d9-f1fe-4d09-8f3d-09238f6d3e4f\",\"y\":46,\"orderNum\":109,\"pageCompId\":\"912286987054530560\"},{\"component\":\"JBar\",\"pcX\":16,\"w\":8,\"moved\":false,\"pcY\":52,\"x\":16,\"h\":34,\"i\":\"2598e9f8-7611-49c1-97a5-018fba23f0e0\",\"y\":46,\"orderNum\":109,\"pageCompId\":\"912286987092279296\"},{\"component\":\"JLine\",\"pcX\":8,\"w\":8,\"moved\":false,\"pcY\":98,\"x\":8,\"h\":33,\"i\":\"01569400-637c-4d01-986e-c6604f88cc32\",\"y\":87,\"orderNum\":109,\"pageCompId\":\"912286987117445120\"},{\"component\":\"JCommonTable\",\"pcX\":16,\"w\":8,\"moved\":false,\"pcY\":86,\"x\":16,\"h\":33,\"i\":\"4fd26be3-3138-4cf5-9e7a-e24ff75bafad\",\"y\":87,\"orderNum\":114,\"pageCompId\":\"912286987138416640\"},{\"component\":\"JText\",\"pcX\":8,\"w\":16,\"moved\":false,\"pcY\":86,\"x\":8,\"h\":7,\"i\":\"e3bea36a-c897-41ec-9c8d-c572609a74e5\",\"y\":80,\"orderNum\":114,\"pageCompId\":\"912286987163582464\"}]', NULL, '1', '1', 'admin', '2024-01-26 14:19:02', 'admin', '2024-01-30 20:29:37', NULL, 6902, 33, 4);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910750333919608832', '物业消防巡检状态', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-26 14:43:30', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910750488542625792', '区域基本情况', '', '910750333919608832', '', 'https://apijeecgcom/mock/51/propertyFireFighting?type=regionBasicInformation', '', '0', 'api', 'get', '2024-01-26 14:44:07', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910754028661030912', '正常设备', '', '910750333919608832', '', 'https://apijeecgcom/mock/51/propertyFireFighting?type=normalDevice', '', '0', 'api', 'get', '2024-01-26 14:58:11', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910754465934000128', '异常设备', '', '910750333919608832', '', 'https://apijeecgcom/mock/51/propertyFireFighting?type=abnormalDevice', '', '0', 'api', 'get', '2024-01-26 14:59:55', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910759432656830464', '设备异常明细', '', '910750333919608832', '', 'https://apijeecgcom/mock/51/propertyFireFighting?type=equipmentDetails', '', '0', 'api', 'get', '2024-01-26 15:19:40', 'admin', '2024-01-26 15:20:43', 'admin', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910765056765509632', '小区地图分布', '', '910750333919608832', '', 'https://apijeecgcom/mock/51/propertyFireFighting?type=residentialDistributionMap', '', '0', 'api', 'get', '2024-01-26 15:42:00', 'admin', '2024-01-26 15:43:52', 'admin', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910773111884398592', '小区分布地图表格', '', '910750333919608832', '', 'https://apijeecgcom/mock/51/propertyFireFighting?type=residentialDistributionTable', '', '0', 'api', 'get', '2024-01-26 16:14:01', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910776816075587584', '室外消火栓泵', '', '910750333919608832', '', 'https://apijeecgcom/mock/51/propertyFireFighting?type=deviceCountProportion', '', '0', 'api', 'get', '2024-01-26 16:28:44', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910790230315417600', '区域设备数量	', '', '910750333919608832', '', 'https://apijeecgcom/mock/51/propertyFireFighting?type=areaDeviceCount', '', '0', 'api', 'get', '2024-01-26 17:22:02', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910794755508060160', '巡检任务数', '', '910750333919608832', '', 'https://apijeecgcom/mock/51/propertyFireFighting?type=inspectionTasksCount', '', '0', 'api', 'get', '2024-01-26 17:40:01', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('910797586147360768', '巡检任务数表格', '', '910750333919608832', '', 'https://apijeecgcom/mock/51/propertyFireFighting?type=inspectionTasksTable', '', '0', 'api', 'get', '2024-01-26 17:51:16', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910750488576180224', '910750488542625792', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 14:44:07', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910750488630706176', '910750488542625792', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 14:44:07', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910754028698779648', '910754028661030912', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 14:58:11', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910754465980137472', '910754465934000128', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 14:59:55', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910759700177928192', '910759432656830464', 'name', '小区名称', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 15:19:40', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910759700219871232', '910759432656830464', 'deviceName', '设备名称', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 15:19:40', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910759700257619968', '910759432656830464', 'deviceAddress', '设备地址', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'admin', '2024-01-26 15:19:40', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910759700295368704', '910759432656830464', 'deviceStatus', '设备状态', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'admin', '2024-01-26 15:19:40', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910765523046285312', '910765056765509632', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 15:42:00', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910765523092422656', '910765056765509632', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 15:42:00', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910773111934730240', '910773111884398592', 'areaName', '区域名称', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 16:14:01', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910773111976673280', '910773111884398592', 'value', '小区数量', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 16:14:01', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910776816117530624', '910776816075587584', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 16:28:44', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910776816167862272', '910776816075587584', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 16:28:44', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910790230357360640', '910790230315417600', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 17:22:02', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910790230407692288', '910790230315417600', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 17:22:02', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910794755550003200', '910794755508060160', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 17:40:01', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910794755591946240', '910794755508060160', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 17:40:01', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910797586197692416', '910797586147360768', 'inspectTime', '检查时间', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-26 17:51:16', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910797586294161408', '910797586147360768', 'deviceName', '设备名称', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-26 17:51:16', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910797586319327232', '910797586147360768', 'deviceAddress', '设备地址', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'admin', '2024-01-26 17:51:16', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910797586352881664', '910797586147360768', 'inspected', '检查人', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'admin', '2024-01-26 17:51:16', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910797586382241792', '910797586147360768', 'content', '检查内容', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 4, 'admin', '2024-01-26 17:51:16', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('910797586415796224', '910797586147360768', 'inspectResult', '巡查结果', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 5, 'admin', '2024-01-26 17:51:16', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286986744152064', NULL, '910744177604083712', NULL, 'JText', '{\"chartData\":\"小 区 消 防 巡 检 状 态\",\"borderColor\":\"#DB771F\",\"size\":{\"width\":1817,\"height\":78},\"background\":\"#DB771F\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"fontSize\":36,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286986777706496', NULL, '910744177604083712', NULL, 'JText', '{\"chartData\":\"区域基本情况\",\"borderColor\":\"#DB771F\",\"size\":{\"width\":599,\"height\":56},\"background\":\"#DB771F\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"fontSize\":22,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":37},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286986798678016', NULL, '910744177604083712', NULL, 'JList', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"描述\"},{\"mapping\":\"value\",\"filed\":\"时间\"},{\"mapping\":\"\",\"filed\":\"封面\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"区域基本情况\",\"query\":[],\"h\":24,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=regionBasicInformation\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/list\",\"timeOut\":-1,\"chartData\":\"[{\\\"title\\\":\\\"通知一\\\",\\\"date\\\":\\\"2022-3-9 14:20:21\\\"},{\\\"title\\\":\\\"通知二\\\",\\\"date\\\":\\\"2022-3-8 14:20:21\\\"},{\\\"title\\\":\\\"通知三\\\",\\\"date\\\":\\\"2022-3-7 14:20:21\\\"},{\\\"title\\\":\\\"通知四\\\",\\\"date\\\":\\\"2022-3-4 14:20:21\\\"}]\",\"size\":{\"width\":599,\"height\":177},\"dataSetId\":\"910750488542625792\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"layout\":\"horizontal\",\"showTitlePrefix\":true,\"titleFontSize\":18,\"showTimePrefix\":false,\"body\":{\"color\":\"#000000\"}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286986819649536', NULL, '910744177604083712', NULL, 'JText', '{\"chartData\":\"设备总数\",\"borderColor\":\"#DB771F\",\"size\":{\"width\":599,\"height\":67},\"background\":\"#DB771F\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"fontSize\":22,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":16,\"marginLeft\":34},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286986844815360', NULL, '910744177604083712', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"dataType\":2,\"dataSetName\":\"正常设备\",\"h\":9,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=normalDevice\",\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{  \\\"value\\\": \\\"15990\\\"}\",\"size\":{\"width\":599,\"height\":122},\"dataSetId\":\"910754028661030912\",\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#000000\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"正常设备\"}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286986869981184', NULL, '910744177604083712', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"异常设备\",\"query\":[],\"h\":9,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=abnormalDevice\",\"drillData\":[],\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{\\\"value\\\":\\\"1024\\\"}\",\"size\":{\"width\":599,\"height\":122},\"dataSetId\":\"910754465934000128\",\"fieldOption\":[{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#000000\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"异常设备\"}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286986890952704', NULL, '910744177604083712', NULL, 'JText', '{\"chartData\":\"设备异常明细\",\"borderColor\":\"#DB771F\",\"size\":{\"width\":599,\"height\":67},\"background\":\"#DB771F\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"fontSize\":22,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":15,\"marginLeft\":32},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286986916118528', NULL, '910744177604083712', NULL, 'JCommonTable', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"设备异常明细\",\"query\":[],\"h\":42,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=equipmentDetails\",\"drillData\":[],\"timeOut\":-1,\"chartData\":\"[{\\\"name\\\":\\\"4月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"2月\\\",\\\"value\\\":200},{\\\"name\\\":\\\"3月\\\",\\\"value\\\":300},{\\\"name\\\":\\\"4月\\\",\\\"value\\\":400},{\\\"name\\\":\\\"5月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"6月\\\",\\\"value\\\":120}]\",\"size\":{\"width\":599,\"height\":551},\"dataSetId\":\"910759432656830464\",\"fieldOption\":[{\"label\":\"小区名称\",\"text\":\"小区名称\",\"value\":\"name\"},{\"label\":\"设备名称\",\"text\":\"设备名称\",\"value\":\"deviceName\"},{\"label\":\"设备地址\",\"text\":\"设备地址\",\"value\":\"deviceAddress\"},{\"label\":\"设备状态\",\"text\":\"设备状态\",\"value\":\"deviceStatus\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":24,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"columns\":[{\"izShow\":\"Y\",\"dataIndex\":\"name\",\"title\":\"小区名称\"},{\"izShow\":\"Y\",\"dataIndex\":\"deviceName\",\"title\":\"设备名称\"},{\"izShow\":\"Y\",\"dataIndex\":\"deviceAddress\",\"title\":\"设备地址\"},{\"izShow\":\"Y\",\"dataIndex\":\"deviceStatus\",\"title\":\"设备状态\"}],\"body\":{\"color\":\"#000000\"}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286986945478656', NULL, '910744177604083712', NULL, 'JBubbleMap', '{\"borderColor\":\"#FFFFFF00\",\"commonOption\":{\"barSize\":10,\"gradientColor\":false,\"breadcrumb\":{\"drillDown\":false,\"textColor\":\"#000000\"},\"areaColor\":{\"color1\":\"#f7f7f7\",\"color2\":\"#fcc02e\"},\"barColor\":\"#fff176\",\"barColor2\":\"#fcc02e\",\"inRange\":{\"color\":[\"#04387b\",\"#467bc0\"]}},\"paramOption\":[],\"dataSetName\":\"小区地图分布\",\"activeKey\":1,\"chartData\":\"[  {    \\\"name\\\": \\\"廊坊\\\",    \\\"value\\\": 199  },  {    \\\"name\\\": \\\"新疆\\\",    \\\"value\\\": 180  },  {    \\\"name\\\": \\\"河南\\\",    \\\"value\\\": 137  },  {    \\\"name\\\": \\\"四川\\\",    \\\"value\\\": 125  },  {    \\\"name\\\": \\\"黑龙江\\\",    \\\"value\\\": 123  },  {    \\\"name\\\": \\\"广东\\\",    \\\"value\\\": 123  },  {    \\\"name\\\": \\\"山东\\\",    \\\"value\\\": 119  },  {    \\\"name\\\": \\\"福建\\\",    \\\"value\\\": 116  },  {    \\\"name\\\": \\\"湖北\\\",    \\\"value\\\": 116  },  {    \\\"name\\\": \\\"浙江\\\",    \\\"value\\\": 114  },  {    \\\"name\\\": \\\"湖南\\\",    \\\"value\\\": 114  },  {    \\\"name\\\": \\\"安徽\\\",    \\\"value\\\": 109  },  {    \\\"name\\\": \\\"河北\\\",    \\\"value\\\": 102  },  {    \\\"name\\\": \\\"江苏\\\",    \\\"value\\\": 92  },  {    \\\"name\\\": \\\"江西\\\",    \\\"value\\\": 91  },  {    \\\"name\\\": \\\"重庆\\\",    \\\"value\\\": 91  },  {    \\\"name\\\": \\\"云南\\\",    \\\"value\\\": 83  },  {    \\\"name\\\": \\\"吉林\\\",    \\\"value\\\": 82  },  {    \\\"name\\\": \\\"山西\\\",    \\\"value\\\": 81  },  {    \\\"name\\\": \\\"陕西\\\",    \\\"value\\\": 80  },  {    \\\"name\\\": \\\"辽宁\\\",    \\\"value\\\": 67  },  {    \\\"name\\\": \\\"贵州\\\",    \\\"value\\\": 62  },  {    \\\"name\\\": \\\"广西\\\",    \\\"value\\\": 59  },  {    \\\"name\\\": \\\"甘肃\\\",    \\\"value\\\": 56  },  {    \\\"name\\\": \\\"内蒙古\\\",    \\\"value\\\": 47  },  {    \\\"name\\\": \\\"天津\\\",    \\\"value\\\": 42  },  {    \\\"name\\\": \\\"上海\\\",    \\\"value\\\": 24  },  {    \\\"name\\\": \\\"宁夏\\\",    \\\"value\\\": 18  },  {    \\\"name\\\": \\\"海南\\\",    \\\"value\\\": 14  },  {    \\\"name\\\": \\\"青海\\\",    \\\"value\\\": 10  },  {    \\\"name\\\": \\\"西藏\\\",    \\\"value\\\": 9  }]\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"seriesType\":[],\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"区域\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"jsConfig\":\"\",\"dataType\":2,\"query\":[],\"h\":50,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=residentialDistributionMap\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/radar\",\"timeOut\":0,\"size\":{\"width\":599,\"height\":342},\"dataSetId\":\"910765056765509632\",\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"w\":12,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"drillDown\":false,\"area\":{\"markerColor\":\"#DDE330\",\"shadowBlur\":10,\"markerCount\":5,\"markerOpacity\":1,\"name\":[\"河北省\"],\"scatterLabelShow\":false,\"shadowColor\":\"#DDE330\",\"value\":[\"13\"],\"markerType\":\"effectScatter\"},\"geo\":{\"top\":27,\"aspectScale\":0.92,\"itemStyle\":{\"normal\":{\"shadowOffsetX\":0,\"borderColor\":\"#a9a9a9\",\"shadowOffsetY\":0,\"areaColor\":\"\",\"shadowBlur\":0,\"borderWidth\":1,\"shadowColor\":\"#80d9f8\"},\"emphasis\":{\"areaColor\":\"#fff59c\",\"borderWidth\":0}},\"zoom\":1,\"label\":{\"emphasis\":{\"color\":\"#fff\",\"show\":false}},\"roam\":true},\"grid\":{\"bottom\":115,\"show\":false},\"legend\":{\"data\":[]},\"title\":{\"left\":10,\"show\":true,\"text\":\"\",\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"graphic\":[],\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"},\"visualMap\":{\"min\":0,\"top\":\"bottom\",\"max\":200,\"left\":\"5%\",\"calculable\":true,\"show\":false,\"type\":\"continuous\",\"seriesIndex\":[1]}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286986966450176', NULL, '910744177604083712', NULL, 'JText', '{\"chartData\":\"小区地图分布\",\"borderColor\":\"#DB771F\",\"size\":{\"width\":1208,\"height\":56},\"background\":\"#DB771F\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"fontSize\":22,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":10,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286987025170432', NULL, '910744177604083712', NULL, 'JCommonTable', '{\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"小区分布地图表格\",\"query\":[],\"h\":42,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=residentialDistributionTable\",\"drillData\":[],\"timeOut\":-1,\"chartData\":\"[{\\\"name\\\":\\\"4月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"2月\\\",\\\"value\\\":200},{\\\"name\\\":\\\"3月\\\",\\\"value\\\":300},{\\\"name\\\":\\\"4月\\\",\\\"value\\\":400},{\\\"name\\\":\\\"5月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"6月\\\",\\\"value\\\":120}]\",\"size\":{\"width\":599,\"height\":342},\"dataSetId\":\"910773111884398592\",\"fieldOption\":[{\"label\":\"区域名称\",\"text\":\"区域名称\",\"value\":\"areaName\"},{\"label\":\"小区数量\",\"text\":\"小区数量\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"seriesType\":[],\"w\":24,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"columns\":[{\"izShow\":\"Y\",\"dataIndex\":\"areaName\",\"title\":\"区域名称\"},{\"izShow\":\"Y\",\"dataIndex\":\"value\",\"title\":\"小区数量\"}],\"title\":{\"textStyle\":{\"color\":\"#464646\"}}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286987054530560', NULL, '910744177604083712', NULL, 'JPie', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"室外消火栓泵\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=deviceCountProportion\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":-1,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":599,\"height\":364},\"dataSetId\":\"910776816075587584\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"bottom\":115,\"show\":false},\"legend\":{\"orient\":\"vertical\"},\"series\":[{\"data\":[],\"color\":[\"#3BB1E3\",\"#DB771F\",\"#B6C5D1\"],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 0.5)\"}},\"label\":{\"color\":\"#000000\",\"show\":true},\"type\":\"pie\",\"radius\":\"50%\"}],\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":true,\"text\":\"设备类型数量占比\",\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286987092279296', NULL, '910744177604083712', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"区域设备数量\\t\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=areaDeviceCount\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":599,\"height\":364},\"dataSetId\":\"910790230315417600\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"},\"interval\":2},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"type\":\"value\",\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":90,\"bottom\":115,\"show\":false},\"series\":[{\"barWidth\":21,\"data\":[],\"color\":[\"#3BB1E3\",\"#DB771F\",\"#B6C5D1\"],\"itemStyle\":{\"color\":\"#3BB1E3\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"区域设备数量\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286987117445120', NULL, '910744177604083712', NULL, 'JLine', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"巡检任务数\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=inspectionTasksCount\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1000,\\\"name\\\":\\\"联想\\\"},{\\\"value\\\":7350,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":5800,\\\"name\\\":\\\"华为\\\"},{\\\"value\\\":6000,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":3000,\\\"name\\\":\\\"戴尔\\\"}]\",\"size\":{\"width\":599,\"height\":353},\"dataSetId\":\"910794755508060160\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"customColor\":[{\"color\":\"#38B4EA\"}],\"grid\":{\"top\":90,\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"lineType\":\"area\",\"itemStyle\":{\"color\":\"#64B5F6\"},\"type\":\"line\"}],\"title\":{\"subtext\":\"\",\"left\":10,\"text\":\"\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286987138416640', NULL, '910744177604083712', NULL, 'JCommonTable', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"巡检任务数表格\",\"query\":[],\"h\":42,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=inspectionTasksTable\",\"drillData\":[],\"timeOut\":-1,\"chartData\":\"[{\\\"name\\\":\\\"4月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"2月\\\",\\\"value\\\":200},{\\\"name\\\":\\\"3月\\\",\\\"value\\\":300},{\\\"name\\\":\\\"4月\\\",\\\"value\\\":400},{\\\"name\\\":\\\"5月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"6月\\\",\\\"value\\\":120}]\",\"size\":{\"width\":599,\"height\":353},\"dataSetId\":\"910797586147360768\",\"fieldOption\":[{\"label\":\"检查时间\",\"text\":\"检查时间\",\"value\":\"inspectTime\"},{\"label\":\"设备名称\",\"text\":\"设备名称\",\"value\":\"deviceName\"},{\"label\":\"设备地址\",\"text\":\"设备地址\",\"value\":\"deviceAddress\"},{\"label\":\"检查人\",\"text\":\"检查人\",\"value\":\"inspected\"},{\"label\":\"检查内容\",\"text\":\"检查内容\",\"value\":\"content\"},{\"label\":\"巡查结果\",\"text\":\"巡查结果\",\"value\":\"inspectResult\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":24,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"columns\":[{\"izShow\":\"Y\",\"dataIndex\":\"inspectTime\",\"title\":\"检查时间\"},{\"izShow\":\"Y\",\"dataIndex\":\"deviceName\",\"title\":\"设备名称\"},{\"izShow\":\"Y\",\"dataIndex\":\"deviceAddress\",\"title\":\"设备地址\"},{\"izShow\":\"Y\",\"dataIndex\":\"inspected\",\"title\":\"检查人\"},{\"izShow\":\"Y\",\"dataIndex\":\"content\",\"title\":\"检查内容\"},{\"izShow\":\"Y\",\"dataIndex\":\"inspectResult\",\"title\":\"巡查结果\"}]}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912286987163582464', NULL, '910744177604083712', NULL, 'JText', '{\"chartData\":\"巡检任务数\",\"borderColor\":\"#DB771F\",\"size\":{\"width\":1208,\"height\":67},\"background\":\"#DB771F\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"fontSize\":22,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":12,\"marginLeft\":0},\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 20:29:37', NULL, NULL);

-- ---author:wangshuai---date:2024/1/30-----for:【QQYUN-7963】模版配置：某连锁饮品销售看板
INSERT INTO onl_drag_page (id, name, path, background_color, background_image, design_type, theme, style, cover_url, template, protection_code, type, iz_template, create_by, create_time, update_by, update_time, low_app_id, tenant_id, update_count, visits_num) VALUES ('910820508471705600', '某连锁饮品销售看板', '/drag/page/view/910820508471705600', NULL, NULL, 100, 'default', 'default', NULL, '[{\"component\":\"JText\",\"pcX\":0,\"w\":15,\"moved\":false,\"pcY\":0,\"x\":0,\"h\":10,\"i\":\"dcd6e80e-8865-4238-844b-2bb8998f755e\",\"y\":0,\"orderNum\":0,\"pageCompId\":\"911854596825595904\"},{\"component\":\"JForm\",\"pcX\":15,\"w\":9,\"moved\":false,\"pcY\":0,\"x\":15,\"h\":10,\"i\":\"50040bd1-c081-4e5b-a36a-ea6a5afce396\",\"y\":0,\"orderNum\":10,\"pageCompId\":\"911854596863344640\"},{\"component\":\"JGrowCard\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":10,\"x\":0,\"h\":30,\"i\":\"0efe3637-39ac-4f2e-82f4-e09540ee332a\",\"y\":10,\"orderNum\":10,\"pageCompId\":\"911854596892704768\"},{\"component\":\"JNumber\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":40,\"x\":0,\"h\":17,\"i\":\"2db8c9de-9f8b-49f8-8d7a-6b3166de0ed5\",\"y\":40,\"orderNum\":40,\"pageCompId\":\"911854596913676288\"},{\"component\":\"JNumber\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":57,\"x\":0,\"h\":16,\"i\":\"6f773617-eca8-4598-8260-dc701cc16e8b\",\"y\":57,\"orderNum\":50,\"pageCompId\":\"911854596930453504\"},{\"component\":\"JNumber\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":73,\"x\":0,\"h\":16,\"i\":\"868c63d3-cfc2-4e49-a1c5-6ee0418fd303\",\"y\":73,\"orderNum\":59,\"pageCompId\":\"911854596951425024\"},{\"component\":\"JNumber\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":89,\"x\":0,\"h\":17,\"i\":\"35a7308b-0482-48c8-8e46-ed50cfb67961\",\"y\":89,\"orderNum\":83,\"pageCompId\":\"911854596972396544\"},{\"component\":\"JLine\",\"pcX\":5,\"w\":8,\"moved\":false,\"pcY\":40,\"x\":5,\"h\":23,\"i\":\"2323a8fb-42ff-4a23-9b13-8f4f514dafb5\",\"y\":40,\"orderNum\":96,\"pageCompId\":\"911854596984979456\"},{\"component\":\"JLine\",\"pcX\":5,\"w\":8,\"moved\":false,\"pcY\":63,\"x\":5,\"h\":22,\"i\":\"715ebf76-9228-4b2e-8ab7-dcb351c2ff03\",\"y\":63,\"orderNum\":96,\"pageCompId\":\"911854597001756672\"},{\"component\":\"JLine\",\"pcX\":5,\"w\":8,\"moved\":false,\"pcY\":85,\"x\":5,\"h\":21,\"i\":\"cd981a55-258c-48cd-8e92-742185a711a3\",\"y\":85,\"orderNum\":96,\"pageCompId\":\"911854597022728192\"},{\"component\":\"JBar\",\"pcX\":13,\"w\":11,\"moved\":false,\"pcY\":40,\"x\":13,\"h\":33,\"i\":\"82016a70-aa98-42cb-b205-c14e5e212cf8\",\"y\":40,\"orderNum\":106,\"pageCompId\":\"911854597043699712\"},{\"component\":\"JCommonTable\",\"pcX\":13,\"w\":6,\"moved\":false,\"pcY\":73,\"x\":13,\"h\":33,\"i\":\"0b68bd42-a0e9-48f8-9a7b-794ba9ebf8cf\",\"y\":73,\"orderNum\":106,\"pageCompId\":\"911854597064671232\"},{\"component\":\"JRing\",\"pcX\":19,\"w\":5,\"moved\":false,\"pcY\":73,\"x\":19,\"h\":33,\"i\":\"5130130f-6076-45b1-8977-d9b5a4c894fc\",\"y\":73,\"orderNum\":106,\"pageCompId\":\"911854597085642752\"}]', NULL, '1', '1', 'admin', '2024-01-26 19:22:21', 'admin', '2024-01-29 15:56:38', NULL, 6902, 36, 3);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911854596825595904', NULL, '910820508471705600', NULL, 'JText', '{\"chartData\":\"某 连 锁 饮 品 销 售\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":1132,\"height\":100},\"background\":\"#FFFFFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#000000\",\"textAlign\":\"center\",\"fontSize\":40,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":18,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-29 15:51:27', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911854596863344640', NULL, '910820508471705600', NULL, 'JForm', '{\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":675,\"height\":100},\"background\":\"#FFFFFF\",\"w\":24,\"dataType\":1,\"h\":12,\"linkageConfig\":[{\"linkageId\":\"0efe3637-39ac-4f2e-82f4-e09540ee332a\",\"linkage\":[]},{\"linkageId\":\"2db8c9de-9f8b-49f8-8d7a-6b3166de0ed5\",\"linkage\":[]},{\"linkageId\":\"6f773617-eca8-4598-8260-dc701cc16e8b\",\"linkage\":[]},{\"linkageId\":\"868c63d3-cfc2-4e49-a1c5-6ee0418fd303\",\"linkage\":[]},{\"linkageId\":\"35a7308b-0482-48c8-8e46-ed50cfb67961\",\"linkage\":[]},{\"linkageId\":\"cd981a55-258c-48cd-8e92-742185a711a3\",\"linkage\":[]},{\"linkageId\":\"715ebf76-9228-4b2e-8ab7-dcb351c2ff03\",\"linkage\":[]},{\"linkageId\":\"2323a8fb-42ff-4a23-9b13-8f4f514dafb5\",\"linkage\":[]},{\"linkageId\":\"0b68bd42-a0e9-48f8-9a7b-794ba9ebf8cf\",\"linkage\":[]},{\"linkageId\":\"5130130f-6076-45b1-8977-d9b5a4c894fc\",\"linkage\":[]},{\"linkageId\":\"82016a70-aa98-42cb-b205-c14e5e212cf8\",\"linkage\":[]}],\"timeOut\":-1,\"option\":{\"fields\":[{\"fieldName\":\"date\",\"dictCode\":\"\",\"dateFormat\":\"YYYY-MM\",\"fieldTxt\":\"日期\",\"defaultValue\":\"2024-01\",\"searchMode\":\"single\",\"orderNum\":\"\",\"action\":\"\",\"id\":\"rowa82899dc-1e6b-47a1-acd8-ba2fb6f5df02\",\"izSearch\":\"1\",\"widgetType\":\"date\"}],\"body\":{\"color\":\"#000000\"},\"title\":{\"textStyle\":{\"color\":\"#464646\"}}}}', 'admin', '2024-01-29 15:51:27', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911854596892704768', NULL, '910820508471705600', NULL, 'JGrowCard', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"图标\"},{\"mapping\":\"value\",\"filed\":\"数值\"},{\"mapping\":\"\",\"filed\":\"总计\"},{\"mapping\":\"\",\"filed\":\"前缀\"},{\"mapping\":\"color\",\"filed\":\"颜色\"},{\"mapping\":\"action\",\"filed\":\"单位\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"销量额\",\"query\":[],\"h\":19,\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=salesVolume\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"chartData\":\"[  {    \\\"title\\\": \\\"访问数\\\",    \\\"icon\\\": \\\"icon-jeecg-qianbao\\\",    \\\"value\\\": 2000,    \\\"total\\\": 120000,    \\\"prefix\\\": \\\"$\\\",    \\\"color\\\": \\\"green\\\",    \\\"action\\\": \\\"月\\\"  },  {    \\\"title\\\": \\\"成交额\\\",    \\\"icon\\\": \\\"icon-jeecg-youhuiquan\\\",    \\\"value\\\": 20000,    \\\"total\\\": 500000,    \\\"prefix\\\": \\\"$\\\",    \\\"color\\\": \\\"blue\\\",    \\\"action\\\": \\\"月\\\"  },  {    \\\"title\\\": \\\"下载数\\\",    \\\"icon\\\": \\\"icon-jeecg-tupian\\\",    \\\"value\\\": 8000,    \\\"prefix\\\": \\\"$\\\",    \\\"total\\\": 120000,    \\\"color\\\": \\\"orange\\\",    \\\"action\\\": \\\"周\\\"  },  {    \\\"title\\\": \\\"成交数\\\",    \\\"icon\\\": \\\"icon-jeecg-jifen\\\",    \\\"value\\\": 5000,    \\\"prefix\\\": \\\"$\\\",    \\\"total\\\": 50000,    \\\"color\\\": \\\"purple\\\",    \\\"action\\\": \\\"年\\\"  }]\",\"size\":{\"width\":1817,\"height\":320},\"dataSetId\":\"911792028287287296\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"},{\"label\":\"icon\",\"text\":\"icon\",\"value\":\"icon\"},{\"label\":\"action\",\"text\":\"action\",\"value\":\"action\"},{\"label\":\"color\",\"text\":\"color\",\"value\":\"color\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"icon\":{\"scriptUrl\":\"//at.alicdn.com/t/font_3237315_b3fqd960glt.js\",\"fontSize\":20},\"body\":{\"horizontal\":6,\"vertical\":4,\"span\":8},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"更多\",\"title\":\"统计卡片\"}}}', 'admin', '2024-01-29 15:51:27', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911854596913676288', NULL, '910820508471705600', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"单月最高销售量分店\\t\",\"query\":[],\"h\":9,\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=topSellingBranch\",\"drillData\":[],\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{\\\"value\\\":\\\"1024\\\"}\",\"size\":{\"width\":371,\"height\":177},\"dataSetId\":\"911836343134896128\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#F3EFF0\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#F9B632\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#FF9736\",\"textStyle\":{\"color\":\"#FFFFFF\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"单月最高销量分店（新洲店）\"}}}', 'admin', '2024-01-29 15:51:27', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911854596930453504', NULL, '910820508471705600', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"单月最高销量品线\\t\",\"query\":[],\"h\":9,\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=salesLine\",\"drillData\":[],\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{\\\"value\\\":\\\"1024\\\"}\",\"size\":{\"width\":371,\"height\":166},\"dataSetId\":\"911836426450550784\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#E9F2FF\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#547BFE\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#547BFE\",\"textStyle\":{\"color\":\"#FFFFFF\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"单月最高销量品线（醇香奶茶）\"}}}', 'admin', '2024-01-29 15:51:27', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911854596951425024', NULL, '910820508471705600', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"单月最高销量单品\",\"query\":[],\"h\":9,\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=highestSellingItem\",\"drillData\":[],\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{\\\"value\\\":\\\"1024\\\"}\",\"size\":{\"width\":371,\"height\":166},\"dataSetId\":\"911836535531814912\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#EDFAFE\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#44C8F3\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#44C8F3\",\"textStyle\":{\"color\":\"#FFFFFF\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"单月最高销量单品（珍珠奶茶）\"}}}', 'admin', '2024-01-29 15:51:27', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911854596972396544', NULL, '910820508471705600', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"单月最高销量规格\",\"query\":[],\"h\":9,\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=maximumSalesSpecs\",\"drillData\":[],\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{\\\"value\\\":\\\"1024\\\"}\",\"size\":{\"width\":371,\"height\":177},\"dataSetId\":\"911836717715603456\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#F2FCF7\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#58DC92\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#9EFFB6\",\"textStyle\":{\"color\":\"#FFFFFF\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"单月最高销量规格（磨砂）\"}}}', 'admin', '2024-01-29 15:51:27', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911854596984979456', NULL, '910820508471705600', NULL, 'JLine', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"销售额走势\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=salesTrend\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1000,\\\"name\\\":\\\"联想\\\"},{\\\"value\\\":7350,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":5800,\\\"name\\\":\\\"华为\\\"},{\\\"value\\\":6000,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":3000,\\\"name\\\":\\\"戴尔\\\"}]\",\"size\":{\"width\":599,\"height\":243},\"dataSetId\":\"911836839023263744\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"},\"show\":false},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"},\"show\":true},\"nameTextStyle\":{\"color\":\"#333333\"}},\"customColor\":[{\"color\":\"#FBE1C2\"}],\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#FFFFFF\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":178,\"bottom\":157,\"show\":false},\"series\":[{\"data\":[],\"lineType\":\"area\",\"itemStyle\":{\"color\":\"#64b5f6\"},\"type\":\"line\"}],\"title\":{\"subtext\":\"销售金额（元）\",\"left\":10,\"text\":\"销售额走势\",\"subtextStyle\":{\"color\":\"#464646\"},\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-29 15:51:27', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911854597001756672', NULL, '910820508471705600', NULL, 'JLine', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"销量走势\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=salesTendency\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1000,\\\"name\\\":\\\"联想\\\"},{\\\"value\\\":7350,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":5800,\\\"name\\\":\\\"华为\\\"},{\\\"value\\\":6000,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":3000,\\\"name\\\":\\\"戴尔\\\"}]\",\"size\":{\"width\":599,\"height\":232},\"dataSetId\":\"911836922162757632\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"customColor\":[{\"color\":\"#D7EBFB\"}],\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":71,\"bottom\":62,\"show\":false},\"series\":[{\"data\":[],\"lineType\":\"area\",\"itemStyle\":{\"color\":\"#64b5f6\"},\"type\":\"line\"}],\"tooltip\":{\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"销售数量（杯）\",\"left\":1,\"text\":\"销量走势\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\",\"fontWeight\":\"normal\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-29 15:51:27', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911854597022728192', NULL, '910820508471705600', NULL, 'JLine', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"原料支出趋势\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=expenditureTrends\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1000,\\\"name\\\":\\\"联想\\\"},{\\\"value\\\":7350,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":5800,\\\"name\\\":\\\"华为\\\"},{\\\"value\\\":6000,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":3000,\\\"name\\\":\\\"戴尔\\\"}]\",\"size\":{\"width\":599,\"height\":221},\"dataSetId\":\"911845934736392192\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"customColor\":[{\"color\":\"#D5DFFE\"}],\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":54,\"bottom\":61,\"show\":false},\"series\":[{\"data\":[],\"lineType\":\"area\",\"itemStyle\":{\"color\":\"#64b5f6\"},\"type\":\"line\"}],\"tooltip\":{\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"支出费用（元）\",\"left\":10,\"text\":\"原料支出趋势\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-29 15:51:27', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911854597043699712', NULL, '910820508471705600', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"订单销售量\\t\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=orderSalesVolume\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":827,\"height\":353},\"dataSetId\":\"911837008343121920\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"},\"interval\":2},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"type\":\"value\",\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":48,\"bottom\":60,\"show\":false},\"series\":[{\"barWidth\":0,\"data\":[],\"showBackground\":false,\"backgroundStyle\":{\"color\":\"#51626E\"},\"itemStyle\":{\"color\":\"#85C6F3\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"订单销售量\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-29 15:51:27', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911854597064671232', NULL, '910820508471705600', NULL, 'JCommonTable', '{\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"店铺销售额排名\",\"query\":[],\"h\":42,\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=salesRanking\",\"drillData\":[],\"timeOut\":-1,\"chartData\":\"[{\\\"name\\\":\\\"4月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"2月\\\",\\\"value\\\":200},{\\\"name\\\":\\\"3月\\\",\\\"value\\\":300},{\\\"name\\\":\\\"4月\\\",\\\"value\\\":400},{\\\"name\\\":\\\"5月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"6月\\\",\\\"value\\\":120}]\",\"size\":{\"width\":447,\"height\":353},\"dataSetId\":\"911837091507781632\",\"fieldOption\":[{\"label\":\"店铺\",\"text\":\"店铺\",\"value\":\"name\"},{\"label\":\"销售金额（元）\",\"text\":\"销售金额（元）\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"seriesType\":[],\"w\":24,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"columns\":[{\"izShow\":\"Y\",\"dataIndex\":\"name\",\"title\":\"店铺\"},{\"izShow\":\"Y\",\"dataIndex\":\"value\",\"title\":\"销售金额（元）\"}],\"title\":{\"textStyle\":{\"color\":\"#464646\"}}}}', 'admin', '2024-01-29 15:51:27', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('911854597085642752', NULL, '910820508471705600', NULL, 'JRing', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\" 冷热占比\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=coldAndHostProportion\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":371,\"height\":353},\"dataSetId\":\"911837168896884736\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"avoidLabelOverlap\":false,\"emphasis\":{\"label\":{\"show\":true,\"fontSize\":\"25\",\"fontWeight\":\"bold\"}},\"label\":{\"show\":false,\"position\":\"center\"},\"labelLine\":{\"show\":false},\"type\":\"pie\",\"radius\":[\"40%\",\"70%\"]}],\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"top\":1,\"show\":true,\"text\":\"冷热占比\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-29 15:51:27', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911773848785436672', '某连锁饮品销售', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-29 10:30:35', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911792028287287296', '销量额', '', '911773848785436672', '', 'https://apijeecgcom/mock/51/beverageSales?type=salesVolume', '', '0', 'api', 'get', '2024-01-29 11:42:50', 'admin', '2024-01-29 11:46:35', 'admin', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911836343134896128', '单月最高销售量分店	', '', '911773848785436672', '', 'https://apijeecgcom/mock/51/beverageSales?type=topSellingBranch', '', '0', 'api', 'get', '2024-01-29 14:38:55', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911836426450550784', '单月最高销量品线	', '', '911773848785436672', '', 'https://apijeecgcom/mock/51/beverageSales?type=salesLine', '', '0', 'api', 'get', '2024-01-29 14:39:15', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911836535531814912', '单月最高销量单品', '', '911773848785436672', '', 'https://apijeecgcom/mock/51/beverageSales?type=highestSellingItem', '', '0', 'api', 'get', '2024-01-29 14:39:41', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911836717715603456', '单月最高销量规格', '', '911773848785436672', '', 'https://apijeecgcom/mock/51/beverageSales?type=maximumSalesSpecs', '', '0', 'api', 'get', '2024-01-29 14:40:24', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911836839023263744', '销售额走势', '', '911773848785436672', '', 'https://apijeecgcom/mock/51/beverageSales?type=salesTrend', '', '0', 'api', 'get', '2024-01-29 14:40:53', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911836922162757632', '销量走势', '', '911773848785436672', '', 'https://apijeecgcom/mock/51/beverageSales?type=salesTendency', '', '0', 'api', 'get', '2024-01-29 14:41:13', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911837008343121920', '订单销售量	', '', '911773848785436672', '', 'https://apijeecgcom/mock/51/beverageSales?type=orderSalesVolume', '', '0', 'api', 'get', '2024-01-29 14:41:34', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911837091507781632', '店铺销售额排名', '', '911773848785436672', '', 'https://apijeecgcom/mock/51/beverageSales?type=salesRanking', '', '0', 'api', 'get', '2024-01-29 14:41:53', 'admin', '2024-01-29 15:44:55', 'admin', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911837168896884736', ' 冷热占比', '', '911773848785436672', '', 'https://apijeecgcom/mock/51/beverageSales?type=coldAndHostProportion', '', '0', 'api', 'get', '2024-01-29 14:42:12', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911845934736392192', '原料支出趋势', '', '911773848785436672', '', 'https://apijeecgcom/mock/51/beverageSales?type=expenditureTrends', '', '0', 'api', 'get', '2024-01-29 15:17:02', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911792972810993664', '911792028287287296', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 11:42:50', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911792972852936704', '911792028287287296', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 11:42:50', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911792972882296832', '911792028287287296', 'icon', 'icon', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'admin', '2024-01-29 11:42:50', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911792972911656960', '911792028287287296', 'action', 'action', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'admin', '2024-01-29 11:42:50', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911792972949405696', '911792028287287296', 'color', 'color', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 4, 'admin', '2024-01-29 11:46:35', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911836343176839168', '911836343134896128', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 14:38:55', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911836343218782208', '911836343134896128', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 14:38:55', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911836426496688128', '911836426450550784', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 14:39:15', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911836426538631168', '911836426450550784', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 14:39:15', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911836535569563648', '911836535531814912', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 14:39:41', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911836535603118080', '911836535531814912', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 14:39:41', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911836717761740800', '911836717715603456', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 14:40:24', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911836717803683840', '911836717715603456', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 14:40:24', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911836839392362496', '911836839023263744', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 14:40:53', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911836839736295424', '911836839023263744', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 14:40:53', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911836922204700672', '911836922162757632', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 14:41:13', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911836922238255104', '911836922162757632', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 14:41:13', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911837008376676352', '911837008343121920', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 14:41:34', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911837008414425088', '911837008343121920', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 14:41:34', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911852950460612608', '911837091507781632', 'name', '店铺', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 14:41:53', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911852950506749952', '911837091507781632', 'value', '销售金额（元）', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 14:41:53', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911837169203068928', '911837168896884736', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 14:42:12', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911837169526030336', '911837168896884736', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 14:42:12', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911845934769946624', '911845934736392192', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 15:17:02', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911845934807695360', '911845934736392192', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 15:17:02', NULL, NULL);

-- ---author:wangshuai---date:2024/1/30-----for:【QQYUN-7963】模版配置：产品销售数据
INSERT INTO onl_drag_page (id, name, path, background_color, background_image, design_type, theme, style, cover_url, template, protection_code, type, iz_template, create_by, create_time, update_by, update_time, low_app_id, tenant_id, update_count, visits_num) VALUES ('911856216581914624', '产品销售数据', '/drag/page/view/911856216581914624', NULL, NULL, 100, 'default', 'default', NULL, '[{\"component\":\"JText\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":0,\"x\":0,\"h\":8,\"i\":\"8faf5811-d619-4ff2-839e-86ecb4e0328f\",\"y\":0,\"orderNum\":0,\"pageCompId\":\"912194458363674624\"},{\"component\":\"JRing\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":8,\"x\":0,\"h\":30,\"i\":\"b5fa4269-1d38-4a62-80a3-52490943ff59\",\"y\":8,\"orderNum\":38,\"pageCompId\":\"912194458397229056\"},{\"component\":\"JBar\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":38,\"x\":0,\"h\":41,\"i\":\"de23faa0-ffa7-482c-857e-8dbfaea7dd47\",\"y\":38,\"orderNum\":38,\"pageCompId\":\"912194458418200576\"},{\"component\":\"JText\",\"pcX\":5,\"w\":4,\"moved\":false,\"pcY\":8,\"x\":5,\"h\":5,\"i\":\"f2158c50-22b0-4978-98b9-6e2cc784650e\",\"y\":8,\"orderNum\":84,\"pageCompId\":\"912194458439172096\"},{\"component\":\"JText\",\"pcX\":5,\"w\":4,\"moved\":false,\"pcY\":13,\"x\":5,\"h\":5,\"i\":\"a28c1804-cb6b-4c70-8e57-ce352ff09d63\",\"y\":13,\"orderNum\":84,\"pageCompId\":\"912194458464337920\"},{\"component\":\"JText\",\"pcX\":5,\"w\":4,\"moved\":false,\"pcY\":18,\"x\":5,\"h\":5,\"i\":\"63506dd7-e376-42c5-b39f-b2211ad8ebdc\",\"y\":18,\"orderNum\":84,\"pageCompId\":\"912194458489503744\"},{\"component\":\"JText\",\"pcX\":9,\"w\":4,\"moved\":false,\"pcY\":8,\"x\":9,\"h\":5,\"i\":\"de7e9a36-1234-4792-835a-c20687c433ed\",\"y\":8,\"orderNum\":84,\"pageCompId\":\"912194458514669568\"},{\"component\":\"JText\",\"pcX\":9,\"w\":4,\"moved\":false,\"pcY\":13,\"x\":9,\"h\":5,\"i\":\"ee6464c3-bda1-424a-8c0f-37fdfce6d8cb\",\"y\":13,\"orderNum\":84,\"pageCompId\":\"912194458535641088\"},{\"component\":\"JText\",\"pcX\":9,\"w\":4,\"moved\":false,\"pcY\":18,\"x\":9,\"h\":5,\"i\":\"c6de77fa-b17a-4549-bda8-0e00e65a7748\",\"y\":18,\"orderNum\":84,\"pageCompId\":\"912194458556612608\"},{\"component\":\"JText\",\"pcX\":13,\"w\":4,\"moved\":false,\"pcY\":8,\"x\":13,\"h\":5,\"i\":\"3dc483bb-0067-48ee-98bd-dfa2e7534909\",\"y\":8,\"orderNum\":84,\"pageCompId\":\"912194458577584128\"},{\"component\":\"JText\",\"pcX\":13,\"w\":4,\"moved\":false,\"pcY\":13,\"x\":13,\"h\":10,\"i\":\"ecd623a6-2e08-47f2-8dd6-c25ff5c29a66\",\"y\":13,\"orderNum\":84,\"pageCompId\":\"912194458602749952\"},{\"component\":\"JLine\",\"pcX\":5,\"w\":12,\"moved\":false,\"pcY\":23,\"x\":5,\"h\":27,\"i\":\"b910f04d-6a4d-4d79-811d-dc5d2f807713\",\"y\":23,\"orderNum\":83,\"pageCompId\":\"912194458665664512\"},{\"component\":\"JStackBar\",\"pcX\":5,\"w\":12,\"moved\":false,\"pcY\":50,\"x\":5,\"h\":29,\"i\":\"406be0c9-dc13-4cad-ac22-17a4a5c22bff\",\"y\":50,\"orderNum\":83,\"pageCompId\":\"912194458686636032\"},{\"component\":\"JRing\",\"pcX\":17,\"w\":7,\"moved\":false,\"pcY\":8,\"x\":17,\"h\":32,\"i\":\"76d16482-fd7f-4e92-ba27-68980b8b0029\",\"y\":8,\"orderNum\":83,\"pageCompId\":\"912194458707607552\"},{\"component\":\"JText\",\"pcX\":17,\"w\":7,\"moved\":false,\"pcY\":40,\"x\":17,\"h\":5,\"i\":\"211ebf49-1284-4b1c-99c9-08066a50f9ae\",\"y\":40,\"orderNum\":82,\"pageCompId\":\"912194458736967680\"},{\"component\":\"JBar\",\"pcX\":17,\"w\":7,\"moved\":false,\"pcY\":45,\"x\":17,\"h\":34,\"i\":\"c89556db-678a-418b-8121-1052958f4149\",\"y\":45,\"orderNum\":115,\"pageCompId\":\"912194458757939200\"}]', NULL, '1', '1', 'admin', '2024-01-29 15:57:53', 'admin', '2024-01-30 14:21:56', NULL, 6902, 24, 0);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458363674624', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"产品销售数据仪表板\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":1817,\"height\":78},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4570F2\",\"textAlign\":\"center\",\"fontSize\":36,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":8,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458397229056', NULL, '911856216581914624', NULL, 'JRing', '{\"borderColor\":\"#DBEAFF\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"本月渠道销售\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=salesThisMonth\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":371,\"height\":320},\"dataSetId\":\"911887596497977344\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"top\":54,\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"avoidLabelOverlap\":false,\"emphasis\":{\"label\":{\"show\":true,\"fontSize\":\"25\",\"fontWeight\":\"bold\"}},\"label\":{\"show\":false,\"position\":\"center\"},\"labelLine\":{\"show\":false},\"type\":\"pie\",\"radius\":[\"40%\",\"70%\"]}],\"tooltip\":{\"trigger\":\"item\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\" 华东区、华中区为主要渠道销售区域。\",\"top\":1,\"show\":true,\"text\":\"本月渠道销售\",\"subtextStyle\":{\"color\":\"#464646\"},\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458418200576', NULL, '911856216581914624', NULL, 'JBar', '{\"borderColor\":\"#DBEAFF\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"本月代理商销售排行\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=salesRanking\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":371,\"height\":441},\"dataSetId\":\"911887691259887616\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"interval\":2}},\"xAxis\":{\"axisLabel\":{\"rotate\":3,\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"type\":\"value\",\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":69,\"left\":94,\"bottom\":58,\"show\":false},\"series\":[{\"barWidth\":17,\"data\":[],\"itemStyle\":{\"color\":\"#64B5F6\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"show\":true,\"text\":\"本月代理商销售排行/元\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458439172096', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"本月销售额/元\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#000000\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":21},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458464337920', NULL, '911856216581914624', NULL, 'JText', '{\"borderColor\":\"#DBEAFF\",\"paramOption\":[],\"dataType\":1,\"dataSetName\":\"本月产品销售\",\"query\":[],\"h\":12,\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=productSalesThisMonth\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"chartData\":\"15,458,757\",\"size\":{\"width\":295,\"height\":45},\"dataSetId\":\"911889006916583424\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"w\":8,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"body\":{\"color\":\"#4D71FE\",\"fontSize\":30,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":0,\"marginLeft\":16},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458489503744', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"月环比 -50%\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#34C874\",\"fontSize\":14,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":21},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458514669568', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"今日销售额/元\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#000000\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":18},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458535641088', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"657,554\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4D71FE\",\"fontSize\":30,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":0,\"marginLeft\":19},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458556612608', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"日环比 -12%\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#34C874\",\"fontSize\":14,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":24},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458577584128', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"今年目标销售额达成率\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#000000\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":16},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458602749952', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"42%\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":100},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4D71FE\",\"textAlign\":\"center\",\"fontSize\":30,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":24,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458665664512', NULL, '911856216581914624', NULL, 'JLine', '{\"borderColor\":\"#DBEAFF\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"近七天销售额趋势\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=salesTrend\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1000,\\\"name\\\":\\\"联想\\\"},{\\\"value\\\":7350,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":5800,\\\"name\\\":\\\"华为\\\"},{\\\"value\\\":6000,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":3000,\\\"name\\\":\\\"戴尔\\\"}]\",\"size\":{\"width\":904,\"height\":287},\"dataSetId\":\"911888632709169152\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"top\":90,\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"itemStyle\":{\"color\":\"#64b5f6\"},\"type\":\"line\"}],\"title\":{\"subtext\":\"日销售额（元）\",\"left\":10,\"text\":\"近七天销售额趋势\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\",\"fontWeight\":\"bold\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458686636032', NULL, '911856216581914624', NULL, 'JStackBar', '{\"borderColor\":\"#DBEAFF\",\"dataMapping\":[{\"mapping\":\"type\",\"filed\":\"分组\"},{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"本月重点城市的重点产品销售额\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=productSalesRevenue\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"1991\\\",\\\"value\\\":3,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1992\\\",\\\"value\\\":4,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1993\\\",\\\"value\\\":3.5,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1994\\\",\\\"value\\\":5,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1995\\\",\\\"value\\\":4.9,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1996\\\",\\\"value\\\":6,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1997\\\",\\\"value\\\":7,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1998\\\",\\\"value\\\":9,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1999\\\",\\\"value\\\":13,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1991\\\",\\\"value\\\":3,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1992\\\",\\\"value\\\":4,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1993\\\",\\\"value\\\":3.5,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1994\\\",\\\"value\\\":5,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1995\\\",\\\"value\\\":4.9,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1996\\\",\\\"value\\\":6,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1997\\\",\\\"value\\\":7,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1998\\\",\\\"value\\\":9,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1999\\\",\\\"value\\\":13,\\\"type\\\":\\\"Bor\\\"}]\",\"size\":{\"width\":904,\"height\":309},\"dataSetId\":\"911888715248877568\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"},{\"label\":\"type\",\"text\":\"type\",\"value\":\"type\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"customColor\":[{\"color\":\"#67BBFF\"},{\"color\":\"#9982FD\"},{\"color\":\"#62DDCC\"}],\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":90,\"bottom\":115},\"series\":[],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"show\":true,\"text\":\"本月重点城市的重点产品销售额/元\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":16}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458707607552', NULL, '911856216581914624', NULL, 'JRing', '{\"borderColor\":\"#DBEAFF\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"本月产品销售\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=productSalesThisMonth\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":523,\"height\":342},\"dataSetId\":\"911889006916583424\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"top\":55,\"left\":47,\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"avoidLabelOverlap\":false,\"emphasis\":{\"label\":{\"show\":true,\"fontSize\":\"25\",\"fontWeight\":\"bold\"}},\"itemStyle\":{\"color\":\"#64B5F6\"},\"label\":{\"show\":false,\"position\":\"center\"},\"labelLine\":{\"show\":false},\"type\":\"pie\",\"radius\":[\"40%\",\"70%\"]}],\"legend\":{\"t\":10},\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"subtext\":\"\",\"show\":true,\"text\":\" 产品3为主推产品产品2为近期上线产品。\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":14,\"fontWeight\":\"bold\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458736967680', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"本月产品销售\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":523,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#000000\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":8,\"marginLeft\":21},\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912194458757939200', NULL, '911856216581914624', NULL, 'JBar', '{\"borderColor\":\"#DBEAFF\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"本月产品小类销售排行\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=rankingCategorySales\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":523,\"height\":364},\"dataSetId\":\"911889086163763200\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"interval\":2}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"type\":\"value\",\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":44,\"bottom\":62,\"show\":false},\"series\":[{\"barWidth\":16,\"data\":[],\"itemStyle\":{\"color\":\"#64B5F6\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"subtext\":\"\",\"show\":true,\"text\":\" 本月产品小类销售排行/元\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":14},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-30 14:21:56', NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911878648998969344', '产品销售数据', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-29 17:27:02', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911887596497977344', '本月渠道销售', '', '911878648998969344', '', 'https://api.jeecg.com/mock/51/productSales?type=salesThisMonth', '', '0', 'api', 'get', '2024-01-29 18:02:35', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911887691259887616', '本月代理商销售排行', '', '911878648998969344', '', 'https://api.jeecg.com/mock/51/productSales?type=salesRanking', '', '0', 'api', 'get', '2024-01-29 18:02:57', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911888632709169152', '近七天销售额趋势', '', '911878648998969344', '', 'https://api.jeecg.com/mock/51/productSales?type=salesTrend', '', '0', 'api', 'get', '2024-01-29 18:06:42', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911888715248877568', '本月重点城市的重点产品销售额', '', '911878648998969344', '', 'https://api.jeecg.com/mock/51/productSales?type=productSalesRevenue', '', '0', 'api', 'get', '2024-01-29 18:07:02', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911888877589413888', '本月重点城市的重点产品销售额（表格）', '', '911878648998969344', '', 'https://api.jeecg.com/mock/51/productSales?type=productSalesRevenueTable', '', '0', 'api', 'get', '2024-01-29 18:07:40', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911889006916583424', '本月产品销售', '', '911878648998969344', '', 'https://api.jeecg.com/mock/51/productSales?type=productSalesThisMonth', '', '0', 'api', 'get', '2024-01-29 18:08:11', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_head (id, name, code, parent_id, db_source, query_sql, content, iz_agent, data_type, api_method, create_time, create_by, update_time, update_by, low_app_id, tenant_id) VALUES ('911889086163763200', '本月产品小类销售排行', '', '911878648998969344', '', 'https://api.jeecg.com/mock/51/productSales?type=rankingCategorySales', '', '0', 'api', 'get', '2024-01-29 18:08:30', 'admin', NULL, NULL, NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911887596531531776', '911887596497977344', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 18:02:35', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911887596565086208', '911887596497977344', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 18:02:35', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911887691293442048', '911887691259887616', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 18:02:57', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911887691331190784', '911887691259887616', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 18:02:57', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911888632742723584', '911888632709169152', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 18:06:42', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911888632767889408', '911888632709169152', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 18:06:42', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911888715282432000', '911888715248877568', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 18:07:02', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911888715320180736', '911888715248877568', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 18:07:02', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911888715345346560', '911888715248877568', 'type', 'type', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'admin', '2024-01-29 18:07:02', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911888877618774016', '911888877589413888', 'city', '城市', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 18:07:40', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911888877643939840', '911888877589413888', 'rate', '月环比', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 18:07:40', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911889006950137856', '911889006916583424', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 18:08:11', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911889006987886592', '911889006916583424', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 18:08:11', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911889086197317632', '911889086163763200', 'name', 'name', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'admin', '2024-01-29 18:08:30', NULL, NULL);
INSERT INTO onl_drag_dataset_item (id, head_id, field_name, field_txt, field_type, widget_type, dict_code, iz_show, iz_search, iz_total, search_mode, order_num, create_by, create_time, update_by, update_time) VALUES ('911889086230872064', '911889086163763200', 'value', 'value', 'String', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'admin', '2024-01-29 18:08:30', NULL, NULL);

ALTER TABLE jimu_report_share
    ADD COLUMN share_token varchar(50) NULL COMMENT '分享token' AFTER preview_lock_status,
ADD UNIQUE INDEX uniq_jrs_share_token(share_token);

-- 新增py支持方案，支持用户自定义脚本，合并接口处理数据等
ALTER TABLE `jimu_report`
    ADD COLUMN `py_str` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT 'py增强' AFTER `js_str`;

-- 补充登录接口查询索引---
ALTER TABLE `sys_user`
    ADD INDEX `idx_su_del_username`(`username`, `del_flag`);

ALTER TABLE `sys_user_tenant`
    ADD INDEX `idx_sut_status`(`status`),
    ADD INDEX `idx_sut_userid_status`(`user_id`, `status`);

ALTER TABLE `sys_role_index`
    ADD INDEX `idx_sri_role_code`(`role_code`),
    ADD INDEX `idx_sri_status`(`status`),
    ADD INDEX `idx_sri_priority`(`priority`);

ALTER TABLE `sys_dict`
    ADD INDEX `uk_sd_tenant_id`(`tenant_id`);

ALTER TABLE `sys_permission`
    ADD INDEX `index_menu_del_flag`(`del_flag`),
    ADD INDEX `index_menu_url`(`url`),
    ADD INDEX `index_menu_sort_no`(`sort_no`);

-- 仪表盘快捷导航配置项组件更新
UPDATE `onl_drag_comp` SET `parent_id` = '100104', `comp_name` = '快捷导航', `comp_type` = 'JQuickNav', `icon` = 'ion:navigate', `order_num` = 6, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 26,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/42/nav\",\n  \"timeOut\": -1,\n  \"linkageConfig\": [],\r\n	\"dataMapping\": [\r\n    {\r\n      \"filed\": \"标题\",\r\n      \"mapping\": \"\"\r\n    },\r\n    {\r\n      \"filed\": \"图标\",\r\n      \"mapping\": \"\"\r\n    },\r\n    {\r\n      \"filed\": \"颜色\",\r\n      \"mapping\": \"\"\r\n    },\r\n    {\r\n      \"filed\": \"跳转\",\r\n      \"mapping\": \"\"\r\n    }\r\n  ],\n  \"chartData\": [\n    {\n      \"title\": \"首页\",\n      \"icon\": \"icon-jeecg-homepage\",\n      \"color\": \"#1fdaca\"\n    },\n    {\n      \"title\": \"仪表盘\",\n      \"icon\": \"icon-jeecg-shijian\",\n      \"color\": \"#bf0c2c\"\n    },\n    {\n      \"title\": \"组件\",\n      \"icon\": \"icon-jeecg-dangan\",\n      \"color\": \"#e18525\"\n    },\n    {\n      \"title\": \"系统管理\",\n      \"icon\": \"icon-jeecg-shezhi\",\n      \"color\": \"#3fb27f\"\n    },\n    {\n      \"title\": \"权限管理\",\n      \"icon\": \"icon-jeecg-yuechi\",\n      \"color\": \"#4daf1bc9\"\n    },\n    {\n      \"title\": \"图表\",\n      \"icon\": \"icon-jeecg-fujin\",\n      \"color\": \"#00d8ff\"\n    }\n  ],\n  \"option\": {\n    \"icon\": {\n      \"scriptUrl\": \"//at.alicdn.com/t/font_3237315_b3fqd960glt.js\",\n      \"fontSize\": 30\n    },\n    \"card\": {\n      \"title\": \"快捷导航\",\n      \"extra\": \"更多\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"body\": {\n      \"column\": 3,\n      \"textAlign\": \"center\",\n      \"iconAlign\": \"top\"\n    }\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 19:50:38' WHERE `id` = '100103';

-- 补充角色表单租户查询索引
ALTER TABLE `sys_role`
    ADD INDEX `idx_sysrole_tenant_id`(`tenant_id`);

-- 横向动态合并 只支持最后合计，不支持前边合计
delete from `jimu_report` where id = '919370186342354944';
delete from `jimu_report_db` where jimu_report_id = '919370186342354944';
INSERT INTO `jimu_report` (`id`, `code`, `name`, `note`, `status`, `type`, `json_str`, `api_url`, `thumb`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `api_method`, `api_code`, `template`, `view_count`, `css_str`, `js_str`, `py_str`, `tenant_id`) VALUES ('919370186342354944', '20240103104736__3061', '横向总合计-横纵3*2*2副本3061', NULL, NULL, 'datainfo', '{\"loopBlockList\":[],\"printConfig\":{\"layout\":\"landscape\",\"paginationShow\":false,\"printCallBackUrl\":\"\",\"paper\":\"A4\",\"isBackend\":false,\"width\":210,\"paginationLocation\":\"middle\",\"definition\":1,\"marginX\":10,\"height\":297,\"marginY\":10},\"dbexps\":[],\"dicts\":[],\"freeze\":\"A1\",\"dataRectWidth\":1071,\"autofilter\":{},\"validations\":[],\"cols\":{\"0\":{\"width\":60},\"1\":{\"width\":96},\"2\":{\"width\":115},\"len\":100},\"area\":{\"sri\":6,\"sci\":6,\"eri\":6,\"eci\":10,\"width\":500,\"height\":25},\"excel_config_id\":\"919370186342354944\",\"hiddenCells\":[],\"zonedEditionList\":[],\"rows\":{\"0\":{\"cells\":{\"0\":{},\"4\":{},\"5\":{}}},\"1\":{\"cells\":{\"0\":{},\"1\":{\"merge\":[3,1],\"height\":100,\"text\":\"地区|销售额|时间\",\"lineStart\":\"lefttop\",\"style\":0},\"3\":{\"merge\":[3,0],\"style\":0,\"text\":\"合计(销售）\",\"height\":100},\"4\":{\"merge\":[0,1],\"style\":1,\"text\":\"#{test_heng_sum.groupRight(year)}\",\"aggregate\":\"group\",\"direction\":\"right\",\"height\":25},\"5\":{},\"6\":{\"merge\":[3,0],\"style\":0,\"text\":\"合计(销售）\",\"height\":100},\"7\":{\"merge\":[3,0],\"style\":0,\"text\":\"合计（赠送）\",\"height\":100},\"8\":{\"merge\":[3,0],\"style\":1,\"text\":\"平均（销售）\",\"height\":100},\"9\":{\"merge\":[3,0],\"style\":1,\"text\":\"最大（销售）\",\"height\":100},\"10\":{\"merge\":[3,0],\"style\":1,\"text\":\"最小（销售）\",\"height\":100}}},\"2\":{\"cells\":{\"0\":{},\"4\":{\"merge\":[0,1],\"style\":1,\"text\":\"#{test_heng_sum.groupRight(quarter)}\",\"height\":25,\"aggregate\":\"group\",\"direction\":\"right\"},\"5\":{},\"8\":{},\"9\":{},\"10\":{}}},\"3\":{\"cells\":{\"0\":{},\"4\":{\"merge\":[0,1],\"style\":1,\"text\":\"#{test_heng_sum.groupRight(month)}\",\"height\":25,\"aggregate\":\"group\",\"direction\":\"right\"},\"5\":{},\"8\":{},\"9\":{},\"10\":{}}},\"4\":{\"cells\":{\"0\":{},\"4\":{\"style\":1,\"text\":\"销售\"},\"5\":{\"style\":1,\"text\":\"赠送\"},\"8\":{},\"9\":{},\"10\":{}}},\"5\":{\"cells\":{\"0\":{},\"1\":{\"style\":1,\"text\":\"#{test_heng_sum.group(region)}\",\"aggregate\":\"group\"},\"2\":{\"style\":1,\"text\":\"#{test_heng_sum.group(province)}\",\"aggregate\":\"group\"},\"3\":{\"style\":0,\"text\":\"=SUM(E6)\"},\"4\":{\"style\":0,\"text\":\"#{test_heng_sum.dynamic(sales)}\",\"aggregate\":\"dynamic\"},\"5\":{\"style\":0,\"text\":\"#{test_heng_sum.dynamic(gift)}\",\"aggregate\":\"dynamic\"},\"6\":{\"style\":0,\"text\":\"=SUM(E6)\"},\"7\":{\"style\":0,\"text\":\"=SUM(F6)\"},\"8\":{\"style\":0,\"text\":\"=AVERAGE(E6)\"},\"9\":{\"style\":0,\"text\":\"=MAX(F6)\"},\"10\":{\"style\":0,\"text\":\"=MIN(F6)\"}}},\"6\":{\"cells\":{\"0\":{},\"1\":{\"merge\":[0,1],\"style\":1,\"text\":\"合计\",\"height\":25},\"2\":{},\"3\":{\"style\":0},\"4\":{\"style\":0,\"text\":\"=SUM(E6)\"},\"5\":{\"style\":0,\"text\":\"=SUM(F6)\"},\"6\":{\"style\":0},\"7\":{\"style\":0},\"8\":{\"style\":0},\"9\":{\"style\":0},\"10\":{\"style\":0}}},\"7\":{\"cells\":{\"0\":{},\"3\":{},\"4\":{},\"5\":{},\"6\":{},\"7\":{},\"8\":{},\"9\":{},\"10\":{},\"11\":{},\"12\":{},\"13\":{},\"14\":{},\"15\":{},\"16\":{},\"17\":{},\"18\":{},\"19\":{},\"20\":{},\"21\":{},\"22\":{},\"23\":{},\"24\":{},\"25\":{},\"26\":{},\"27\":{},\"28\":{},\"29\":{}}},\"8\":{\"cells\":{\"0\":{}}},\"len\":167},\"rpbar\":{\"show\":true,\"pageSize\":\"\",\"btnList\":[]},\"groupField\":\"test_heng_sum.region\",\"fixedPrintHeadRows\":[],\"fixedPrintTailRows\":[],\"displayConfig\":{},\"background\":false,\"name\":\"sheet1\",\"styles\":[{\"border\":{\"top\":[\"thin\",\"#000\"],\"left\":[\"thin\",\"#000\"],\"bottom\":[\"thin\",\"#000\"],\"right\":[\"thin\",\"#000\"]}},{\"border\":{\"top\":[\"thin\",\"#000\"],\"left\":[\"thin\",\"#000\"],\"bottom\":[\"thin\",\"#000\"],\"right\":[\"thin\",\"#000\"]},\"align\":\"center\"},{\"align\":\"center\"},{\"bgcolor\":\"#9cc2e6\"},{\"border\":{\"top\":[\"thin\",\"#000\"],\"left\":[\"thin\",\"#000\"],\"bottom\":[\"thin\",\"#000\"],\"right\":[\"thin\",\"#000\"]},\"bgcolor\":\"#9cc2e6\"}],\"isGroup\":true,\"merges\":[\"D2:D5\",\"E2:F2\",\"G2:G5\",\"H2:H5\",\"I2:I5\",\"J2:J5\",\"K2:K5\",\"E3:F3\",\"E4:F4\",\"B7:C7\",\"B2:C5\"]}', NULL, NULL, 'admin', '2024-02-19 09:35:44', 'admin', '2024-02-22 09:38:08', 0, NULL, NULL, 0, 42, NULL, NULL, NULL, NULL);
INSERT INTO `jimu_report_db` (`id`, `jimu_report_id`, `create_by`, `update_by`, `create_time`, `update_time`, `db_code`, `db_ch_name`, `db_type`, `db_table_name`, `db_dyn_sql`, `db_key`, `tb_db_key`, `tb_db_table_name`, `java_type`, `java_value`, `api_url`, `api_method`, `is_list`, `is_page`, `db_source`, `db_source_type`, `json_data`, `api_convert`) VALUES ('919370187038609408', '919370186342354944', 'admin', 'admin', '2024-01-04 17:46:20', '2024-01-04 17:46:20', 'test_heng_sum', 'test_heng_sum', '3', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', '0', '1', '1', '', NULL, '{\n    \"data\": [\n        {\"region\":\"华东\",\"province\":\"上海市\",\"year\":\"2022\",\"quarter\":\"1\",\"month\":\"1月\",\"sales\":\"2000\",\"gift\":\"1000\",\"proportion\":\"10%\"},\n        {\"region\":\"华东\",\"province\":\"上海市\",\"year\":\"2022\",\"quarter\":\"1\",\"month\":\"2月\",\"sales\":\"2000\",\"gift\":\"1000\",\"proportion\":\"10%\"},\n        {\"region\":\"华东\",\"province\":\"上海市\",\"year\":\"2022\",\"quarter\":\"1\",\"month\":\"3月\",\"sales\":\"2000\",\"gift\":\"1000\",\"proportion\":\"10%\"},\n        {\"region\":\"华东\",\"province\":\"上海市\",\"year\":\"2022\",\"quarter\":\"2\",\"month\":\"1月\",\"sales\":\"3000\",\"gift\":\"2000\",\"proportion\":\"10%\"},\n        {\"region\":\"华东\",\"province\":\"上海市\",\"year\":\"2022\",\"quarter\":\"2\",\"month\":\"2月\",\"sales\":\"3000\",\"gift\":\"2000\",\"proportion\":\"10%\"},\n        {\"region\":\"华东\",\"province\":\"上海市\",\"year\":\"2022\",\"quarter\":\"2\",\"month\":\"3月\",\"sales\":\"3000\",\"gift\":\"2000\",\"proportion\":\"10%\"},\n        {\"region\":\"华西\",\"province\":\"太原市\",\"year\":\"2022\",\"quarter\":\"1\",\"month\":\"1月\",\"sales\":\"2000\",\"gift\":\"1000\",\"proportion\":\"10%\"},\n        {\"region\":\"华西\",\"province\":\"太原市\",\"year\":\"2022\",\"quarter\":\"1\",\"month\":\"2月\",\"sales\":\"2000\",\"gift\":\"1000\",\"proportion\":\"10%\"},\n        {\"region\":\"华西\",\"province\":\"太原市\",\"year\":\"2022\",\"quarter\":\"1\",\"month\":\"3月\",\"sales\":\"2000\",\"gift\":\"1000\",\"proportion\":\"10%\"},\n        {\"region\":\"华西\",\"province\":\"太原市\",\"year\":\"2022\",\"quarter\":\"2\",\"month\":\"1月\",\"sales\":\"3000\",\"gift\":\"2000\",\"proportion\":\"10%\"},\n        {\"region\":\"华西\",\"province\":\"太原市\",\"year\":\"2022\",\"quarter\":\"2\",\"month\":\"2月\",\"sales\":\"3000\",\"gift\":\"2000\",\"proportion\":\"10%\"},\n        {\"region\":\"华西\",\"province\":\"太原市\",\"year\":\"2022\",\"quarter\":\"2\",\"month\":\"3月\",\"sales\":\"3000\",\"gift\":\"2000\",\"proportion\":\"10%\"},\n        {\"region\":\"华东\",\"province\":\"上海市\",\"year\":\"2023\",\"quarter\":\"1\",\"month\":\"1月\",\"sales\":\"2000\",\"gift\":\"1000\",\"proportion\":\"10%\"},\n        {\"region\":\"华东\",\"province\":\"上海市\",\"year\":\"2023\",\"quarter\":\"1\",\"month\":\"2月\",\"sales\":\"2000\",\"gift\":\"1000\",\"proportion\":\"10%\"},\n        {\"region\":\"华东\",\"province\":\"上海市\",\"year\":\"2023\",\"quarter\":\"1\",\"month\":\"3月\",\"sales\":\"2000\",\"gift\":\"1000\",\"proportion\":\"10%\"},\n        {\"region\":\"华东\",\"province\":\"上海市\",\"year\":\"2023\",\"quarter\":\"2\",\"month\":\"1月\",\"sales\":\"3000\",\"gift\":\"2000\",\"proportion\":\"10%\"},\n        {\"region\":\"华东\",\"province\":\"上海市\",\"year\":\"2023\",\"quarter\":\"2\",\"month\":\"2月\",\"sales\":\"3000\",\"gift\":\"2000\",\"proportion\":\"10%\"},\n        {\"region\":\"华东\",\"province\":\"上海市\",\"year\":\"2023\",\"quarter\":\"2\",\"month\":\"3月\",\"sales\":\"3000\",\"gift\":\"2000\",\"proportion\":\"10%\"},\n        {\"region\":\"华西\",\"province\":\"太原市\",\"year\":\"2023\",\"quarter\":\"1\",\"month\":\"1月\",\"sales\":\"2000\",\"gift\":\"1000\",\"proportion\":\"10%\"},\n        {\"region\":\"华西\",\"province\":\"太原市\",\"year\":\"2023\",\"quarter\":\"1\",\"month\":\"2月\",\"sales\":\"2000\",\"gift\":\"1000\",\"proportion\":\"10%\"},\n        {\"region\":\"华西\",\"province\":\"太原市\",\"year\":\"2023\",\"quarter\":\"1\",\"month\":\"3月\",\"sales\":\"2000\",\"gift\":\"1000\",\"proportion\":\"10%\"},\n        {\"region\":\"华西\",\"province\":\"太原市\",\"year\":\"2023\",\"quarter\":\"2\",\"month\":\"1月\",\"sales\":\"3000\",\"gift\":\"2000\",\"proportion\":\"10%\"},\n        {\"region\":\"华西\",\"province\":\"太原市\",\"year\":\"2023\",\"quarter\":\"2\",\"month\":\"2月\",\"sales\":\"3000\",\"gift\":\"2000\",\"proportion\":\"10%\"},\n        {\"region\":\"华西\",\"province\":\"太原市\",\"year\":\"2023\",\"quarter\":\"2\",\"month\":\"3月\",\"sales\":\"3000\",\"gift\":\"2000\",\"proportion\":\"10%\"}\n    ]\n}', '');

-- 补充获取权限的查询sql索引---
ALTER TABLE `sys_depart_role_user`
    ADD INDEX `idx_sdr_user_id`(`user_id`),
    ADD INDEX `idx_sdr_role_id`(`drole_id`);

ALTER TABLE `sys_tenant_pack_perms`
    ADD INDEX `idx_stpp_pack_id`(`pack_id`);

ALTER TABLE `sys_tenant_pack_user`
    ADD INDEX `idx_tpu_pack_id`(`pack_id`),
    ADD INDEX `idx_tpu_user_id`(`user_id`),
    ADD INDEX `idx_tpu_tenant_id`(`tenant_id`),
    ADD INDEX `idx_tpu_status`(`status`);

-- 更新用户表真实姓名字符集
-- 【QQYUN-8319】企业微信集成，同步用户信息时，特殊字符导致失败的问题 #5887
ALTER TABLE sys_user
    MODIFY COLUMN realname varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '真实姓名' AFTER username;

-- 分享表增加唯一索引
ALTER TABLE jimu_report_share
    ADD UNIQUE INDEX uniq_jrs_report_id(report_id) USING BTREE COMMENT '报表id唯一索引';

-- 模版配置：更新仪表盘
-- 库存管理可视化大屏
UPDATE onl_drag_page SET template = '[{\"component\":\"JText\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":0,\"x\":0,\"h\":8,\"i\":\"377bfc6b-26f1-4fb0-8fe1-0acbc39149e2\",\"y\":0,\"orderNum\":0,\"pageCompId\":\"912577433139986432\"},{\"component\":\"JGrowCard\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":8,\"x\":0,\"h\":31,\"i\":\"94fb7d28-1f93-4506-8310-f39f6ca8f356\",\"y\":8,\"orderNum\":10,\"pageCompId\":\"912577433169346560\"},{\"component\":\"JText\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":39,\"x\":0,\"h\":5,\"i\":\"79eb8d80-8720-4dac-b4ab-a1bd295d3fa5\",\"y\":39,\"orderNum\":65,\"pageCompId\":\"912577433190318080\"},{\"component\":\"JList\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":44,\"x\":0,\"h\":11,\"i\":\"3e12ea9a-04b8-4f1a-819a-1666be83bb2c\",\"y\":44,\"orderNum\":51,\"pageCompId\":\"912577433207095296\"},{\"component\":\"JText\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":55,\"x\":0,\"h\":5,\"i\":\"b1bc2b1b-4e38-4042-942d-50978e79236f\",\"y\":55,\"orderNum\":61,\"pageCompId\":\"912577433228066816\"},{\"component\":\"JList\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":60,\"x\":0,\"h\":11,\"i\":\"ee6f5621-53ef-4d0f-a6c9-fd33982b2be1\",\"y\":60,\"orderNum\":71,\"pageCompId\":\"912577433249038336\"},{\"component\":\"JText\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":71,\"x\":0,\"h\":5,\"i\":\"9d1b768d-17ac-461b-8597-41a969fd1589\",\"y\":71,\"orderNum\":82,\"pageCompId\":\"912577433274204160\"},{\"component\":\"JList\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":76,\"x\":0,\"h\":11,\"i\":\"380b67f4-ada6-47df-83a0-83b9c62fa435\",\"y\":76,\"orderNum\":92,\"pageCompId\":\"912577433295175680\"},{\"component\":\"JText\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":87,\"x\":0,\"h\":5,\"i\":\"0b6da57d-9804-4bfa-8ab8-54d690f8a09a\",\"y\":87,\"orderNum\":103,\"pageCompId\":\"912577433316147200\"},{\"component\":\"JList\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":92,\"x\":0,\"h\":11,\"i\":\"c2551fe6-5a8f-4d45-bb3a-aa86dd6b46cd\",\"y\":92,\"orderNum\":113,\"pageCompId\":\"912577433337118720\"},{\"component\":\"JBar\",\"pcX\":5,\"w\":10,\"moved\":false,\"pcY\":39,\"x\":5,\"h\":20,\"i\":\"acecbdec-0b6b-4744-aeed-3f969e7915b6\",\"y\":39,\"orderNum\":124,\"pageCompId\":\"912577433366478848\"},{\"component\":\"JBar\",\"pcX\":5,\"w\":10,\"moved\":false,\"pcY\":59,\"x\":5,\"h\":22,\"i\":\"a3420701-faac-4d38-b7e2-9c12c3dc45e2\",\"y\":59,\"orderNum\":124,\"pageCompId\":\"912577433387450368\"},{\"component\":\"JBar\",\"pcX\":15,\"w\":9,\"moved\":false,\"pcY\":59,\"x\":15,\"h\":22,\"i\":\"a100e000-aee6-4138-8c72-0e4a830ec8de\",\"y\":59,\"orderNum\":124,\"pageCompId\":\"912577433408421888\"},{\"component\":\"JMultipleBar\",\"pcX\":15,\"w\":9,\"moved\":false,\"pcY\":81,\"x\":15,\"h\":22,\"i\":\"7d4ec916-020b-4f35-baff-87951bf2aa48\",\"y\":81,\"orderNum\":124,\"pageCompId\":\"912577433429393408\"},{\"component\":\"JCommonTable\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":110,\"x\":0,\"h\":45,\"i\":\"49592c96-4da5-4afb-9c15-fa7bea72bbc1\",\"y\":103,\"orderNum\":131,\"pageCompId\":\"912577433450364928\"},{\"component\":\"JPie\",\"pcX\":15,\"w\":9,\"moved\":false,\"pcY\":39,\"x\":15,\"h\":20,\"i\":\"ed714128-19d1-4117-a7b0-029620389fda\",\"y\":39,\"orderNum\":148,\"pageCompId\":\"912577433471336448\"},{\"component\":\"JMultipleBar\",\"pcX\":5,\"w\":10,\"moved\":false,\"pcY\":81,\"x\":5,\"h\":22,\"i\":\"7bcdb3a7-eff0-4373-9911-e900d3e3ec7d\",\"y\":81,\"orderNum\":148,\"pageCompId\":\"912577433496502272\"}]', protection_code = 'amVlY2cxMzE0' WHERE id = '910394028067438592';
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433139986432', NULL, '910394028067438592', NULL, 'JText', '{\"chartData\":\"库存管理可视化大屏\",\"borderColor\":\"#059DA8\",\"size\":{\"width\":1817,\"height\":78},\"background\":\"#059DA8\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"fontSize\":30,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433169346560', NULL, '910394028067438592', NULL, 'JGrowCard', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"库存管理-卡片\",\"query\":[],\"h\":19,\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryManagement\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"chartData\":\"[  {    \\\"title\\\": \\\"访问数\\\",    \\\"icon\\\": \\\"icon-jeecg-qianbao\\\",    \\\"value\\\": 2000,    \\\"total\\\": 120000,    \\\"prefix\\\": \\\"$\\\",    \\\"color\\\": \\\"green\\\",    \\\"action\\\": \\\"月\\\"  },  {    \\\"title\\\": \\\"成交额\\\",    \\\"icon\\\": \\\"icon-jeecg-youhuiquan\\\",    \\\"value\\\": 20000,    \\\"total\\\": 500000,    \\\"prefix\\\": \\\"$\\\",    \\\"color\\\": \\\"blue\\\",    \\\"action\\\": \\\"月\\\"  },  {    \\\"title\\\": \\\"下载数\\\",    \\\"icon\\\": \\\"icon-jeecg-tupian\\\",    \\\"value\\\": 8000,    \\\"prefix\\\": \\\"$\\\",    \\\"total\\\": 120000,    \\\"color\\\": \\\"orange\\\",    \\\"action\\\": \\\"周\\\"  },  {    \\\"title\\\": \\\"成交数\\\",    \\\"icon\\\": \\\"icon-jeecg-jifen\\\",    \\\"value\\\": 5000,    \\\"prefix\\\": \\\"$\\\",    \\\"total\\\": 50000,    \\\"color\\\": \\\"purple\\\",    \\\"action\\\": \\\"年\\\"  }]\",\"size\":{\"width\":1817,\"height\":331},\"dataSetId\":\"910406419257802752\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"icon\",\"text\":\"icon\",\"value\":\"icon\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"},{\"label\":\"action\",\"text\":\"action\",\"value\":\"action\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"icon\":{\"scriptUrl\":\"//at.alicdn.com/t/font_3237315_b3fqd960glt.js\",\"fontSize\":20},\"body\":{\"horizontal\":7,\"color\":\"#000000\",\"vertical\":5,\"span\":8},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"更多\",\"title\":\"统计卡片\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433190318080', NULL, '910394028067438592', NULL, 'JText', '{\"chartData\":\"AIR 库存情况\",\"borderColor\":\"#059DA8\",\"size\":{\"width\":371,\"height\":45},\"background\":\"#059DA8\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":7,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433207095296', NULL, '910394028067438592', NULL, 'JList', '{\"borderColor\":\"#FFFFFF\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"描述\"},{\"mapping\":\"value\",\"filed\":\"时间\"},{\"mapping\":\"\",\"filed\":\"封面\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"air库存情况\",\"query\":[],\"h\":24,\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryStatus?type=air\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/list\",\"timeOut\":-1,\"chartData\":\"[  {    \\\"title\\\": \\\"通知一\\\",    \\\"date\\\": \\\"10000\\\"  },  {    \\\"title\\\": \\\"通知二\\\",    \\\"date\\\": \\\"20000\\\"  },  {    \\\"title\\\": \\\"通知三\\\",    \\\"date\\\": \\\"30000\\\"  },  {    \\\"title\\\": \\\"通知四\\\",    \\\"date\\\": \\\"40000\\\"  }]\",\"size\":{\"width\":371,\"height\":111},\"dataSetId\":\"910419343896526848\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"layout\":\"horizontal\",\"showTitlePrefix\":false,\"titleFontSize\":15,\"showTimePrefix\":false,\"body\":{\"color\":\"#000000\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433228066816', NULL, '910394028067438592', NULL, 'JText', '{\"chartData\":\"LITE 库存情况\",\"borderColor\":\"#059DA8\",\"size\":{\"width\":371,\"height\":45},\"background\":\"#059DA8\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":7,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433249038336', NULL, '910394028067438592', NULL, 'JList', '{\"borderColor\":\"#FFFFFF\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"描述\"},{\"mapping\":\"value\",\"filed\":\"时间\"},{\"mapping\":\"\",\"filed\":\"封面\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"lite库存情况\",\"query\":[],\"h\":24,\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryStatus?type=lite\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/list\",\"timeOut\":-1,\"chartData\":\"[{\\\"title\\\":\\\"通知一\\\",\\\"date\\\":\\\"2022-3-9 14:20:21\\\"},{\\\"title\\\":\\\"通知二\\\",\\\"date\\\":\\\"2022-3-8 14:20:21\\\"},{\\\"title\\\":\\\"通知三\\\",\\\"date\\\":\\\"2022-3-7 14:20:21\\\"},{\\\"title\\\":\\\"通知四\\\",\\\"date\\\":\\\"2022-3-4 14:20:21\\\"}]\",\"size\":{\"width\":371,\"height\":111},\"dataSetId\":\"910423953398874112\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"layout\":\"horizontal\",\"showTitlePrefix\":false,\"titleFontSize\":15,\"showTimePrefix\":false}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433274204160', NULL, '910394028067438592', NULL, 'JText', '{\"chartData\":\"SUPER 库存情况\",\"borderColor\":\"#059DA8\",\"size\":{\"width\":371,\"height\":45},\"background\":\"#059DA8\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":7,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433295175680', NULL, '910394028067438592', NULL, 'JList', '{\"borderColor\":\"#FFFFFF\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"描述\"},{\"mapping\":\"value\",\"filed\":\"时间\"},{\"mapping\":\"\",\"filed\":\"封面\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"super库存情况\",\"query\":[],\"h\":24,\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryStatus?type=super\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/list\",\"timeOut\":-1,\"chartData\":\"[{\\\"title\\\":\\\"通知一\\\",\\\"date\\\":\\\"2022-3-9 14:20:21\\\"},{\\\"title\\\":\\\"通知二\\\",\\\"date\\\":\\\"2022-3-8 14:20:21\\\"},{\\\"title\\\":\\\"通知三\\\",\\\"date\\\":\\\"2022-3-7 14:20:21\\\"},{\\\"title\\\":\\\"通知四\\\",\\\"date\\\":\\\"2022-3-4 14:20:21\\\"}]\",\"size\":{\"width\":371,\"height\":111},\"dataSetId\":\"910425301976662016\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"layout\":\"horizontal\",\"showTitlePrefix\":false,\"titleFontSize\":15,\"showTimePrefix\":false}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433316147200', NULL, '910394028067438592', NULL, 'JText', '{\"chartData\":\"ULTRA 库存情况\",\"borderColor\":\"#059DA8\",\"size\":{\"width\":371,\"height\":45},\"background\":\"#059DA8\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":7,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433337118720', NULL, '910394028067438592', NULL, 'JList', '{\"borderColor\":\"#FFFFFF\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"描述\"},{\"mapping\":\"value\",\"filed\":\"时间\"},{\"mapping\":\"\",\"filed\":\"封面\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"ultra库存情况\",\"query\":[],\"h\":24,\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryStatus?type=ultra\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/list\",\"timeOut\":-1,\"chartData\":\"[{\\\"title\\\":\\\"通知一\\\",\\\"date\\\":\\\"2022-3-9 14:20:21\\\"},{\\\"title\\\":\\\"通知二\\\",\\\"date\\\":\\\"2022-3-8 14:20:21\\\"},{\\\"title\\\":\\\"通知三\\\",\\\"date\\\":\\\"2022-3-7 14:20:21\\\"},{\\\"title\\\":\\\"通知四\\\",\\\"date\\\":\\\"2022-3-4 14:20:21\\\"}]\",\"size\":{\"width\":371,\"height\":111},\"dataSetId\":\"910427052079366144\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"layout\":\"horizontal\",\"showTitlePrefix\":false,\"titleFontSize\":15,\"showTimePrefix\":false,\"body\":{\"color\":\"#000000\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433366478848', NULL, '910394028067438592', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"产品库存占比情况\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=storkProportion\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":751,\"height\":210},\"dataSetId\":\"910430104345690112\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"},\"interval\":2},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"rotate\":0,\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":50,\"left\":76,\"bottom\":67,\"show\":false,\"right\":3},\"series\":[{\"barWidth\":39,\"data\":[],\"color\":[\"#207B85\",\"#2C5E5A\",\"#36756E\"],\"itemStyle\":{\"color\":\"#009BA7\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"产品库存占比情况\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433387450368', NULL, '910394028067438592', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"当月出库情况\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=outbound\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":751,\"height\":232},\"dataSetId\":\"910435726206222336\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"interval\":2}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":48,\"left\":62,\"bottom\":62,\"show\":false},\"series\":[{\"barWidth\":40,\"data\":[],\"color\":[\"#075A63\",\"#285754\",\"#37706A\",\"#63968F\",\"#09A7B3\"],\"itemStyle\":{\"color\":\"#059DA8\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"当月出库情况\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433408421888', NULL, '910394028067438592', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"当月入库情况\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=warehousing\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":675,\"height\":232},\"dataSetId\":\"910441161197928448\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"interval\":2}},\"grid\":{\"top\":45,\"bottom\":58,\"show\":false},\"series\":[{\"barWidth\":40,\"data\":[],\"itemStyle\":{\"color\":\"#059DA8\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"当月入库情况\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433429393408', NULL, '910394028067438592', NULL, 'JMultipleBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"type\",\"filed\":\"分组\"},{\"mapping\":\"title\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"产品库龄分布情况\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=stockAge\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"1991\\\",\\\"value\\\":3,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1992\\\",\\\"value\\\":4,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1993\\\",\\\"value\\\":3.5,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1994\\\",\\\"value\\\":5,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1995\\\",\\\"value\\\":4.9,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1996\\\",\\\"value\\\":6,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1997\\\",\\\"value\\\":7,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1998\\\",\\\"value\\\":9,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1999\\\",\\\"value\\\":13,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1991\\\",\\\"value\\\":3,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1992\\\",\\\"value\\\":4,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1993\\\",\\\"value\\\":3.5,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1994\\\",\\\"value\\\":5,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1995\\\",\\\"value\\\":4.9,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1996\\\",\\\"value\\\":6,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1997\\\",\\\"value\\\":7,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1998\\\",\\\"value\\\":9,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1999\\\",\\\"value\\\":13,\\\"type\\\":\\\"Bor\\\"}]\",\"size\":{\"width\":675,\"height\":232},\"dataSetId\":\"910447275230674944\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"type\",\"text\":\"type\",\"value\":\"type\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":54,\"bottom\":54},\"series\":[{\"color\":[\"#09A7B3\",\"#499E91\",\"#03A2AD\",\"#68F7D8\"]}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"产品库龄分布情况\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433450364928', NULL, '910394028067438592', NULL, 'JCommonTable', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"产品库存状态监控\",\"query\":[],\"h\":42,\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=monitor\",\"drillData\":[],\"timeOut\":-1,\"chartData\":\"[  {    \\\"name\\\": \\\"4月\\\",    \\\"value\\\": 50  },  {    \\\"name\\\": \\\"2月\\\",    \\\"value\\\": 200  },  {    \\\"name\\\": \\\"3月\\\",    \\\"value\\\": 300  },  {    \\\"name\\\": \\\"4月\\\",    \\\"value\\\": 400  },  {    \\\"name\\\": \\\"5月\\\",    \\\"value\\\": 50  },  {    \\\"name\\\": \\\"6月\\\",    \\\"value\\\": 120  }]\",\"size\":{\"width\":1817,\"height\":485},\"dataSetId\":\"910455640270880768\",\"fieldOption\":[{\"label\":\"仓库\",\"text\":\"仓库\",\"value\":\"name\"},{\"label\":\"系列\",\"text\":\"系列\",\"value\":\"series\"},{\"label\":\"市场名\",\"text\":\"市场名\",\"value\":\"marketName\"},{\"label\":\"机型\",\"text\":\"机型\",\"value\":\"model\"},{\"label\":\"产品状态\",\"text\":\"产品状态\",\"value\":\"productStatus\"},{\"label\":\"当前库存\",\"text\":\"当前库存\",\"value\":\"inventory\"},{\"label\":\"库存状态\",\"text\":\"库存状态\",\"value\":\"inventoryStatus\"}],\"dataSetType\":\"api\",\"seriesType\":[],\"background\":\"#FFFFFF\",\"w\":24,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"columns\":[{\"izShow\":\"Y\",\"dataIndex\":\"name\",\"title\":\"仓库\"},{\"izShow\":\"Y\",\"dataIndex\":\"series\",\"title\":\"系列\"},{\"izShow\":\"Y\",\"dataIndex\":\"marketName\",\"title\":\"市场名\"},{\"izShow\":\"Y\",\"dataIndex\":\"model\",\"title\":\"机型\"},{\"izShow\":\"Y\",\"dataIndex\":\"productStatus\",\"title\":\"产品状态\"},{\"izShow\":\"Y\",\"dataIndex\":\"inventory\",\"title\":\"当前库存\"},{\"izShow\":\"Y\",\"dataIndex\":\"inventoryStatus\",\"title\":\"库存状态\"}],\"body\":{\"color\":\"#000000\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433471336448', NULL, '910394028067438592', NULL, 'JPie', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"仓库状态情况\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=warehouseStatus\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":-1,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":675,\"height\":210},\"dataSetId\":\"910431120222896128\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"bottom\":115,\"show\":false},\"legend\":{\"orient\":\"vertical\"},\"series\":[{\"data\":[],\"color\":[\"#05A0AB\",\"#65A89E\",\"#469C8F\"],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 0.5)\"}},\"label\":{\"color\":\"#000000\",\"show\":true},\"type\":\"pie\",\"radius\":\"50%\"}],\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":true,\"text\":\"仓库状态情况\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577433496502272', NULL, '910394028067438592', NULL, 'JMultipleBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"type\",\"filed\":\"分组\"},{\"mapping\":\"title\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"产品库存覆盖率情况\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/inventoryChart?type=coverage\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"1991\\\",\\\"value\\\":3,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1992\\\",\\\"value\\\":4,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1993\\\",\\\"value\\\":3.5,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1994\\\",\\\"value\\\":5,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1995\\\",\\\"value\\\":4.9,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1996\\\",\\\"value\\\":6,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1997\\\",\\\"value\\\":7,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1998\\\",\\\"value\\\":9,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1999\\\",\\\"value\\\":13,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1991\\\",\\\"value\\\":3,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1992\\\",\\\"value\\\":4,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1993\\\",\\\"value\\\":3.5,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1994\\\",\\\"value\\\":5,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1995\\\",\\\"value\\\":4.9,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1996\\\",\\\"value\\\":6,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1997\\\",\\\"value\\\":7,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1998\\\",\\\"value\\\":9,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1999\\\",\\\"value\\\":13,\\\"type\\\":\\\"Bor\\\"}]\",\"size\":{\"width\":751,\"height\":232},\"dataSetId\":\"910442989620871168\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"type\",\"text\":\"type\",\"value\":\"type\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"type\":\"value\",\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":41,\"left\":104,\"bottom\":52,\"right\":90},\"series\":[{\"color\":[\"#05A5B0\",\"#6BB3A8\",\"#4AA194\",\"#1D828A\"]}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"show\":true,\"text\":\"产品库存覆盖率情况\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:43:45', NULL, NULL);

-- 某电商公司销售运营看板
UPDATE onl_drag_page SET  template = '[{\"component\":\"JText\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":0,\"x\":0,\"h\":8,\"i\":\"1aa60c03-aa11-400b-81a9-3a710ef7b17d\",\"y\":0,\"orderNum\":0,\"pageCompId\":\"912579172505272320\"},{\"component\":\"JGrowCard\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":10,\"x\":0,\"h\":17,\"i\":\"3ab3538e-5195-452c-82a7-08a1768548bb\",\"y\":8,\"orderNum\":10,\"pageCompId\":\"912579172538826752\"},{\"component\":\"JBubbleMap\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":27,\"x\":0,\"h\":42,\"i\":\"79506e2d-bfca-410b-bd5f-3fc1791af798\",\"y\":25,\"orderNum\":26,\"pageCompId\":\"912579172559798272\"},{\"component\":\"JCommonTable\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":69,\"x\":0,\"h\":45,\"i\":\"cd448408-0e55-41b3-8ab7-3b7dd8657055\",\"y\":67,\"orderNum\":72,\"pageCompId\":\"912579172580769792\"},{\"component\":\"JPie\",\"pcX\":8,\"w\":5,\"moved\":false,\"pcY\":10,\"x\":8,\"h\":35,\"i\":\"40b0322c-f6a9-4614-83b8-c77a086bc065\",\"y\":8,\"orderNum\":105,\"pageCompId\":\"912579172597547008\"},{\"component\":\"JNumber\",\"pcX\":13,\"w\":5,\"moved\":false,\"pcY\":10,\"x\":13,\"h\":17,\"i\":\"353858f6-fecf-4c42-81f4-537d24289a68\",\"y\":8,\"orderNum\":105,\"pageCompId\":\"912579172618518528\"},{\"component\":\"JNumber\",\"pcX\":13,\"w\":5,\"moved\":false,\"pcY\":27,\"x\":13,\"h\":18,\"i\":\"3a74f30d-8357-43c4-811c-a0a1c7201453\",\"y\":25,\"orderNum\":105,\"pageCompId\":\"912579172643684352\"},{\"component\":\"JPie\",\"pcX\":18,\"w\":6,\"moved\":false,\"pcY\":10,\"x\":18,\"h\":35,\"i\":\"2bd80a2b-f848-49d6-875b-05897deac11c\",\"y\":8,\"orderNum\":105,\"pageCompId\":\"912579172664655872\"},{\"component\":\"JBar\",\"pcX\":8,\"w\":8,\"moved\":false,\"pcY\":45,\"x\":8,\"h\":29,\"i\":\"043d13e0-4e90-47e4-8715-29cc400adeb1\",\"y\":43,\"orderNum\":105,\"pageCompId\":\"912579172685627392\"},{\"component\":\"JBar\",\"pcX\":16,\"w\":8,\"moved\":false,\"pcY\":45,\"x\":16,\"h\":29,\"i\":\"4b5fbe15-8931-48bb-a0f0-67855e782af4\",\"y\":43,\"orderNum\":114,\"pageCompId\":\"912579172702404608\"},{\"component\":\"JBar\",\"pcX\":8,\"w\":8,\"moved\":false,\"pcY\":74,\"x\":8,\"h\":40,\"i\":\"e2f99a5d-eaf4-4823-b1a7-eb0a878be363\",\"y\":72,\"orderNum\":114,\"pageCompId\":\"912579172719181824\"},{\"component\":\"JLine\",\"pcX\":16,\"w\":8,\"moved\":false,\"pcY\":74,\"x\":16,\"h\":40,\"i\":\"dad9aacc-549f-4418-8f55-8ca499aa83b4\",\"y\":72,\"orderNum\":114,\"pageCompId\":\"912579172740153344\"}]', protection_code = 'amVlY2cxMzE0' WHERE id = '910475721247866880';
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912579172505272320', NULL, '910475721247866880', NULL, 'JText', '{\"chartData\":\"某电商公司销售运营看板\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":1817,\"height\":78},\"background\":\"#0774F0\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":23,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:50:39', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912579172538826752', NULL, '910475721247866880', NULL, 'JGrowCard', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"title\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"图标\"},{\"mapping\":\"value\",\"filed\":\"数值\"},{\"mapping\":\"\",\"filed\":\"总计\"},{\"mapping\":\"\",\"filed\":\"前缀\"},{\"mapping\":\"\",\"filed\":\"颜色\"},{\"mapping\":\"unit\",\"filed\":\"单位\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"销售状态\",\"query\":[],\"h\":19,\"dataSetApi\":\"https://api.jeecg.com/mock/51/commerceSalesOperations?type=saleStatus\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"chartData\":\"[{\\\"title\\\":\\\"访问数\\\",\\\"icon\\\":\\\"icon-jeecg-qianbao\\\",\\\"value\\\":2000,\\\"total\\\":120000,\\\"prefix\\\":\\\"$\\\",\\\"color\\\":\\\"green\\\",\\\"action\\\":\\\"月\\\"},{\\\"title\\\":\\\"成交额\\\",\\\"icon\\\":\\\"icon-jeecg-youhuiquan\\\",\\\"value\\\":20000,\\\"total\\\":500000,\\\"prefix\\\":\\\"$\\\",\\\"color\\\":\\\"blue\\\",\\\"action\\\":\\\"月\\\"},{\\\"title\\\":\\\"下载数\\\",\\\"icon\\\":\\\"icon-jeecg-tupian\\\",\\\"value\\\":8000,\\\"prefix\\\":\\\"$\\\",\\\"total\\\":120000,\\\"color\\\":\\\"orange\\\",\\\"action\\\":\\\"周\\\"},{\\\"title\\\":\\\"成交数\\\",\\\"icon\\\":\\\"icon-jeecg-jifen\\\",\\\"value\\\":5000,\\\"prefix\\\":\\\"$\\\",\\\"total\\\":50000,\\\"color\\\":\\\"purple\\\",\\\"action\\\":\\\"年\\\"}]\",\"size\":{\"width\":599,\"height\":177},\"dataSetId\":\"910478592823574528\",\"fieldOption\":[{\"label\":\"title\",\"text\":\"title\",\"value\":\"title\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"},{\"label\":\"unit\",\"text\":\"unit\",\"value\":\"unit\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"icon\":{\"scriptUrl\":\"//at.alicdn.com/t/font_3237315_b3fqd960glt.js\",\"fontSize\":20},\"body\":{\"horizontal\":8,\"vertical\":8,\"span\":8},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"更多\",\"title\":\"统计卡片\"}}}', 'admin', '2024-01-31 15:50:39', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912579172559798272', NULL, '910475721247866880', NULL, 'JBubbleMap', '{\"borderColor\":\"#FFFFFF00\",\"commonOption\":{\"barSize\":10,\"gradientColor\":false,\"breadcrumb\":{\"drillDown\":false,\"textColor\":\"#000000\"},\"areaColor\":{\"color1\":\"#f7f7f7\",\"color2\":\"#fcc02e\"},\"barColor\":\"#fff176\",\"barColor2\":\"#fcc02e\",\"inRange\":{\"color\":[\"#04387b\",\"#467bc0\"]}},\"paramOption\":[],\"dataSetName\":\"各地区订单与仓库情况\",\"activeKey\":1,\"chartData\":\"[{\\\"name\\\":\\\"北京\\\",\\\"value\\\":199},{\\\"name\\\":\\\"新疆\\\",\\\"value\\\":180},{\\\"name\\\":\\\"河南\\\",\\\"value\\\":137},{\\\"name\\\":\\\"四川\\\",\\\"value\\\":125},{\\\"name\\\":\\\"黑龙江\\\",\\\"value\\\":123},{\\\"name\\\":\\\"广东\\\",\\\"value\\\":123},{\\\"name\\\":\\\"山东\\\",\\\"value\\\":119},{\\\"name\\\":\\\"福建\\\",\\\"value\\\":116},{\\\"name\\\":\\\"湖北\\\",\\\"value\\\":116},{\\\"name\\\":\\\"浙江\\\",\\\"value\\\":114},{\\\"name\\\":\\\"湖南\\\",\\\"value\\\":114},{\\\"name\\\":\\\"安徽\\\",\\\"value\\\":109},{\\\"name\\\":\\\"河北\\\",\\\"value\\\":102},{\\\"name\\\":\\\"江苏\\\",\\\"value\\\":92},{\\\"name\\\":\\\"江西\\\",\\\"value\\\":91},{\\\"name\\\":\\\"重庆\\\",\\\"value\\\":91},{\\\"name\\\":\\\"云南\\\",\\\"value\\\":83},{\\\"name\\\":\\\"吉林\\\",\\\"value\\\":82},{\\\"name\\\":\\\"山西\\\",\\\"value\\\":81},{\\\"name\\\":\\\"陕西\\\",\\\"value\\\":80},{\\\"name\\\":\\\"辽宁\\\",\\\"value\\\":67},{\\\"name\\\":\\\"贵州\\\",\\\"value\\\":62},{\\\"name\\\":\\\"广西\\\",\\\"value\\\":59},{\\\"name\\\":\\\"甘肃\\\",\\\"value\\\":56},{\\\"name\\\":\\\"内蒙古\\\",\\\"value\\\":47},{\\\"name\\\":\\\"天津\\\",\\\"value\\\":42},{\\\"name\\\":\\\"上海\\\",\\\"value\\\":24},{\\\"name\\\":\\\"宁夏\\\",\\\"value\\\":18},{\\\"name\\\":\\\"海南\\\",\\\"value\\\":14},{\\\"name\\\":\\\"青海\\\",\\\"value\\\":10},{\\\"name\\\":\\\"西藏\\\",\\\"value\\\":9}]\",\"fieldOption\":[{\"label\":\"仓库名称\",\"text\":\"仓库名称\",\"value\":\"warehouseName\"},{\"label\":\"仓库存量\",\"text\":\"仓库存量\",\"value\":\"warehouseCount\"},{\"label\":\"省份\",\"text\":\"省份\",\"value\":\"name\"},{\"label\":\"销售额\",\"text\":\"销售额\",\"value\":\"value\"}],\"seriesType\":[],\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"区域\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"jsConfig\":\"\",\"dataType\":2,\"query\":[],\"h\":50,\"dataSetApi\":\"https://api.jeecg.com/mock/51/commerceSalesOperations?type=regionalOrders\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/radar\",\"timeOut\":0,\"size\":{\"width\":599,\"height\":452},\"dataSetId\":\"910695134387552256\",\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"w\":12,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"drillDown\":false,\"area\":{\"markerColor\":\"#DDE330\",\"shadowBlur\":10,\"markerCount\":5,\"markerOpacity\":1,\"name\":[\"中国\"],\"scatterLabelShow\":false,\"shadowColor\":\"#DDE330\",\"value\":[\"china\"],\"markerType\":\"effectScatter\"},\"geo\":{\"top\":80,\"itemStyle\":{\"normal\":{\"shadowOffsetX\":0,\"borderColor\":\"#a9a9a9\",\"shadowOffsetY\":0,\"areaColor\":\"\",\"shadowBlur\":0,\"borderWidth\":1,\"shadowColor\":\"#80d9f8\"},\"emphasis\":{\"areaColor\":\"#fff59c\",\"borderWidth\":0}},\"zoom\":1,\"label\":{\"emphasis\":{\"color\":\"#fff\",\"show\":false}},\"roam\":true},\"grid\":{\"bottom\":115,\"show\":false},\"legend\":{\"data\":[]},\"title\":{\"left\":10,\"show\":true,\"text\":\"\"},\"graphic\":[],\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"},\"visualMap\":{\"min\":0,\"top\":\"bottom\",\"max\":200,\"left\":\"5%\",\"calculable\":true,\"show\":false,\"type\":\"continuous\",\"seriesIndex\":[1]}}}', 'admin', '2024-01-31 15:50:39', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912579172580769792', NULL, '910475721247866880', NULL, 'JCommonTable', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"各地区订单与仓库情况\",\"query\":[],\"h\":42,\"dataSetApi\":\"https://api.jeecg.com/mock/51/commerceSalesOperations?type=regionalOrders\",\"drillData\":[],\"timeOut\":-1,\"chartData\":\"[{\\\"name\\\":\\\"4月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"2月\\\",\\\"value\\\":200},{\\\"name\\\":\\\"3月\\\",\\\"value\\\":300},{\\\"name\\\":\\\"4月\\\",\\\"value\\\":400},{\\\"name\\\":\\\"5月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"6月\\\",\\\"value\\\":120}]\",\"size\":{\"width\":599,\"height\":485},\"dataSetId\":\"910695134387552256\",\"fieldOption\":[{\"label\":\"仓库名称\",\"text\":\"仓库名称\",\"value\":\"warehouseName\"},{\"label\":\"仓库存量\",\"text\":\"仓库存量\",\"value\":\"warehouseCount\"},{\"label\":\"省份\",\"text\":\"省份\",\"value\":\"name\"},{\"label\":\"销售额\",\"text\":\"销售额\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"seriesType\":[],\"background\":\"#FFFFFF\",\"w\":24,\"dataNum\":\"0\",\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"columns\":[{\"izShow\":\"Y\",\"dataIndex\":\"warehouseName\",\"title\":\"仓库名称\"},{\"izShow\":\"Y\",\"dataIndex\":\"warehouseCount\",\"title\":\"仓库存量\"},{\"izShow\":\"N\",\"dataIndex\":\"name\",\"title\":\"省份\"},{\"izShow\":\"Y\",\"dataIndex\":\"value\",\"title\":\"销售额\"}]}}', 'admin', '2024-01-31 15:50:39', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912579172597547008', NULL, '910475721247866880', NULL, 'JPie', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"季度目标完成\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/commerceSalesOperations?type=quarterlyFinish\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":-1,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":371,\"height\":375},\"dataSetId\":\"910708293282349056\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"bottom\":115,\"show\":false},\"legend\":{\"orient\":\"vertical\"},\"series\":[{\"data\":[],\"color\":[\"#1F70E0\",\"#F0F2FA\"],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 0.5)\"}},\"type\":\"pie\",\"radius\":\"50%\"}],\"isRadius\":true,\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":true,\"text\":\"季度目标完成\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:50:39', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912579172618518528', NULL, '910475721247866880', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":1,\"dataSetName\":\"季度目标完成\",\"query\":[],\"h\":9,\"dataSetApi\":\"https://api.jeecg.com/mock/51/commerceSalesOperations?type=quarterlyFinish\",\"drillData\":[],\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{  \\\"value\\\": 200}\",\"size\":{\"width\":371,\"height\":177},\"dataSetId\":\"910708293282349056\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#1C6CDE\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"季度销售额（万）\"}}}', 'admin', '2024-01-31 15:50:39', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912579172643684352', NULL, '910475721247866880', NULL, 'JNumber', '{\"chartData\":\"{  \\\"value\\\": \\\"39\\\"}\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":371,\"height\":188},\"background\":\"#FFFFFF\",\"w\":5,\"dataType\":1,\"h\":9,\"turnConfig\":{\"url\":\"\"},\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#1C6CDE\",\"text\":\"\",\"fontWeight\":\"bold\"},\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"同比增长（%）\"}}}', 'admin', '2024-01-31 15:50:39', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912579172664655872', NULL, '910475721247866880', NULL, 'JPie', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"品牌销售占比\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/commerceSalesOperations?type=brandSales\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":-1,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":447,\"height\":375},\"dataSetId\":\"910721009699045376\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"left\":47,\"bottom\":115,\"show\":false},\"legend\":{\"r\":1,\"orient\":\"vertical\",\"t\":1},\"series\":[{\"data\":[],\"color\":[\"#0E52B0\",\"#118FF0\",\"#97CFFC\",\"#216DC4\",\"#60AEF7\"],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 0.5)\"}},\"type\":\"pie\",\"radius\":\"50%\"}],\"isRadius\":true,\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":true,\"text\":\"品牌销售占比\",\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-31 15:50:39', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912579172685627392', NULL, '910475721247866880', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"销售地区排行\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/commerceSalesOperations?type=areaRanking\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":599,\"height\":309},\"dataSetId\":\"910732622212677632\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"},\"interval\":2},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":90,\"bottom\":115,\"show\":false},\"series\":[{\"barWidth\":40,\"data\":[],\"showBackground\":false,\"backgroundStyle\":{\"color\":\"#51626E\"},\"itemStyle\":{\"color\":\"#428BEF\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"show\":true,\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"销售额（万元）\",\"show\":true,\"text\":\"销售地区排行\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-31 15:50:39', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912579172702404608', NULL, '910475721247866880', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"品牌销售占比\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/commerceSalesOperations?type=brandSales\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":599,\"height\":309},\"dataSetId\":\"910721009699045376\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"interval\":2}},\"grid\":{\"top\":90,\"bottom\":115,\"show\":false},\"series\":[{\"barWidth\":40,\"data\":[],\"itemStyle\":{\"color\":\"#428BEF\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"subtext\":\"销售额（元）\",\"show\":true,\"text\":\"品牌销售情况\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-31 15:50:39', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912579172719181824', NULL, '910475721247866880', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"物流订单接收\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/commerceSalesOperations?type=logisticsOrder\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":599,\"height\":430},\"dataSetId\":\"910737864308342784\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"interval\":2}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"type\":\"value\",\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":90,\"left\":58,\"bottom\":115,\"show\":false},\"series\":[{\"barWidth\":24,\"data\":[],\"showBackground\":false,\"backgroundStyle\":{\"color\":\"#51626E\"},\"itemStyle\":{\"color\":\"#428BEF\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"subtext\":\"接收订单（件）\",\"show\":true,\"text\":\"物流订单接收\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:50:39', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912579172740153344', NULL, '910475721247866880', NULL, 'JLine', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"订单产生趋势\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/commerceSalesOperations?type=generatingTrends\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1000,\\\"name\\\":\\\"联想\\\"},{\\\"value\\\":7350,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":5800,\\\"name\\\":\\\"华为\\\"},{\\\"value\\\":6000,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":3000,\\\"name\\\":\\\"戴尔\\\"}]\",\"size\":{\"width\":599,\"height\":430},\"dataSetId\":\"910740127152128000\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"customColor\":[{\"color\":\"#428BEF\"}],\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":90,\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"lineType\":\"area\",\"itemStyle\":{\"color\":\"#64B5F6\"},\"type\":\"line\"}],\"tooltip\":{\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"订单数（单）\",\"left\":10,\"text\":\"订单产生趋势\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:50:39', NULL, NULL);

-- 物业消防巡检状态
UPDATE onl_drag_page SET  template = '[{\"component\":\"JText\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":0,\"x\":0,\"h\":8,\"i\":\"5d85e389-7ee4-40dd-8544-80049646ee34\",\"y\":0,\"orderNum\":0,\"pageCompId\":\"912578020841668608\"},{\"component\":\"JText\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":10,\"x\":0,\"h\":6,\"i\":\"878306f4-8ff4-412c-b8d8-744b0897ae8f\",\"y\":8,\"orderNum\":10,\"pageCompId\":\"912578020875223040\"},{\"component\":\"JList\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":20,\"x\":0,\"h\":17,\"i\":\"fe852828-ba3d-46d1-884a-1c723b870d55\",\"y\":14,\"orderNum\":20,\"pageCompId\":\"912578020896194560\"},{\"component\":\"JText\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":37,\"x\":0,\"h\":7,\"i\":\"57fd4478-440a-4a6e-a115-186e14d5047a\",\"y\":31,\"orderNum\":38,\"pageCompId\":\"912578020917166080\"},{\"component\":\"JNumber\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":47,\"x\":0,\"h\":12,\"i\":\"bb98b9db-042b-445e-8672-34182191871d\",\"y\":38,\"orderNum\":48,\"pageCompId\":\"912578020938137600\"},{\"component\":\"JNumber\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":57,\"x\":0,\"h\":12,\"i\":\"7eeab8ac-66f7-4d80-81a8-2e75dcb7093a\",\"y\":50,\"orderNum\":58,\"pageCompId\":\"912578020959109120\"},{\"component\":\"JText\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":67,\"x\":0,\"h\":7,\"i\":\"d7a87bcb-e5a6-4092-9b44-be37c284761d\",\"y\":62,\"orderNum\":69,\"pageCompId\":\"912578020980080640\"},{\"component\":\"JCommonTable\",\"pcX\":0,\"w\":8,\"moved\":false,\"pcY\":77,\"x\":0,\"h\":51,\"i\":\"1397ca94-7293-48bd-bb4f-673355c0355e\",\"y\":69,\"orderNum\":79,\"pageCompId\":\"912578021005246464\"},{\"component\":\"JBubbleMap\",\"pcX\":8,\"w\":8,\"moved\":false,\"pcY\":20,\"x\":8,\"h\":32,\"i\":\"f0f84536-4ae3-43ff-aadb-6a1f113b1ab9\",\"y\":14,\"orderNum\":109,\"pageCompId\":\"912578021026217984\"},{\"component\":\"JText\",\"pcX\":8,\"w\":16,\"moved\":false,\"pcY\":10,\"x\":8,\"h\":6,\"i\":\"52c28b77-268c-4dc4-805e-a48b37657be1\",\"y\":8,\"orderNum\":109,\"pageCompId\":\"912578021047189504\"},{\"component\":\"JCommonTable\",\"pcX\":16,\"w\":8,\"moved\":false,\"pcY\":20,\"x\":16,\"h\":32,\"i\":\"8b2b0a47-c1fb-47bf-b8af-9c249d903baa\",\"y\":14,\"orderNum\":109,\"pageCompId\":\"912578021068161024\"},{\"component\":\"JPie\",\"pcX\":8,\"w\":8,\"moved\":false,\"pcY\":52,\"x\":8,\"h\":34,\"i\":\"619921d9-f1fe-4d09-8f3d-09238f6d3e4f\",\"y\":46,\"orderNum\":109,\"pageCompId\":\"912578021093326848\"},{\"component\":\"JBar\",\"pcX\":16,\"w\":8,\"moved\":false,\"pcY\":52,\"x\":16,\"h\":34,\"i\":\"2598e9f8-7611-49c1-97a5-018fba23f0e0\",\"y\":46,\"orderNum\":109,\"pageCompId\":\"912578021114298368\"},{\"component\":\"JLine\",\"pcX\":8,\"w\":8,\"moved\":false,\"pcY\":98,\"x\":8,\"h\":33,\"i\":\"01569400-637c-4d01-986e-c6604f88cc32\",\"y\":87,\"orderNum\":109,\"pageCompId\":\"912578021131075584\"},{\"component\":\"JCommonTable\",\"pcX\":16,\"w\":8,\"moved\":false,\"pcY\":86,\"x\":16,\"h\":33,\"i\":\"4fd26be3-3138-4cf5-9e7a-e24ff75bafad\",\"y\":87,\"orderNum\":114,\"pageCompId\":\"912578021156241408\"},{\"component\":\"JText\",\"pcX\":8,\"w\":16,\"moved\":false,\"pcY\":86,\"x\":8,\"h\":7,\"i\":\"e3bea36a-c897-41ec-9c8d-c572609a74e5\",\"y\":80,\"orderNum\":114,\"pageCompId\":\"912578021181407232\"}]', protection_code = 'amVlY2cxMzE0', type = '1', iz_template = '1', create_by = 'admin', create_time = '2024-01-26 14:19:02', update_by = 'admin', update_time = '2024-01-31 15:46:14', low_app_id = NULL, tenant_id = 6902, update_count = 35, visits_num = 5 WHERE id = '910744177604083712';
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578020841668608', NULL, '910744177604083712', NULL, 'JText', '{\"chartData\":\"小 区 消 防 巡 检 状 态\",\"borderColor\":\"#DB771F\",\"size\":{\"width\":1817,\"height\":78},\"background\":\"#DB771F\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"fontSize\":36,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578020875223040', NULL, '910744177604083712', NULL, 'JText', '{\"chartData\":\"区域基本情况\",\"borderColor\":\"#DB771F\",\"size\":{\"width\":599,\"height\":56},\"background\":\"#DB771F\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"fontSize\":22,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":37},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578020896194560', NULL, '910744177604083712', NULL, 'JList', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"描述\"},{\"mapping\":\"value\",\"filed\":\"时间\"},{\"mapping\":\"\",\"filed\":\"封面\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"区域基本情况\",\"query\":[],\"h\":24,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=regionBasicInformation\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/list\",\"timeOut\":-1,\"chartData\":\"[{\\\"title\\\":\\\"通知一\\\",\\\"date\\\":\\\"2022-3-9 14:20:21\\\"},{\\\"title\\\":\\\"通知二\\\",\\\"date\\\":\\\"2022-3-8 14:20:21\\\"},{\\\"title\\\":\\\"通知三\\\",\\\"date\\\":\\\"2022-3-7 14:20:21\\\"},{\\\"title\\\":\\\"通知四\\\",\\\"date\\\":\\\"2022-3-4 14:20:21\\\"}]\",\"size\":{\"width\":599,\"height\":177},\"dataSetId\":\"910750488542625792\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"layout\":\"horizontal\",\"showTitlePrefix\":true,\"titleFontSize\":18,\"showTimePrefix\":false,\"body\":{\"color\":\"#000000\"}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578020917166080', NULL, '910744177604083712', NULL, 'JText', '{\"chartData\":\"设备总数\",\"borderColor\":\"#DB771F\",\"size\":{\"width\":599,\"height\":67},\"background\":\"#DB771F\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"fontSize\":22,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":16,\"marginLeft\":34},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578020938137600', NULL, '910744177604083712', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"dataType\":2,\"dataSetName\":\"正常设备\",\"h\":9,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=normalDevice\",\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{  \\\"value\\\": \\\"15990\\\"}\",\"size\":{\"width\":599,\"height\":122},\"dataSetId\":\"910754028661030912\",\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#000000\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"正常设备\"}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578020959109120', NULL, '910744177604083712', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"异常设备\",\"query\":[],\"h\":9,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=abnormalDevice\",\"drillData\":[],\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{\\\"value\\\":\\\"1024\\\"}\",\"size\":{\"width\":599,\"height\":122},\"dataSetId\":\"910754465934000128\",\"fieldOption\":[{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#000000\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"异常设备\"}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578020980080640', NULL, '910744177604083712', NULL, 'JText', '{\"chartData\":\"设备异常明细\",\"borderColor\":\"#DB771F\",\"size\":{\"width\":599,\"height\":67},\"background\":\"#DB771F\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"fontSize\":22,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":15,\"marginLeft\":32},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578021005246464', NULL, '910744177604083712', NULL, 'JCommonTable', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"设备异常明细\",\"query\":[],\"h\":42,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=equipmentDetails\",\"drillData\":[],\"timeOut\":-1,\"chartData\":\"[{\\\"name\\\":\\\"4月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"2月\\\",\\\"value\\\":200},{\\\"name\\\":\\\"3月\\\",\\\"value\\\":300},{\\\"name\\\":\\\"4月\\\",\\\"value\\\":400},{\\\"name\\\":\\\"5月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"6月\\\",\\\"value\\\":120}]\",\"size\":{\"width\":599,\"height\":551},\"dataSetId\":\"910759432656830464\",\"fieldOption\":[{\"label\":\"小区名称\",\"text\":\"小区名称\",\"value\":\"name\"},{\"label\":\"设备名称\",\"text\":\"设备名称\",\"value\":\"deviceName\"},{\"label\":\"设备地址\",\"text\":\"设备地址\",\"value\":\"deviceAddress\"},{\"label\":\"设备状态\",\"text\":\"设备状态\",\"value\":\"deviceStatus\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":24,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"columns\":[{\"izShow\":\"Y\",\"dataIndex\":\"name\",\"title\":\"小区名称\"},{\"izShow\":\"Y\",\"dataIndex\":\"deviceName\",\"title\":\"设备名称\"},{\"izShow\":\"Y\",\"dataIndex\":\"deviceAddress\",\"title\":\"设备地址\"},{\"izShow\":\"Y\",\"dataIndex\":\"deviceStatus\",\"title\":\"设备状态\"}],\"body\":{\"color\":\"#000000\"}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578021026217984', NULL, '910744177604083712', NULL, 'JBubbleMap', '{\"borderColor\":\"#FFFFFF00\",\"commonOption\":{\"barSize\":10,\"gradientColor\":false,\"breadcrumb\":{\"drillDown\":false,\"textColor\":\"#000000\"},\"areaColor\":{\"color1\":\"#f7f7f7\",\"color2\":\"#fcc02e\"},\"barColor\":\"#fff176\",\"barColor2\":\"#fcc02e\",\"inRange\":{\"color\":[\"#04387b\",\"#467bc0\"]}},\"paramOption\":[],\"dataSetName\":\"小区地图分布\",\"activeKey\":1,\"chartData\":\"[  {    \\\"name\\\": \\\"廊坊\\\",    \\\"value\\\": 199  },  {    \\\"name\\\": \\\"新疆\\\",    \\\"value\\\": 180  },  {    \\\"name\\\": \\\"河南\\\",    \\\"value\\\": 137  },  {    \\\"name\\\": \\\"四川\\\",    \\\"value\\\": 125  },  {    \\\"name\\\": \\\"黑龙江\\\",    \\\"value\\\": 123  },  {    \\\"name\\\": \\\"广东\\\",    \\\"value\\\": 123  },  {    \\\"name\\\": \\\"山东\\\",    \\\"value\\\": 119  },  {    \\\"name\\\": \\\"福建\\\",    \\\"value\\\": 116  },  {    \\\"name\\\": \\\"湖北\\\",    \\\"value\\\": 116  },  {    \\\"name\\\": \\\"浙江\\\",    \\\"value\\\": 114  },  {    \\\"name\\\": \\\"湖南\\\",    \\\"value\\\": 114  },  {    \\\"name\\\": \\\"安徽\\\",    \\\"value\\\": 109  },  {    \\\"name\\\": \\\"河北\\\",    \\\"value\\\": 102  },  {    \\\"name\\\": \\\"江苏\\\",    \\\"value\\\": 92  },  {    \\\"name\\\": \\\"江西\\\",    \\\"value\\\": 91  },  {    \\\"name\\\": \\\"重庆\\\",    \\\"value\\\": 91  },  {    \\\"name\\\": \\\"云南\\\",    \\\"value\\\": 83  },  {    \\\"name\\\": \\\"吉林\\\",    \\\"value\\\": 82  },  {    \\\"name\\\": \\\"山西\\\",    \\\"value\\\": 81  },  {    \\\"name\\\": \\\"陕西\\\",    \\\"value\\\": 80  },  {    \\\"name\\\": \\\"辽宁\\\",    \\\"value\\\": 67  },  {    \\\"name\\\": \\\"贵州\\\",    \\\"value\\\": 62  },  {    \\\"name\\\": \\\"广西\\\",    \\\"value\\\": 59  },  {    \\\"name\\\": \\\"甘肃\\\",    \\\"value\\\": 56  },  {    \\\"name\\\": \\\"内蒙古\\\",    \\\"value\\\": 47  },  {    \\\"name\\\": \\\"天津\\\",    \\\"value\\\": 42  },  {    \\\"name\\\": \\\"上海\\\",    \\\"value\\\": 24  },  {    \\\"name\\\": \\\"宁夏\\\",    \\\"value\\\": 18  },  {    \\\"name\\\": \\\"海南\\\",    \\\"value\\\": 14  },  {    \\\"name\\\": \\\"青海\\\",    \\\"value\\\": 10  },  {    \\\"name\\\": \\\"西藏\\\",    \\\"value\\\": 9  }]\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"seriesType\":[],\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"区域\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"jsConfig\":\"\",\"dataType\":2,\"query\":[],\"h\":50,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=residentialDistributionMap\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/radar\",\"timeOut\":0,\"size\":{\"width\":599,\"height\":342},\"dataSetId\":\"910765056765509632\",\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"w\":12,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"drillDown\":false,\"area\":{\"markerColor\":\"#DDE330\",\"shadowBlur\":10,\"markerCount\":5,\"markerOpacity\":1,\"name\":[\"河北省\"],\"scatterLabelShow\":false,\"shadowColor\":\"#DDE330\",\"value\":[\"13\"],\"markerType\":\"effectScatter\"},\"geo\":{\"top\":27,\"aspectScale\":0.92,\"itemStyle\":{\"normal\":{\"shadowOffsetX\":0,\"borderColor\":\"#a9a9a9\",\"shadowOffsetY\":0,\"areaColor\":\"\",\"shadowBlur\":0,\"borderWidth\":1,\"shadowColor\":\"#80d9f8\"},\"emphasis\":{\"areaColor\":\"#fff59c\",\"borderWidth\":0}},\"zoom\":1,\"label\":{\"emphasis\":{\"color\":\"#fff\",\"show\":false}},\"roam\":true},\"grid\":{\"bottom\":115,\"show\":false},\"legend\":{\"data\":[]},\"title\":{\"left\":10,\"show\":true,\"text\":\"\",\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"graphic\":[],\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"},\"visualMap\":{\"min\":0,\"top\":\"bottom\",\"max\":200,\"left\":\"5%\",\"calculable\":true,\"show\":false,\"type\":\"continuous\",\"seriesIndex\":[1]}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578021047189504', NULL, '910744177604083712', NULL, 'JText', '{\"chartData\":\"小区地图分布\",\"borderColor\":\"#DB771F\",\"size\":{\"width\":1208,\"height\":56},\"background\":\"#DB771F\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"fontSize\":22,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":10,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578021068161024', NULL, '910744177604083712', NULL, 'JCommonTable', '{\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"小区分布地图表格\",\"query\":[],\"h\":42,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=residentialDistributionTable\",\"drillData\":[],\"timeOut\":-1,\"chartData\":\"[{\\\"name\\\":\\\"4月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"2月\\\",\\\"value\\\":200},{\\\"name\\\":\\\"3月\\\",\\\"value\\\":300},{\\\"name\\\":\\\"4月\\\",\\\"value\\\":400},{\\\"name\\\":\\\"5月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"6月\\\",\\\"value\\\":120}]\",\"size\":{\"width\":599,\"height\":342},\"dataSetId\":\"910773111884398592\",\"fieldOption\":[{\"label\":\"区域名称\",\"text\":\"区域名称\",\"value\":\"areaName\"},{\"label\":\"小区数量\",\"text\":\"小区数量\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"seriesType\":[],\"w\":24,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"columns\":[{\"izShow\":\"Y\",\"dataIndex\":\"areaName\",\"title\":\"区域名称\"},{\"izShow\":\"Y\",\"dataIndex\":\"value\",\"title\":\"小区数量\"}],\"title\":{\"textStyle\":{\"color\":\"#464646\"}}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578021093326848', NULL, '910744177604083712', NULL, 'JPie', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"室外消火栓泵\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=deviceCountProportion\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":-1,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":599,\"height\":364},\"dataSetId\":\"910776816075587584\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"bottom\":115,\"show\":false},\"legend\":{\"orient\":\"vertical\"},\"series\":[{\"data\":[],\"color\":[\"#3BB1E3\",\"#DB771F\",\"#B6C5D1\"],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 0.5)\"}},\"label\":{\"color\":\"#000000\",\"show\":true},\"type\":\"pie\",\"radius\":\"50%\"}],\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":true,\"text\":\"设备类型数量占比\",\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578021114298368', NULL, '910744177604083712', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"区域设备数量\\t\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=areaDeviceCount\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":599,\"height\":364},\"dataSetId\":\"910790230315417600\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"},\"interval\":2},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"type\":\"value\",\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":90,\"left\":62,\"bottom\":115,\"show\":false},\"series\":[{\"barWidth\":21,\"data\":[],\"color\":[\"#3BB1E3\",\"#DB771F\",\"#B6C5D1\"],\"itemStyle\":{\"color\":\"#3BB1E3\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"区域设备数量\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578021131075584', NULL, '910744177604083712', NULL, 'JLine', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"巡检任务数\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=inspectionTasksCount\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1000,\\\"name\\\":\\\"联想\\\"},{\\\"value\\\":7350,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":5800,\\\"name\\\":\\\"华为\\\"},{\\\"value\\\":6000,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":3000,\\\"name\\\":\\\"戴尔\\\"}]\",\"size\":{\"width\":599,\"height\":353},\"dataSetId\":\"910794755508060160\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"customColor\":[{\"color\":\"#38B4EA\"}],\"grid\":{\"top\":90,\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"lineType\":\"area\",\"itemStyle\":{\"color\":\"#64B5F6\"},\"type\":\"line\"}],\"title\":{\"subtext\":\"\",\"left\":10,\"text\":\"\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578021156241408', NULL, '910744177604083712', NULL, 'JCommonTable', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"巡检任务数表格\",\"query\":[],\"h\":42,\"dataSetApi\":\"https://api.jeecg.com/mock/51/propertyFireFighting?type=inspectionTasksTable\",\"drillData\":[],\"timeOut\":-1,\"chartData\":\"[{\\\"name\\\":\\\"4月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"2月\\\",\\\"value\\\":200},{\\\"name\\\":\\\"3月\\\",\\\"value\\\":300},{\\\"name\\\":\\\"4月\\\",\\\"value\\\":400},{\\\"name\\\":\\\"5月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"6月\\\",\\\"value\\\":120}]\",\"size\":{\"width\":599,\"height\":353},\"dataSetId\":\"910797586147360768\",\"fieldOption\":[{\"label\":\"检查时间\",\"text\":\"检查时间\",\"value\":\"inspectTime\"},{\"label\":\"设备名称\",\"text\":\"设备名称\",\"value\":\"deviceName\"},{\"label\":\"设备地址\",\"text\":\"设备地址\",\"value\":\"deviceAddress\"},{\"label\":\"检查人\",\"text\":\"检查人\",\"value\":\"inspected\"},{\"label\":\"检查内容\",\"text\":\"检查内容\",\"value\":\"content\"},{\"label\":\"巡查结果\",\"text\":\"巡查结果\",\"value\":\"inspectResult\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":24,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"columns\":[{\"izShow\":\"Y\",\"dataIndex\":\"inspectTime\",\"title\":\"检查时间\"},{\"izShow\":\"Y\",\"dataIndex\":\"deviceName\",\"title\":\"设备名称\"},{\"izShow\":\"Y\",\"dataIndex\":\"deviceAddress\",\"title\":\"设备地址\"},{\"izShow\":\"Y\",\"dataIndex\":\"inspected\",\"title\":\"检查人\"},{\"izShow\":\"Y\",\"dataIndex\":\"content\",\"title\":\"检查内容\"},{\"izShow\":\"Y\",\"dataIndex\":\"inspectResult\",\"title\":\"巡查结果\"}]}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912578021181407232', NULL, '910744177604083712', NULL, 'JText', '{\"chartData\":\"巡检任务数\",\"borderColor\":\"#DB771F\",\"size\":{\"width\":1208,\"height\":67},\"background\":\"#DB771F\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"textAlign\":\"center\",\"fontSize\":22,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":12,\"marginLeft\":0},\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:46:05', NULL, NULL);

-- 某连锁饮品销售看板
UPDATE onl_drag_page SET  template = '[{\"component\":\"JText\",\"pcX\":0,\"w\":15,\"moved\":false,\"pcY\":0,\"x\":0,\"h\":10,\"i\":\"dcd6e80e-8865-4238-844b-2bb8998f755e\",\"y\":0,\"orderNum\":0,\"pageCompId\":\"912577878868672512\"},{\"component\":\"JForm\",\"pcX\":15,\"w\":9,\"moved\":false,\"pcY\":0,\"x\":15,\"h\":10,\"i\":\"50040bd1-c081-4e5b-a36a-ea6a5afce396\",\"y\":0,\"orderNum\":10,\"pageCompId\":\"912577878898032640\"},{\"component\":\"JGrowCard\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":10,\"x\":0,\"h\":30,\"i\":\"0efe3637-39ac-4f2e-82f4-e09540ee332a\",\"y\":10,\"orderNum\":10,\"pageCompId\":\"912577878919004160\"},{\"component\":\"JNumber\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":40,\"x\":0,\"h\":17,\"i\":\"2db8c9de-9f8b-49f8-8d7a-6b3166de0ed5\",\"y\":40,\"orderNum\":40,\"pageCompId\":\"912577878944169984\"},{\"component\":\"JNumber\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":57,\"x\":0,\"h\":16,\"i\":\"6f773617-eca8-4598-8260-dc701cc16e8b\",\"y\":57,\"orderNum\":50,\"pageCompId\":\"912577878965141504\"},{\"component\":\"JNumber\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":73,\"x\":0,\"h\":16,\"i\":\"868c63d3-cfc2-4e49-a1c5-6ee0418fd303\",\"y\":73,\"orderNum\":59,\"pageCompId\":\"912577878990307328\"},{\"component\":\"JNumber\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":89,\"x\":0,\"h\":17,\"i\":\"35a7308b-0482-48c8-8e46-ed50cfb67961\",\"y\":89,\"orderNum\":83,\"pageCompId\":\"912577879023861760\"},{\"component\":\"JLine\",\"pcX\":5,\"w\":8,\"moved\":false,\"pcY\":40,\"x\":5,\"h\":23,\"i\":\"2323a8fb-42ff-4a23-9b13-8f4f514dafb5\",\"y\":40,\"orderNum\":96,\"pageCompId\":\"912577879044833280\"},{\"component\":\"JLine\",\"pcX\":5,\"w\":8,\"moved\":false,\"pcY\":63,\"x\":5,\"h\":22,\"i\":\"715ebf76-9228-4b2e-8ab7-dcb351c2ff03\",\"y\":63,\"orderNum\":96,\"pageCompId\":\"912577879065804800\"},{\"component\":\"JLine\",\"pcX\":5,\"w\":8,\"moved\":false,\"pcY\":85,\"x\":5,\"h\":21,\"i\":\"cd981a55-258c-48cd-8e92-742185a711a3\",\"y\":85,\"orderNum\":96,\"pageCompId\":\"912577879086776320\"},{\"component\":\"JBar\",\"pcX\":13,\"w\":11,\"moved\":false,\"pcY\":40,\"x\":13,\"h\":33,\"i\":\"82016a70-aa98-42cb-b205-c14e5e212cf8\",\"y\":40,\"orderNum\":106,\"pageCompId\":\"912577879103553536\"},{\"component\":\"JCommonTable\",\"pcX\":13,\"w\":6,\"moved\":false,\"pcY\":73,\"x\":13,\"h\":33,\"i\":\"0b68bd42-a0e9-48f8-9a7b-794ba9ebf8cf\",\"y\":73,\"orderNum\":106,\"pageCompId\":\"912577879116136448\"},{\"component\":\"JRing\",\"pcX\":19,\"w\":5,\"moved\":false,\"pcY\":73,\"x\":19,\"h\":33,\"i\":\"5130130f-6076-45b1-8977-d9b5a4c894fc\",\"y\":73,\"orderNum\":106,\"pageCompId\":\"912577879132913664\"}]', protection_code = 'amVlY2cxMzE0' WHERE id = '910820508471705600';
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577878868672512', NULL, '910820508471705600', NULL, 'JText', '{\"chartData\":\"某 连 锁 饮 品 销 售\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":1132,\"height\":100},\"background\":\"#FFFFFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#000000\",\"textAlign\":\"center\",\"fontSize\":40,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":18,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-31 15:45:31', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577878898032640', NULL, '910820508471705600', NULL, 'JForm', '{\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":675,\"height\":100},\"background\":\"#FFFFFF\",\"w\":24,\"dataType\":1,\"h\":12,\"linkageConfig\":[{\"linkageId\":\"0efe3637-39ac-4f2e-82f4-e09540ee332a\",\"linkage\":[]},{\"linkageId\":\"2db8c9de-9f8b-49f8-8d7a-6b3166de0ed5\",\"linkage\":[]},{\"linkageId\":\"6f773617-eca8-4598-8260-dc701cc16e8b\",\"linkage\":[]},{\"linkageId\":\"868c63d3-cfc2-4e49-a1c5-6ee0418fd303\",\"linkage\":[]},{\"linkageId\":\"35a7308b-0482-48c8-8e46-ed50cfb67961\",\"linkage\":[]},{\"linkageId\":\"cd981a55-258c-48cd-8e92-742185a711a3\",\"linkage\":[]},{\"linkageId\":\"715ebf76-9228-4b2e-8ab7-dcb351c2ff03\",\"linkage\":[]},{\"linkageId\":\"2323a8fb-42ff-4a23-9b13-8f4f514dafb5\",\"linkage\":[]},{\"linkageId\":\"0b68bd42-a0e9-48f8-9a7b-794ba9ebf8cf\",\"linkage\":[]},{\"linkageId\":\"5130130f-6076-45b1-8977-d9b5a4c894fc\",\"linkage\":[]},{\"linkageId\":\"82016a70-aa98-42cb-b205-c14e5e212cf8\",\"linkage\":[]}],\"timeOut\":-1,\"option\":{\"fields\":[{\"fieldName\":\"date\",\"dictCode\":\"\",\"dateFormat\":\"YYYY-MM\",\"fieldTxt\":\"日期\",\"defaultValue\":\"2024-01\",\"searchMode\":\"single\",\"orderNum\":\"\",\"action\":\"\",\"id\":\"rowa82899dc-1e6b-47a1-acd8-ba2fb6f5df02\",\"izSearch\":\"1\",\"widgetType\":\"date\"}],\"body\":{\"color\":\"#000000\"},\"title\":{\"textStyle\":{\"color\":\"#464646\"}}}}', 'admin', '2024-01-31 15:45:31', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577878919004160', NULL, '910820508471705600', NULL, 'JGrowCard', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"标题\"},{\"mapping\":\"\",\"filed\":\"图标\"},{\"mapping\":\"value\",\"filed\":\"数值\"},{\"mapping\":\"\",\"filed\":\"总计\"},{\"mapping\":\"\",\"filed\":\"前缀\"},{\"mapping\":\"color\",\"filed\":\"颜色\"},{\"mapping\":\"action\",\"filed\":\"单位\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"销量额\",\"query\":[],\"h\":19,\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=salesVolume\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"chartData\":\"[  {    \\\"title\\\": \\\"访问数\\\",    \\\"icon\\\": \\\"icon-jeecg-qianbao\\\",    \\\"value\\\": 2000,    \\\"total\\\": 120000,    \\\"prefix\\\": \\\"$\\\",    \\\"color\\\": \\\"green\\\",    \\\"action\\\": \\\"月\\\"  },  {    \\\"title\\\": \\\"成交额\\\",    \\\"icon\\\": \\\"icon-jeecg-youhuiquan\\\",    \\\"value\\\": 20000,    \\\"total\\\": 500000,    \\\"prefix\\\": \\\"$\\\",    \\\"color\\\": \\\"blue\\\",    \\\"action\\\": \\\"月\\\"  },  {    \\\"title\\\": \\\"下载数\\\",    \\\"icon\\\": \\\"icon-jeecg-tupian\\\",    \\\"value\\\": 8000,    \\\"prefix\\\": \\\"$\\\",    \\\"total\\\": 120000,    \\\"color\\\": \\\"orange\\\",    \\\"action\\\": \\\"周\\\"  },  {    \\\"title\\\": \\\"成交数\\\",    \\\"icon\\\": \\\"icon-jeecg-jifen\\\",    \\\"value\\\": 5000,    \\\"prefix\\\": \\\"$\\\",    \\\"total\\\": 50000,    \\\"color\\\": \\\"purple\\\",    \\\"action\\\": \\\"年\\\"  }]\",\"size\":{\"width\":1817,\"height\":320},\"dataSetId\":\"911792028287287296\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"},{\"label\":\"icon\",\"text\":\"icon\",\"value\":\"icon\"},{\"label\":\"action\",\"text\":\"action\",\"value\":\"action\"},{\"label\":\"color\",\"text\":\"color\",\"value\":\"color\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"w\":12,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"icon\":{\"scriptUrl\":\"//at.alicdn.com/t/font_3237315_b3fqd960glt.js\",\"fontSize\":20},\"body\":{\"horizontal\":6,\"vertical\":4,\"span\":8},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"更多\",\"title\":\"统计卡片\"}}}', 'admin', '2024-01-31 15:45:31', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577878944169984', NULL, '910820508471705600', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"单月最高销售量分店\\t\",\"query\":[],\"h\":9,\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=topSellingBranch\",\"drillData\":[],\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{\\\"value\\\":\\\"1024\\\"}\",\"size\":{\"width\":371,\"height\":177},\"dataSetId\":\"911836343134896128\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#F3EFF0\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#F9B632\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#FF9736\",\"textStyle\":{\"color\":\"#FFFFFF\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"单月最高销量分店（新洲店）\"}}}', 'admin', '2024-01-31 15:45:31', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577878965141504', NULL, '910820508471705600', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"单月最高销量品线\\t\",\"query\":[],\"h\":9,\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=salesLine\",\"drillData\":[],\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{\\\"value\\\":\\\"1024\\\"}\",\"size\":{\"width\":371,\"height\":166},\"dataSetId\":\"911836426450550784\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#E9F2FF\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#547BFE\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#547BFE\",\"textStyle\":{\"color\":\"#FFFFFF\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"单月最高销量品线（醇香奶茶）\"}}}', 'admin', '2024-01-31 15:45:31', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577878990307328', NULL, '910820508471705600', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"单月最高销量单品\",\"query\":[],\"h\":9,\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=highestSellingItem\",\"drillData\":[],\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{\\\"value\\\":\\\"1024\\\"}\",\"size\":{\"width\":371,\"height\":166},\"dataSetId\":\"911836535531814912\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#EDFAFE\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#44C8F3\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#44C8F3\",\"textStyle\":{\"color\":\"#FFFFFF\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"单月最高销量单品（珍珠奶茶）\"}}}', 'admin', '2024-01-31 15:45:31', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577879023861760', NULL, '910820508471705600', NULL, 'JNumber', '{\"borderColor\":\"#FFFFFF00\",\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"单月最高销量规格\",\"query\":[],\"h\":9,\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=maximumSalesSpecs\",\"drillData\":[],\"analysis\":{\"isCompare\":false,\"compareType\":\"\",\"trendType\":\"1\"},\"timeOut\":0,\"chartData\":\"{\\\"value\\\":\\\"1024\\\"}\",\"size\":{\"width\":371,\"height\":177},\"dataSetId\":\"911836717715603456\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#F2FCF7\",\"seriesType\":[],\"w\":5,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"isCompare\":false,\"trendType\":\"1\",\"body\":{\"color\":\"#58DC92\",\"text\":\"\",\"fontWeight\":\"bold\"},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#9EFFB6\",\"textStyle\":{\"color\":\"#FFFFFF\",\"fontSize\":18,\"fontWeight\":\"bold\"},\"title\":\"单月最高销量规格（磨砂）\"}}}', 'admin', '2024-01-31 15:45:31', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577879044833280', NULL, '910820508471705600', NULL, 'JLine', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"销售额走势\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=salesTrend\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1000,\\\"name\\\":\\\"联想\\\"},{\\\"value\\\":7350,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":5800,\\\"name\\\":\\\"华为\\\"},{\\\"value\\\":6000,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":3000,\\\"name\\\":\\\"戴尔\\\"}]\",\"size\":{\"width\":599,\"height\":243},\"dataSetId\":\"911836839023263744\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"},\"show\":false},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"},\"show\":true},\"nameTextStyle\":{\"color\":\"#333333\"}},\"customColor\":[{\"color\":\"#FBE1C2\"}],\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#FFFFFF\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":178,\"bottom\":157,\"show\":false},\"series\":[{\"data\":[],\"lineType\":\"area\",\"itemStyle\":{\"color\":\"#64b5f6\"},\"type\":\"line\"}],\"title\":{\"subtext\":\"销售金额（元）\",\"left\":10,\"text\":\"销售额走势\",\"subtextStyle\":{\"color\":\"#464646\"},\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"small\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-31 15:45:31', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577879065804800', NULL, '910820508471705600', NULL, 'JLine', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"销量走势\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=salesTendency\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1000,\\\"name\\\":\\\"联想\\\"},{\\\"value\\\":7350,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":5800,\\\"name\\\":\\\"华为\\\"},{\\\"value\\\":6000,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":3000,\\\"name\\\":\\\"戴尔\\\"}]\",\"size\":{\"width\":599,\"height\":232},\"dataSetId\":\"911836922162757632\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"customColor\":[{\"color\":\"#D7EBFB\"}],\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":71,\"bottom\":62,\"show\":false},\"series\":[{\"data\":[],\"lineType\":\"area\",\"itemStyle\":{\"color\":\"#64b5f6\"},\"type\":\"line\"}],\"tooltip\":{\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"销售数量（杯）\",\"left\":1,\"text\":\"销量走势\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\",\"fontWeight\":\"normal\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-31 15:45:31', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577879086776320', NULL, '910820508471705600', NULL, 'JLine', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"原料支出趋势\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=expenditureTrends\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1000,\\\"name\\\":\\\"联想\\\"},{\\\"value\\\":7350,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":5800,\\\"name\\\":\\\"华为\\\"},{\\\"value\\\":6000,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":3000,\\\"name\\\":\\\"戴尔\\\"}]\",\"size\":{\"width\":599,\"height\":221},\"dataSetId\":\"911845934736392192\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"customColor\":[{\"color\":\"#D5DFFE\"}],\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":54,\"bottom\":61,\"show\":false},\"series\":[{\"data\":[],\"lineType\":\"area\",\"itemStyle\":{\"color\":\"#64b5f6\"},\"type\":\"line\"}],\"tooltip\":{\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"支出费用（元）\",\"left\":10,\"text\":\"原料支出趋势\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-31 15:45:31', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577879103553536', NULL, '910820508471705600', NULL, 'JBar', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"订单销售量\\t\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=orderSalesVolume\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":827,\"height\":353},\"dataSetId\":\"911837008343121920\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"},\"interval\":2},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"type\":\"value\",\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":48,\"left\":85,\"bottom\":60,\"show\":false},\"series\":[{\"barWidth\":0,\"data\":[],\"showBackground\":false,\"backgroundStyle\":{\"color\":\"#51626E\"},\"itemStyle\":{\"color\":\"#85C6F3\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"订单销售量\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-31 15:45:31', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577879116136448', NULL, '910820508471705600', NULL, 'JCommonTable', '{\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"店铺销售额排名\",\"query\":[],\"h\":42,\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=salesRanking\",\"drillData\":[],\"timeOut\":-1,\"chartData\":\"[{\\\"name\\\":\\\"4月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"2月\\\",\\\"value\\\":200},{\\\"name\\\":\\\"3月\\\",\\\"value\\\":300},{\\\"name\\\":\\\"4月\\\",\\\"value\\\":400},{\\\"name\\\":\\\"5月\\\",\\\"value\\\":50},{\\\"name\\\":\\\"6月\\\",\\\"value\\\":120}]\",\"size\":{\"width\":447,\"height\":353},\"dataSetId\":\"911837091507781632\",\"fieldOption\":[{\"label\":\"店铺\",\"text\":\"店铺\",\"value\":\"name\"},{\"label\":\"销售金额（元）\",\"text\":\"销售金额（元）\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"seriesType\":[],\"w\":24,\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"columns\":[{\"izShow\":\"Y\",\"dataIndex\":\"name\",\"title\":\"店铺\"},{\"izShow\":\"Y\",\"dataIndex\":\"value\",\"title\":\"销售金额（元）\"}],\"title\":{\"textStyle\":{\"color\":\"#464646\"}}}}', 'admin', '2024-01-31 15:45:31', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912577879132913664', NULL, '910820508471705600', NULL, 'JRing', '{\"borderColor\":\"#FFFFFF00\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\" 冷热占比\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/beverageSales?type=coldAndHostProportion\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":371,\"height\":353},\"dataSetId\":\"911837168896884736\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#FFFFFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"avoidLabelOverlap\":false,\"emphasis\":{\"label\":{\"show\":true,\"fontSize\":\"25\",\"fontWeight\":\"bold\"}},\"label\":{\"show\":false,\"position\":\"center\"},\"labelLine\":{\"show\":false},\"type\":\"pie\",\"radius\":[\"40%\",\"70%\"]}],\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"top\":1,\"show\":true,\"text\":\"冷热占比\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 15:45:31', NULL, NULL);

-- 产品销售数据
UPDATE onl_drag_page SET template = '[{\"component\":\"JText\",\"pcX\":0,\"w\":24,\"moved\":false,\"pcY\":0,\"x\":0,\"h\":8,\"i\":\"8faf5811-d619-4ff2-839e-86ecb4e0328f\",\"y\":0,\"orderNum\":0,\"pageCompId\":\"912581733916393472\"},{\"component\":\"JRing\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":8,\"x\":0,\"h\":30,\"i\":\"b5fa4269-1d38-4a62-80a3-52490943ff59\",\"y\":8,\"orderNum\":38,\"pageCompId\":\"912581733949947904\"},{\"component\":\"JBar\",\"pcX\":0,\"w\":5,\"moved\":false,\"pcY\":38,\"x\":0,\"h\":41,\"i\":\"de23faa0-ffa7-482c-857e-8dbfaea7dd47\",\"y\":38,\"orderNum\":38,\"pageCompId\":\"912581733975113728\"},{\"component\":\"JText\",\"pcX\":5,\"w\":4,\"moved\":false,\"pcY\":8,\"x\":5,\"h\":5,\"i\":\"f2158c50-22b0-4978-98b9-6e2cc784650e\",\"y\":8,\"orderNum\":84,\"pageCompId\":\"912581734000279552\"},{\"component\":\"JText\",\"pcX\":5,\"w\":4,\"moved\":false,\"pcY\":13,\"x\":5,\"h\":5,\"i\":\"a28c1804-cb6b-4c70-8e57-ce352ff09d63\",\"y\":13,\"orderNum\":84,\"pageCompId\":\"912581734025445376\"},{\"component\":\"JText\",\"pcX\":5,\"w\":4,\"moved\":false,\"pcY\":18,\"x\":5,\"h\":5,\"i\":\"63506dd7-e376-42c5-b39f-b2211ad8ebdc\",\"y\":18,\"orderNum\":84,\"pageCompId\":\"912581734050611200\"},{\"component\":\"JText\",\"pcX\":9,\"w\":4,\"moved\":false,\"pcY\":8,\"x\":9,\"h\":5,\"i\":\"de7e9a36-1234-4792-835a-c20687c433ed\",\"y\":8,\"orderNum\":84,\"pageCompId\":\"912581734084165632\"},{\"component\":\"JText\",\"pcX\":9,\"w\":4,\"moved\":false,\"pcY\":13,\"x\":9,\"h\":5,\"i\":\"ee6464c3-bda1-424a-8c0f-37fdfce6d8cb\",\"y\":13,\"orderNum\":84,\"pageCompId\":\"912581734113525760\"},{\"component\":\"JText\",\"pcX\":9,\"w\":4,\"moved\":false,\"pcY\":18,\"x\":9,\"h\":5,\"i\":\"c6de77fa-b17a-4549-bda8-0e00e65a7748\",\"y\":18,\"orderNum\":84,\"pageCompId\":\"912581734138691584\"},{\"component\":\"JText\",\"pcX\":13,\"w\":4,\"moved\":false,\"pcY\":8,\"x\":13,\"h\":5,\"i\":\"3dc483bb-0067-48ee-98bd-dfa2e7534909\",\"y\":8,\"orderNum\":84,\"pageCompId\":\"912581734163857408\"},{\"component\":\"JText\",\"pcX\":13,\"w\":4,\"moved\":false,\"pcY\":13,\"x\":13,\"h\":10,\"i\":\"ecd623a6-2e08-47f2-8dd6-c25ff5c29a66\",\"y\":13,\"orderNum\":84,\"pageCompId\":\"912581734189023232\"},{\"component\":\"JLine\",\"pcX\":5,\"w\":12,\"moved\":false,\"pcY\":23,\"x\":5,\"h\":27,\"i\":\"b910f04d-6a4d-4d79-811d-dc5d2f807713\",\"y\":23,\"orderNum\":83,\"pageCompId\":\"912581734209994752\"},{\"component\":\"JStackBar\",\"pcX\":5,\"w\":12,\"moved\":false,\"pcY\":50,\"x\":5,\"h\":29,\"i\":\"406be0c9-dc13-4cad-ac22-17a4a5c22bff\",\"y\":50,\"orderNum\":83,\"pageCompId\":\"912581734230966272\"},{\"component\":\"JRing\",\"pcX\":17,\"w\":7,\"moved\":false,\"pcY\":8,\"x\":17,\"h\":32,\"i\":\"76d16482-fd7f-4e92-ba27-68980b8b0029\",\"y\":8,\"orderNum\":83,\"pageCompId\":\"912581734256132096\"},{\"component\":\"JText\",\"pcX\":17,\"w\":7,\"moved\":false,\"pcY\":40,\"x\":17,\"h\":5,\"i\":\"211ebf49-1284-4b1c-99c9-08066a50f9ae\",\"y\":40,\"orderNum\":82,\"pageCompId\":\"912581734272909312\"},{\"component\":\"JBar\",\"pcX\":17,\"w\":7,\"moved\":false,\"pcY\":45,\"x\":17,\"h\":34,\"i\":\"c89556db-678a-418b-8121-1052958f4149\",\"y\":45,\"orderNum\":115,\"pageCompId\":\"912581734289686528\"}]', protection_code = 'amVlY2cxMzE0' WHERE id = '911856216581914624';
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581733916393472', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"产品销售数据仪表板\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":1817,\"height\":78},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4570F2\",\"textAlign\":\"center\",\"fontSize\":36,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":8,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581733949947904', NULL, '911856216581914624', NULL, 'JRing', '{\"borderColor\":\"#DBEAFF\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"本月渠道销售\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=salesThisMonth\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":371,\"height\":320},\"dataSetId\":\"911887596497977344\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"top\":53,\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"avoidLabelOverlap\":false,\"emphasis\":{\"label\":{\"show\":true,\"fontSize\":\"25\",\"fontWeight\":\"bold\"}},\"label\":{\"show\":false,\"position\":\"center\"},\"labelLine\":{\"show\":false},\"type\":\"pie\",\"radius\":[\"40%\",\"70%\"]}],\"legend\":{\"t\":1,\"orient\":\"vertical\"},\"tooltip\":{\"trigger\":\"item\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\" 华东区、华中区为主要渠道销售区域。\",\"top\":1,\"show\":true,\"text\":\"本月渠道销售\",\"subtextStyle\":{\"color\":\"#464646\"},\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581733975113728', NULL, '911856216581914624', NULL, 'JBar', '{\"borderColor\":\"#DBEAFF\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"本月代理商销售排行\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=salesRanking\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":371,\"height\":441},\"dataSetId\":\"911887691259887616\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"interval\":2}},\"xAxis\":{\"axisLabel\":{\"rotate\":3,\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"type\":\"value\",\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":69,\"left\":94,\"bottom\":58,\"show\":false},\"series\":[{\"barWidth\":17,\"data\":[],\"itemStyle\":{\"color\":\"#64B5F6\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"show\":true,\"text\":\"本月代理商销售排行/元\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581734000279552', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"本月销售额/元\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#000000\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":21},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581734025445376', NULL, '911856216581914624', NULL, 'JText', '{\"borderColor\":\"#DBEAFF\",\"paramOption\":[],\"dataType\":1,\"dataSetName\":\"本月产品销售\",\"query\":[],\"h\":12,\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=productSalesThisMonth\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"chartData\":\"15,458,757\",\"size\":{\"width\":295,\"height\":45},\"dataSetId\":\"911889006916583424\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"w\":8,\"turnConfig\":{\"url\":\"\"},\"dataSetIzAgent\":\"0\",\"option\":{\"body\":{\"color\":\"#4D71FE\",\"fontSize\":30,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":0,\"marginLeft\":16},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581734050611200', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"月环比 -50%\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#34C874\",\"fontSize\":14,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":21},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581734084165632', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"今日销售额/元\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#000000\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":18},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581734113525760', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"657,554\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4D71FE\",\"fontSize\":30,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":0,\"marginLeft\":19},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581734138691584', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"日环比 -12%\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#34C874\",\"fontSize\":14,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":24},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581734163857408', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"今年目标销售额达成率\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#000000\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":16},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581734189023232', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"42%\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":295,\"height\":100},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4D71FE\",\"textAlign\":\"center\",\"fontSize\":30,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":24,\"marginLeft\":0},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581734209994752', NULL, '911856216581914624', NULL, 'JLine', '{\"borderColor\":\"#DBEAFF\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"近七天销售额趋势\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=salesTrend\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1000,\\\"name\\\":\\\"联想\\\"},{\\\"value\\\":7350,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":5800,\\\"name\\\":\\\"华为\\\"},{\\\"value\\\":6000,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":3000,\\\"name\\\":\\\"戴尔\\\"}]\",\"size\":{\"width\":904,\"height\":287},\"dataSetId\":\"911888632709169152\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"top\":90,\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"itemStyle\":{\"color\":\"#64b5f6\"},\"type\":\"line\"}],\"title\":{\"subtext\":\"日销售额（元）\",\"left\":10,\"text\":\"近七天销售额趋势\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\",\"fontWeight\":\"bold\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581734230966272', NULL, '911856216581914624', NULL, 'JStackBar', '{\"borderColor\":\"#DBEAFF\",\"dataMapping\":[{\"mapping\":\"type\",\"filed\":\"分组\"},{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"本月重点城市的重点产品销售额\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=productSalesRevenue\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"1991\\\",\\\"value\\\":3,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1992\\\",\\\"value\\\":4,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1993\\\",\\\"value\\\":3.5,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1994\\\",\\\"value\\\":5,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1995\\\",\\\"value\\\":4.9,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1996\\\",\\\"value\\\":6,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1997\\\",\\\"value\\\":7,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1998\\\",\\\"value\\\":9,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1999\\\",\\\"value\\\":13,\\\"type\\\":\\\"Lon\\\"},{\\\"name\\\":\\\"1991\\\",\\\"value\\\":3,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1992\\\",\\\"value\\\":4,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1993\\\",\\\"value\\\":3.5,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1994\\\",\\\"value\\\":5,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1995\\\",\\\"value\\\":4.9,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1996\\\",\\\"value\\\":6,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1997\\\",\\\"value\\\":7,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1998\\\",\\\"value\\\":9,\\\"type\\\":\\\"Bor\\\"},{\\\"name\\\":\\\"1999\\\",\\\"value\\\":13,\\\"type\\\":\\\"Bor\\\"}]\",\"size\":{\"width\":904,\"height\":309},\"dataSetId\":\"911888715248877568\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"},{\"label\":\"type\",\"text\":\"type\",\"value\":\"type\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"customColor\":[{\"color\":\"#67BBFF\"},{\"color\":\"#9982FD\"},{\"color\":\"#62DDCC\"}],\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":90,\"bottom\":115},\"series\":[],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"show\":true,\"text\":\"本月重点城市的重点产品销售额/元\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":16}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581734256132096', NULL, '911856216581914624', NULL, 'JRing', '{\"borderColor\":\"#DBEAFF\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"本月产品销售\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=productSalesThisMonth\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"value\\\":1048,\\\"name\\\":\\\"oppo\\\"},{\\\"value\\\":735,\\\"name\\\":\\\"vivo\\\"},{\\\"value\\\":580,\\\"name\\\":\\\"苹果\\\"},{\\\"value\\\":484,\\\"name\\\":\\\"小米\\\"},{\\\"value\\\":300,\\\"name\\\":\\\"三星\\\"}]\",\"size\":{\"width\":523,\"height\":342},\"dataSetId\":\"911889006916583424\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"grid\":{\"top\":55,\"left\":47,\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"avoidLabelOverlap\":false,\"emphasis\":{\"label\":{\"show\":true,\"fontSize\":\"25\",\"fontWeight\":\"bold\"}},\"itemStyle\":{\"color\":\"#64B5F6\"},\"label\":{\"show\":false,\"position\":\"center\"},\"labelLine\":{\"show\":false},\"type\":\"pie\",\"radius\":[\"40%\",\"70%\"]}],\"legend\":{\"t\":10},\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"subtext\":\"\",\"show\":true,\"text\":\" 产品3为主推产品产品2为近期上线产品。\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":14,\"fontWeight\":\"bold\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581734272909312', NULL, '911856216581914624', NULL, 'JText', '{\"chartData\":\"本月产品销售\",\"borderColor\":\"#DBEAFF\",\"size\":{\"width\":523,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#000000\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":8,\"marginLeft\":21},\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
INSERT INTO onl_drag_page_comp (id, parent_id, page_Id, comp_id, component, config, create_by, create_time, update_by, update_time) VALUES ('912581734289686528', NULL, '911856216581914624', NULL, 'JBar', '{\"borderColor\":\"#DBEAFF\",\"dataMapping\":[{\"mapping\":\"name\",\"filed\":\"维度\"},{\"mapping\":\"value\",\"filed\":\"数值\"}],\"paramOption\":[],\"dataType\":2,\"dataSetName\":\"本月产品小类销售排行\",\"query\":[],\"dataSetApi\":\"https://api.jeecg.com/mock/51/productSales?type=rankingCategorySales\",\"drillData\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"chartData\":\"[{\\\"name\\\":\\\"苹果\\\",\\\"value\\\":1000879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"三星\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"小米\\\",\\\"value\\\":2300879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"oppo\\\",\\\"value\\\":5400879,\\\"type\\\":\\\"手机品牌\\\"},{\\\"name\\\":\\\"vivo\\\",\\\"value\\\":3400879,\\\"type\\\":\\\"手机品牌\\\"}]\",\"size\":{\"width\":523,\"height\":364},\"dataSetId\":\"911889086163763200\",\"fieldOption\":[{\"label\":\"name\",\"text\":\"name\",\"value\":\"name\"},{\"label\":\"value\",\"text\":\"value\",\"value\":\"value\"}],\"dataSetType\":\"api\",\"background\":\"#DBEAFF\",\"seriesType\":[],\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"dataSetIzAgent\":\"0\",\"option\":{\"yAxis\":{\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"interval\":2}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"type\":\"value\",\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":44,\"bottom\":62,\"show\":false},\"series\":[{\"barWidth\":16,\"data\":[],\"itemStyle\":{\"color\":\"#64B5F6\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"subtext\":\"\",\"show\":true,\"text\":\" 本月产品小类销售排行/元\",\"textStyle\":{\"color\":\"#464646\",\"fontSize\":14},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-01-31 16:00:50', NULL, NULL);
